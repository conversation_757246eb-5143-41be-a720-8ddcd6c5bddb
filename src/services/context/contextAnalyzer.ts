import * as vscode from 'vscode'
import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { TypeScriptAnalyzer } from '../analysis/typescriptAnalyzer'
import { PythonAnalyzer } from '../analysis/pythonAnalyzer'

export interface ContextInfo {
  currentFile: FileContext
  relatedFiles: RelatedFileContext[]
  workspaceContext: WorkspaceContext
  cursorContext: CursorContext
  selectionContext?: SelectionContext
  projectStructure: ProjectStructureContext
  recentActivity: RecentActivityContext
  relevanceScores: Map<string, number>
}

export interface FileContext {
  path: string
  relativePath: string
  name: string
  language: string
  size: number
  modifiedTime: Date
  content?: string
  symbols: SymbolContext[]
  imports: string[]
  exports: string[]
  dependencies: string[]
  dependents: string[]
  isMainFile: boolean
  isTestFile: boolean
  isConfigFile: boolean
}

export interface RelatedFileContext {
  path: string
  relativePath: string
  language: string
  relationshipType: 'dependency' | 'dependent' | 'sibling' | 'related' | 'test'
  relevanceScore: number
  reason: string
  symbols?: SymbolContext[]
}

export interface SymbolContext {
  name: string
  type: string
  line: number
  column: number
  scope: string
  signature?: string
  documentation?: string
  isExported: boolean
  complexity: number
  references: number
}

export interface WorkspaceContext {
  rootPath: string
  name: string
  folders: string[]
  totalFiles: number
  languages: string[]
  frameworks: string[]
  hasGit: boolean
  hasTesting: boolean
  hasLinting: boolean
  packageManagers: string[]
  buildTools: string[]
}

export interface CursorContext {
  line: number
  column: number
  lineText: string
  surroundingLines: string[]
  symbolAtCursor?: SymbolContext
  scopeAtCursor: string
  indentLevel: number
  isInComment: boolean
  isInString: boolean
  isInFunction: boolean
  isInClass: boolean
  nearbySymbols: SymbolContext[]
}

export interface SelectionContext {
  startLine: number
  endLine: number
  startColumn: number
  endColumn: number
  selectedText: string
  containedSymbols: SymbolContext[]
  selectionType: 'partial_line' | 'full_line' | 'multi_line' | 'symbol' | 'expression'
}

export interface ProjectStructureContext {
  directories: DirectoryInfo[]
  patterns: StructurePattern[]
  conventions: NamingConvention[]
  architecture: ArchitectureInfo
}

export interface DirectoryInfo {
  path: string
  name: string
  type: 'source' | 'test' | 'config' | 'build' | 'docs' | 'assets' | 'unknown'
  fileCount: number
  languages: string[]
  depth: number
}

export interface StructurePattern {
  pattern: string
  description: string
  confidence: number
  examples: string[]
}

export interface NamingConvention {
  type: 'camelCase' | 'snake_case' | 'PascalCase' | 'kebab-case' | 'CONSTANT_CASE'
  scope: 'file' | 'variable' | 'function' | 'class' | 'constant'
  confidence: number
  examples: string[]
}

export interface ArchitectureInfo {
  type: 'mvc' | 'mvvm' | 'layered' | 'microservices' | 'monolithic' | 'unknown'
  confidence: number
  layers: string[]
  entryPoints: string[]
  testStrategy: 'unit' | 'integration' | 'e2e' | 'mixed' | 'none'
}

export interface RecentActivityContext {
  recentFiles: string[]
  recentEdits: EditInfo[]
  activeProblems: ProblemInfo[]
  searchHistory: string[]
  commandHistory: string[]
}

export interface EditInfo {
  filePath: string
  timestamp: Date
  changeType: 'create' | 'modify' | 'delete'
  linesChanged: number
  context?: string
}

export interface ProblemInfo {
  filePath: string
  line: number
  severity: 'error' | 'warning' | 'info'
  message: string
  source: string
  code?: string
}

export class ContextAnalyzer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private tsAnalyzer: TypeScriptAnalyzer
  private pythonAnalyzer: PythonAnalyzer

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    tsAnalyzer: TypeScriptAnalyzer,
    pythonAnalyzer: PythonAnalyzer
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.tsAnalyzer = tsAnalyzer
    this.pythonAnalyzer = pythonAnalyzer
  }

  async analyzeContext(
    document: vscode.TextDocument,
    position?: vscode.Position,
    selection?: vscode.Selection
  ): Promise<ContextInfo> {
    const filePath = document.uri.fsPath
    const currentFile = await this.analyzeCurrentFile(document)
    const relatedFiles = await this.findRelatedFiles(filePath)
    const workspaceContext = await this.analyzeWorkspace()
    const cursorContext = position ? await this.analyzeCursorPosition(document, position) : this.getDefaultCursorContext()
    const selectionContext = selection ? await this.analyzeSelection(document, selection) : undefined
    const projectStructure = await this.analyzeProjectStructure()
    const recentActivity = await this.analyzeRecentActivity()
    const relevanceScores = await this.calculateRelevanceScores(filePath, relatedFiles)

    return {
      currentFile,
      relatedFiles,
      workspaceContext,
      cursorContext,
      selectionContext,
      projectStructure,
      recentActivity,
      relevanceScores
    }
  }

  private async analyzeCurrentFile(document: vscode.TextDocument): Promise<FileContext> {
    const filePath = document.uri.fsPath
    const relativePath = vscode.workspace.asRelativePath(filePath)
    const name = this.fileSystem.basename(filePath)
    const language = document.languageId
    const content = document.getText()
    const stats = await this.fileSystem.stat(filePath)

    const symbols = await this.extractSymbols(filePath, language, content)
    const { imports, exports, dependencies, dependents } = await this.getFileDependencies(filePath)

    const fileType = this.determineFileType(relativePath, content)

    return {
      path: filePath,
      relativePath,
      name,
      language,
      size: stats.size,
      modifiedTime: stats.mtime,
      content,
      symbols,
      imports,
      exports,
      dependencies,
      dependents,
      isMainFile: fileType.isMain,
      isTestFile: fileType.isTest,
      isConfigFile: fileType.isConfig
    }
  }

  private async extractSymbols(filePath: string, language: string, content: string): Promise<SymbolContext[]> {
    const symbols: SymbolContext[] = []

    try {
      if (language === 'typescript' || language === 'javascript') {
        const analysis = await this.tsAnalyzer.analyzeContent(content, filePath)
        symbols.push(...analysis.symbols.map(sym => ({
          name: sym.name,
          type: sym.type,
          line: sym.line,
          column: sym.column,
          scope: sym.scope,
          signature: sym.signature,
          documentation: sym.documentation,
          isExported: sym.isExported,
          complexity: 1,
          references: 0
        })))
      } else if (language === 'python') {
        const analysis = this.pythonAnalyzer.analyzeContent(content, filePath)
        symbols.push(...analysis.symbols.map(sym => ({
          name: sym.name,
          type: sym.type,
          line: sym.line,
          column: sym.column,
          scope: sym.scope,
          signature: sym.signature,
          documentation: sym.documentation,
          isExported: !sym.isPrivate,
          complexity: 1,
          references: 0
        })))
      }
    } catch (error) {
      console.error(`Error extracting symbols from ${filePath}:`, error)
    }

    return symbols
  }

  private async getFileDependencies(filePath: string): Promise<{
    imports: string[]
    exports: string[]
    dependencies: string[]
    dependents: string[]
  }> {
    const fileRecord = await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', filePath)
    )

    if (!fileRecord) {
      return { imports: [], exports: [], dependencies: [], dependents: [] }
    }

    const dependencies = await this.db.findMany(
      this.db.query()
        .select('dependency_path')
        .from('dependencies')
        .where('file_id', '=', fileRecord.id)
    )

    const dependents = await this.db.findMany(
      this.db.query()
        .select('f.path')
        .from('dependencies d')
        .innerJoin('files f', 'f.id = d.file_id')
        .where('d.dependency_path', '=', filePath)
    )

    const symbols = await this.db.findMany(
      this.db.query()
        .select('name, type')
        .from('symbols')
        .where('file_id', '=', fileRecord.id)
        .where('is_exported', '=', 1)
    )

    return {
      imports: dependencies.map(d => d.dependency_path),
      exports: symbols.filter(s => s.type !== 'import').map(s => s.name),
      dependencies: dependencies.map(d => d.dependency_path),
      dependents: dependents.map(d => d.path)
    }
  }

  private async findRelatedFiles(filePath: string): Promise<RelatedFileContext[]> {
    const relatedFiles: RelatedFileContext[] = []
    
    // Get files in the same directory
    const siblingFiles = await this.findSiblingFiles(filePath)
    relatedFiles.push(...siblingFiles)

    // Get dependency files
    const dependencyFiles = await this.findDependencyFiles(filePath)
    relatedFiles.push(...dependencyFiles)

    // Get dependent files
    const dependentFiles = await this.findDependentFiles(filePath)
    relatedFiles.push(...dependentFiles)

    // Get test files
    const testFiles = await this.findTestFiles(filePath)
    relatedFiles.push(...testFiles)

    // Remove duplicates and current file
    const uniqueFiles = new Map<string, RelatedFileContext>()
    for (const file of relatedFiles) {
      if (file.path !== filePath && !uniqueFiles.has(file.path)) {
        uniqueFiles.set(file.path, file)
      }
    }

    return Array.from(uniqueFiles.values())
  }

  private async findSiblingFiles(filePath: string): Promise<RelatedFileContext[]> {
    const directory = this.fileSystem.dirname(filePath)
    const siblings: RelatedFileContext[] = []

    try {
      const files = await this.fileSystem.readdir(directory)
      for (const file of files) {
        const siblingPath = this.fileSystem.joinPath(directory, file)
        if (siblingPath !== filePath && await this.fileSystem.isFile(siblingPath)) {
          const relativePath = vscode.workspace.asRelativePath(siblingPath)
          const language = this.detectLanguage(siblingPath)
          
          siblings.push({
            path: siblingPath,
            relativePath,
            language,
            relationshipType: 'sibling',
            relevanceScore: 0.3,
            reason: 'Same directory'
          })
        }
      }
    } catch (error) {
      console.error(`Error finding sibling files for ${filePath}:`, error)
    }

    return siblings
  }

  private async findDependencyFiles(filePath: string): Promise<RelatedFileContext[]> {
    const dependencies: RelatedFileContext[] = []
    
    const fileRecord = await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', filePath)
    )

    if (fileRecord) {
      const deps = await this.db.findMany(
        this.db.query()
          .select('d.dependency_path, f.language')
          .from('dependencies d')
          .leftJoin('files f', 'f.path = d.dependency_path')
          .where('d.file_id', '=', fileRecord.id)
      )

      for (const dep of deps) {
        const relativePath = vscode.workspace.asRelativePath(dep.dependency_path)
        dependencies.push({
          path: dep.dependency_path,
          relativePath,
          language: dep.language || this.detectLanguage(dep.dependency_path),
          relationshipType: 'dependency',
          relevanceScore: 0.7,
          reason: 'Direct dependency'
        })
      }
    }

    return dependencies
  }

  private async findDependentFiles(filePath: string): Promise<RelatedFileContext[]> {
    const dependents: RelatedFileContext[] = []
    
    const deps = await this.db.findMany(
      this.db.query()
        .select('f.path, f.language')
        .from('dependencies d')
        .innerJoin('files f', 'f.id = d.file_id')
        .where('d.dependency_path', '=', filePath)
    )

    for (const dep of deps) {
      const relativePath = vscode.workspace.asRelativePath(dep.path)
      dependents.push({
        path: dep.path,
        relativePath,
        language: dep.language || this.detectLanguage(dep.path),
        relationshipType: 'dependent',
        relevanceScore: 0.6,
        reason: 'Depends on this file'
      })
    }

    return dependents
  }

  private async findTestFiles(filePath: string): Promise<RelatedFileContext[]> {
    const testFiles: RelatedFileContext[] = []
    const baseName = this.fileSystem.basename(filePath, this.fileSystem.extname(filePath))
    const directory = this.fileSystem.dirname(filePath)

    // Common test file patterns
    const testPatterns = [
      `${baseName}.test.*`,
      `${baseName}.spec.*`,
      `test_${baseName}.*`,
      `${baseName}_test.*`
    ]

    // Search in common test directories
    const testDirectories = [
      this.fileSystem.joinPath(directory, '__tests__'),
      this.fileSystem.joinPath(directory, 'tests'),
      this.fileSystem.joinPath(directory, 'test'),
      this.fileSystem.joinPath(vscode.workspace.rootPath || '', 'tests'),
      this.fileSystem.joinPath(vscode.workspace.rootPath || '', 'test')
    ]

    for (const testDir of testDirectories) {
      if (await this.fileSystem.exists(testDir)) {
        try {
          const files = await this.fileSystem.readdir(testDir)
          for (const file of files) {
            const testFilePath = this.fileSystem.joinPath(testDir, file)
            if (this.matchesTestPattern(file, baseName)) {
              const relativePath = vscode.workspace.asRelativePath(testFilePath)
              const language = this.detectLanguage(testFilePath)
              
              testFiles.push({
                path: testFilePath,
                relativePath,
                language,
                relationshipType: 'test',
                relevanceScore: 0.8,
                reason: 'Test file for this module'
              })
            }
          }
        } catch (error) {
          // Ignore errors reading test directories
        }
      }
    }

    return testFiles
  }

  private matchesTestPattern(fileName: string, baseName: string): boolean {
    const patterns = [
      new RegExp(`${baseName}\\.test\\.`),
      new RegExp(`${baseName}\\.spec\\.`),
      new RegExp(`test_${baseName}\\.`),
      new RegExp(`${baseName}_test\\.`)
    ]

    return patterns.some(pattern => pattern.test(fileName))
  }

  private async analyzeWorkspace(): Promise<WorkspaceContext> {
    const workspace = vscode.workspace
    const rootPath = workspace.rootPath || ''
    const name = this.fileSystem.basename(rootPath)
    const folders = workspace.workspaceFolders?.map(f => f.uri.fsPath) || []

    const stats = await this.getWorkspaceStats()
    const frameworks = await this.detectFrameworks()
    const tools = await this.detectTools()

    return {
      rootPath,
      name,
      folders,
      totalFiles: stats.totalFiles,
      languages: stats.languages,
      frameworks,
      hasGit: await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, '.git')),
      hasTesting: tools.hasTesting,
      hasLinting: tools.hasLinting,
      packageManagers: tools.packageManagers,
      buildTools: tools.buildTools
    }
  }

  private async getWorkspaceStats(): Promise<{ totalFiles: number, languages: string[] }> {
    const result = await this.db.findOne(
      this.db.query()
        .select('COUNT(*) as total_files')
        .from('files')
    )

    const languages = await this.db.findMany(
      this.db.query()
        .select('language, COUNT(*) as count')
        .from('files')
        .where('language', 'IS NOT', null)
        .groupBy('language')
        .orderBy('count', 'DESC')
    )

    return {
      totalFiles: result?.total_files || 0,
      languages: languages.map(l => l.language)
    }
  }

  private async detectFrameworks(): Promise<string[]> {
    const frameworks: string[] = []
    const rootPath = vscode.workspace.rootPath || ''

    // Check package.json for web frameworks
    const packageJsonPath = this.fileSystem.joinPath(rootPath, 'package.json')
    if (await this.fileSystem.exists(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(await this.fileSystem.readFile(packageJsonPath))
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
        
        if (dependencies.react) frameworks.push('React')
        if (dependencies.vue) frameworks.push('Vue')
        if (dependencies.angular) frameworks.push('Angular')
        if (dependencies.svelte) frameworks.push('Svelte')
        if (dependencies.express) frameworks.push('Express')
        if (dependencies.fastify) frameworks.push('Fastify')
        if (dependencies.next) frameworks.push('Next.js')
        if (dependencies.nuxt) frameworks.push('Nuxt.js')
      } catch (error) {
        // Ignore JSON parsing errors
      }
    }

    // Check for Python frameworks
    const requirementsPath = this.fileSystem.joinPath(rootPath, 'requirements.txt')
    if (await this.fileSystem.exists(requirementsPath)) {
      try {
        const requirements = await this.fileSystem.readFile(requirementsPath)
        if (requirements.includes('django')) frameworks.push('Django')
        if (requirements.includes('flask')) frameworks.push('Flask')
        if (requirements.includes('fastapi')) frameworks.push('FastAPI')
      } catch (error) {
        // Ignore read errors
      }
    }

    return frameworks
  }

  private async detectTools(): Promise<{
    hasTesting: boolean
    hasLinting: boolean
    packageManagers: string[]
    buildTools: string[]
  }> {
    const rootPath = vscode.workspace.rootPath || ''
    const packageManagers: string[] = []
    const buildTools: string[] = []

    // Check for package managers
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'package.json'))) {
      packageManagers.push('npm')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'yarn.lock'))) {
      packageManagers.push('yarn')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'pnpm-lock.yaml'))) {
      packageManagers.push('pnpm')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'requirements.txt'))) {
      packageManagers.push('pip')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'Pipfile'))) {
      packageManagers.push('pipenv')
    }

    // Check for build tools
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'webpack.config.js'))) {
      buildTools.push('webpack')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'vite.config.js'))) {
      buildTools.push('vite')
    }
    if (await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'rollup.config.js'))) {
      buildTools.push('rollup')
    }

    const hasTesting = await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, '__tests__')) ||
                      await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'tests')) ||
                      await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'test'))

    const hasLinting = await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, '.eslintrc.js')) ||
                      await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, '.eslintrc.json')) ||
                      await this.fileSystem.exists(this.fileSystem.joinPath(rootPath, 'tslint.json'))

    return {
      hasTesting,
      hasLinting,
      packageManagers,
      buildTools
    }
  }

  private async analyzeCursorPosition(document: vscode.TextDocument, position: vscode.Position): Promise<CursorContext> {
    const line = position.line
    const column = position.character
    const lineText = document.lineAt(line).text
    const surroundingLines = this.getSurroundingLines(document, line, 3)
    
    const symbolAtCursor = await this.getSymbolAtPosition(document, position)
    const scopeAtCursor = this.getScopeAtPosition(document, position)
    const indentLevel = this.getIndentLevel(lineText)
    const isInComment = this.isInComment(lineText, column)
    const isInString = this.isInString(lineText, column)
    const isInFunction = this.isInFunction(document, position)
    const isInClass = this.isInClass(document, position)
    const nearbySymbols = await this.getNearbySymbols(document, position)

    return {
      line,
      column,
      lineText,
      surroundingLines,
      symbolAtCursor,
      scopeAtCursor,
      indentLevel,
      isInComment,
      isInString,
      isInFunction,
      isInClass,
      nearbySymbols
    }
  }

  private getSurroundingLines(document: vscode.TextDocument, line: number, radius: number): string[] {
    const lines: string[] = []
    const start = Math.max(0, line - radius)
    const end = Math.min(document.lineCount - 1, line + radius)

    for (let i = start; i <= end; i++) {
      lines.push(document.lineAt(i).text)
    }

    return lines
  }

  private async getSymbolAtPosition(document: vscode.TextDocument, position: vscode.Position): Promise<SymbolContext | undefined> {
    // This would integrate with language services to get symbol information
    // For now, return undefined
    return undefined
  }

  private getScopeAtPosition(document: vscode.TextDocument, position: vscode.Position): string {
    // Simple scope detection based on indentation and keywords
    let scope = 'global'
    const line = position.line

    for (let i = line; i >= 0; i--) {
      const lineText = document.lineAt(i).text.trim()
      
      if (lineText.includes('class ') && !lineText.startsWith('//')) {
        const match = lineText.match(/class\s+(\w+)/)
        if (match) {
          scope = match[1]
          break
        }
      } else if (lineText.includes('function ') && !lineText.startsWith('//')) {
        const match = lineText.match(/function\s+(\w+)/)
        if (match) {
          scope = match[1]
          break
        }
      } else if (lineText.includes('def ') && !lineText.startsWith('#')) {
        const match = lineText.match(/def\s+(\w+)/)
        if (match) {
          scope = match[1]
          break
        }
      }
    }

    return scope
  }

  private getIndentLevel(lineText: string): number {
    const match = lineText.match(/^(\s*)/)
    return match ? match[1].length : 0
  }

  private isInComment(lineText: string, column: number): boolean {
    const beforeCursor = lineText.substring(0, column)
    return beforeCursor.includes('//') || beforeCursor.includes('#')
  }

  private isInString(lineText: string, column: number): boolean {
    const beforeCursor = lineText.substring(0, column)
    const singleQuotes = (beforeCursor.match(/'/g) || []).length
    const doubleQuotes = (beforeCursor.match(/"/g) || []).length
    return singleQuotes % 2 === 1 || doubleQuotes % 2 === 1
  }

  private isInFunction(document: vscode.TextDocument, position: vscode.Position): boolean {
    const line = position.line
    
    for (let i = line; i >= 0; i--) {
      const lineText = document.lineAt(i).text.trim()
      if (lineText.includes('function ') || lineText.includes('def ')) {
        return true
      }
    }
    
    return false
  }

  private isInClass(document: vscode.TextDocument, position: vscode.Position): boolean {
    const line = position.line
    
    for (let i = line; i >= 0; i--) {
      const lineText = document.lineAt(i).text.trim()
      if (lineText.includes('class ')) {
        return true
      }
    }
    
    return false
  }

  private async getNearbySymbols(document: vscode.TextDocument, position: vscode.Position): Promise<SymbolContext[]> {
    // This would get symbols near the cursor position
    // For now, return empty array
    return []
  }

  private async analyzeSelection(document: vscode.TextDocument, selection: vscode.Selection): Promise<SelectionContext> {
    const startLine = selection.start.line
    const endLine = selection.end.line
    const startColumn = selection.start.character
    const endColumn = selection.end.character
    const selectedText = document.getText(selection)
    
    const containedSymbols: SymbolContext[] = []
    const selectionType = this.determineSelectionType(selection, selectedText)

    return {
      startLine,
      endLine,
      startColumn,
      endColumn,
      selectedText,
      containedSymbols,
      selectionType
    }
  }

  private determineSelectionType(selection: vscode.Selection, text: string): SelectionContext['selectionType'] {
    if (selection.isSingleLine) {
      if (text.trim() === '') return 'partial_line'
      if (text.includes('\n')) return 'multi_line'
      return 'partial_line'
    }
    return 'multi_line'
  }

  private async analyzeProjectStructure(): Promise<ProjectStructureContext> {
    return {
      directories: [],
      patterns: [],
      conventions: [],
      architecture: {
        type: 'unknown',
        confidence: 0,
        layers: [],
        entryPoints: [],
        testStrategy: 'none'
      }
    }
  }

  private async analyzeRecentActivity(): Promise<RecentActivityContext> {
    return {
      recentFiles: [],
      recentEdits: [],
      activeProblems: [],
      searchHistory: [],
      commandHistory: []
    }
  }

  private async calculateRelevanceScores(currentFile: string, relatedFiles: RelatedFileContext[]): Promise<Map<string, number>> {
    const scores = new Map<string, number>()
    
    for (const file of relatedFiles) {
      scores.set(file.path, file.relevanceScore)
    }
    
    return scores
  }

  private determineFileType(relativePath: string, content: string): {
    isMain: boolean
    isTest: boolean
    isConfig: boolean
  } {
    const fileName = this.fileSystem.basename(relativePath).toLowerCase()
    const isTest = fileName.includes('test') || fileName.includes('spec') || relativePath.includes('/test/') || relativePath.includes('/__tests__/')
    const isConfig = fileName.includes('config') || fileName.includes('settings') || 
                    ['.json', '.yml', '.yaml', '.toml', '.ini'].some(ext => fileName.endsWith(ext))
    const isMain = fileName === 'index.js' || fileName === 'index.ts' || fileName === 'main.py' || fileName === 'app.py'

    return { isMain, isTest, isConfig }
  }

  private detectLanguage(filePath: string): string {
    const ext = this.fileSystem.extname(filePath).toLowerCase()
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.c': 'c',
      '.cpp': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift'
    }
    return languageMap[ext] || 'unknown'
  }

  private getDefaultCursorContext(): CursorContext {
    return {
      line: 0,
      column: 0,
      lineText: '',
      surroundingLines: [],
      scopeAtCursor: 'global',
      indentLevel: 0,
      isInComment: false,
      isInString: false,
      isInFunction: false,
      isInClass: false,
      nearbySymbols: []
    }
  }
}