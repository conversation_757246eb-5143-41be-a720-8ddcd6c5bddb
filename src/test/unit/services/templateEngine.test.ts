import { TemplateEngine } from '../../../services/templateEngine'
import * as fs from 'fs'

jest.mock('fs')

describe('TemplateEngine', () => {
  let templateEngine: TemplateEngine

  beforeEach(() => {
    templateEngine = new TemplateEngine()
  })

  describe('template compilation', () => {
    it('should compile basic templates', () => {
      const template = 'Hello {{name}}!'
      const compiled = templateEngine.compile(template)
      const result = compiled({ name: 'World' })
      
      expect(result).toBe('Hello World!')
    })

    it('should handle complex template data', () => {
      const template = `
        # {{title}}
        {{#each items}}
        - {{this.name}}: {{this.value}}
        {{/each}}
      `
      
      const compiled = templateEngine.compile(template)
      const result = compiled({
        title: 'Test Tasks',
        items: [
          { name: 'Task 1', value: 'Complete' },
          { name: 'Task 2', value: 'In Progress' }
        ]
      })
      
      expect(result).toContain('# Test Tasks')
      expect(result).toContain('- Task 1: Complete')
      expect(result).toContain('- Task 2: In Progress')
    })
  })

  describe('template loading', () => {
    it('should load templates from file system', async () => {
      const mockReadFile = fs.promises.readFile as jest.Mock
      mockReadFile.mockResolvedValue('Template content: {{data}}')
      
      await templateEngine.loadTemplate('test-template')
      
      expect(mockReadFile).toHaveBeenCalledWith(
        expect.stringContaining('test-template.hbs'),
        'utf-8'
      )
    })

    it('should cache loaded templates', async () => {
      const mockReadFile = fs.promises.readFile as jest.Mock
      mockReadFile.mockResolvedValue('Template content: {{data}}')
      
      await templateEngine.loadTemplate('test-template')
      await templateEngine.loadTemplate('test-template')
      
      expect(mockReadFile).toHaveBeenCalledTimes(1)
    })
  })

  describe('template rendering', () => {
    it('should render loaded templates', async () => {
      const mockReadFile = fs.promises.readFile as jest.Mock
      mockReadFile.mockResolvedValue('Task: {{task.name}} - {{task.status}}')
      
      const result = await templateEngine.render('task-template', {
        task: { name: 'Test Task', status: 'Complete' }
      })
      
      expect(result).toBe('Task: Test Task - Complete')
    })

    it('should throw error for missing templates', async () => {
      const mockReadFile = fs.promises.readFile as jest.Mock
      mockReadFile.mockRejectedValue(new Error('File not found'))
      
      await expect(templateEngine.render('missing-template', {}))
        .rejects.toThrow('Template not found: missing-template')
    })
  })

  describe('helper functions', () => {
    it('should register custom helpers', () => {
      templateEngine.registerHelper('uppercase', (str: string) => str.toUpperCase())
      
      const template = 'Hello {{uppercase name}}!'
      const compiled = templateEngine.compile(template)
      const result = compiled({ name: 'world' })
      
      expect(result).toBe('Hello WORLD!')
    })

    it('should provide built-in helpers', () => {
      const template = '{{#if condition}}YES{{else}}NO{{/if}}'
      const compiled = templateEngine.compile(template)
      
      expect(compiled({ condition: true })).toBe('YES')
      expect(compiled({ condition: false })).toBe('NO')
    })
  })

  describe('error handling', () => {
    it('should handle template compilation errors', () => {
      const invalidTemplate = '{{#each items}}{{/if}}'
      
      expect(() => templateEngine.compile(invalidTemplate)).toThrow()
    })

    it('should handle template rendering errors', () => {
      const template = '{{data.nonexistent.property}}'
      const compiled = templateEngine.compile(template)
      
      expect(() => compiled({})).toThrow()
    })
  })
})