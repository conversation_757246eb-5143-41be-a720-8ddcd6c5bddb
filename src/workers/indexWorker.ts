import { Worker, isMainThread, parentPort, workerData } from 'worker_threads'
import * as path from 'path'
import * as fs from 'fs'

// Worker message types
export interface WorkerMessage {
  id: string
  type: WorkerMessageType
  data: any
  timestamp: Date
}

export interface WorkerResponse {
  id: string
  success: boolean
  result?: any
  error?: string
  duration: number
  timestamp: Date
}

export interface WorkerTask {
  id: string
  type: WorkerTaskType
  data: any
  priority: TaskPriority
  timeout?: number
  retryCount?: number
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
}

export type WorkerMessageType = 'task' | 'cancel' | 'status' | 'shutdown'
export type WorkerTaskType = 'index-file' | 'analyze-code' | 'generate-embeddings' | 'parse-ast' | 'compress-data' | 'calculate-similarity'
export type TaskPriority = 'low' | 'medium' | 'high' | 'critical'

// Worker Pool Manager
export class WorkerPoolManager {
  private workers: Map<string, Worker> = new Map()
  private taskQueue: WorkerTask[] = []
  private activeTasks: Map<string, WorkerTask> = new Map()
  private completedTasks: Map<string, WorkerResponse> = new Map()
  private workerStates: Map<string, WorkerState> = new Map()
  private maxWorkers: number = 4
  private maxTasksPerWorker: number = 10
  private taskTimeout: number = 30000
  private retryLimit: number = 3

  constructor(
    private readonly workerScript: string,
    maxWorkers: number = 4
  ) {
    this.maxWorkers = maxWorkers
    this.initializeWorkers()
  }

  public async submitTask(
    type: WorkerTaskType,
    data: any,
    priority: TaskPriority = 'medium',
    timeout?: number
  ): Promise<string> {
    const task: WorkerTask = {
      id: this.generateTaskId(),
      type,
      data,
      priority,
      timeout: timeout || this.taskTimeout,
      retryCount: 0,
      createdAt: new Date()
    }

    this.taskQueue.push(task)
    this.sortTaskQueue()
    this.processNextTask()

    return task.id
  }

  public async getTaskResult(taskId: string): Promise<WorkerResponse | undefined> {
    return this.completedTasks.get(taskId)
  }

  public async cancelTask(taskId: string): Promise<boolean> {
    // Remove from queue
    const queueIndex = this.taskQueue.findIndex(t => t.id === taskId)
    if (queueIndex !== -1) {
      this.taskQueue.splice(queueIndex, 1)
      return true
    }

    // Cancel active task
    const activeTask = this.activeTasks.get(taskId)
    if (activeTask) {
      const workerId = this.findWorkerForTask(taskId)
      if (workerId) {
        const worker = this.workers.get(workerId)
        if (worker) {
          worker.postMessage({
            id: taskId,
            type: 'cancel',
            data: {},
            timestamp: new Date()
          })
          return true
        }
      }
    }

    return false
  }

  public getWorkerStats(): {
    totalWorkers: number
    activeWorkers: number
    idleWorkers: number
    queuedTasks: number
    activeTasks: number
    completedTasks: number
  } {
    const activeWorkers = Array.from(this.workerStates.values()).filter(s => s.status === 'busy').length
    const idleWorkers = this.workers.size - activeWorkers

    return {
      totalWorkers: this.workers.size,
      activeWorkers,
      idleWorkers,
      queuedTasks: this.taskQueue.length,
      activeTasks: this.activeTasks.size,
      completedTasks: this.completedTasks.size
    }
  }

  private initializeWorkers(): void {
    for (let i = 0; i < this.maxWorkers; i++) {
      this.createWorker()
    }
  }

  private createWorker(): string {
    const workerId = this.generateWorkerId()
    const worker = new Worker(this.workerScript, {
      workerData: { workerId }
    })

    worker.on('message', (response: WorkerResponse) => {
      this.handleWorkerResponse(workerId, response)
    })

    worker.on('error', (error) => {
      this.handleWorkerError(workerId, error)
    })

    worker.on('exit', (code) => {
      this.handleWorkerExit(workerId, code)
    })

    this.workers.set(workerId, worker)
    this.workerStates.set(workerId, {
      id: workerId,
      status: 'idle',
      activeTasks: 0,
      totalTasks: 0,
      createdAt: new Date(),
      lastActive: new Date()
    })

    return workerId
  }

  private handleWorkerResponse(workerId: string, response: WorkerResponse): void {
    const workerState = this.workerStates.get(workerId)
    if (workerState) {
      workerState.status = 'idle'
      workerState.activeTasks--
      workerState.lastActive = new Date()
    }

    const task = this.activeTasks.get(response.id)
    if (task) {
      task.completedAt = new Date()
      this.activeTasks.delete(response.id)
      this.completedTasks.set(response.id, response)

      // If task failed and retries available, requeue it
      if (!response.success && task.retryCount < this.retryLimit) {
        task.retryCount++
        this.taskQueue.push(task)
        this.sortTaskQueue()
      }
    }

    this.processNextTask()
  }

  private handleWorkerError(workerId: string, error: Error): void {
    console.error(`Worker ${workerId} error:`, error)
    
    const workerState = this.workerStates.get(workerId)
    if (workerState) {
      workerState.status = 'error'
    }

    // Restart worker
    this.restartWorker(workerId)
  }

  private handleWorkerExit(workerId: string, code: number): void {
    console.log(`Worker ${workerId} exited with code ${code}`)
    
    // Remove worker
    this.workers.delete(workerId)
    this.workerStates.delete(workerId)
    
    // Create new worker if needed
    if (this.workers.size < this.maxWorkers) {
      this.createWorker()
    }
  }

  private restartWorker(workerId: string): void {
    const worker = this.workers.get(workerId)
    if (worker) {
      worker.terminate()
    }
    
    this.workers.delete(workerId)
    this.workerStates.delete(workerId)
    
    // Create new worker
    this.createWorker()
  }

  private processNextTask(): void {
    if (this.taskQueue.length === 0) return

    const availableWorker = this.findAvailableWorker()
    if (!availableWorker) return

    const task = this.taskQueue.shift()
    if (!task) return

    task.startedAt = new Date()
    this.activeTasks.set(task.id, task)

    const worker = this.workers.get(availableWorker)
    const workerState = this.workerStates.get(availableWorker)
    
    if (worker && workerState) {
      workerState.status = 'busy'
      workerState.activeTasks++
      workerState.totalTasks++
      workerState.lastActive = new Date()

      worker.postMessage({
        id: task.id,
        type: 'task',
        data: { taskType: task.type, taskData: task.data },
        timestamp: new Date()
      })

      // Set timeout for task
      setTimeout(() => {
        this.handleTaskTimeout(task.id)
      }, task.timeout || this.taskTimeout)
    }
  }

  private findAvailableWorker(): string | undefined {
    for (const [workerId, state] of this.workerStates) {
      if (state.status === 'idle' && state.activeTasks < this.maxTasksPerWorker) {
        return workerId
      }
    }
    return undefined
  }

  private findWorkerForTask(taskId: string): string | undefined {
    const task = this.activeTasks.get(taskId)
    if (!task) return undefined

    for (const [workerId, state] of this.workerStates) {
      if (state.status === 'busy') {
        // Would need to track which worker has which task
        return workerId
      }
    }
    return undefined
  }

  private handleTaskTimeout(taskId: string): void {
    const task = this.activeTasks.get(taskId)
    if (task) {
      this.completedTasks.set(taskId, {
        id: taskId,
        success: false,
        error: 'Task timeout',
        duration: Date.now() - (task.startedAt?.getTime() || task.createdAt.getTime()),
        timestamp: new Date()
      })
      
      this.activeTasks.delete(taskId)
      this.cancelTask(taskId)
    }
  }

  private sortTaskQueue(): void {
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      const aPriority = priorityOrder[a.priority]
      const bPriority = priorityOrder[b.priority]
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }
      
      return a.createdAt.getTime() - b.createdAt.getTime()
    })
  }

  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateWorkerId(): string {
    return `worker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  public async terminate(): Promise<void> {
    const terminationPromises = Array.from(this.workers.values()).map(worker => {
      return worker.terminate()
    })
    
    await Promise.all(terminationPromises)
    
    this.workers.clear()
    this.workerStates.clear()
    this.taskQueue.length = 0
    this.activeTasks.clear()
  }
}

// Worker State Interface
interface WorkerState {
  id: string
  status: 'idle' | 'busy' | 'error'
  activeTasks: number
  totalTasks: number
  createdAt: Date
  lastActive: Date
}

// Worker Script (runs in worker thread)
if (!isMainThread) {
  const workerId = workerData.workerId

  parentPort?.on('message', async (message: WorkerMessage) => {
    const startTime = Date.now()
    
    try {
      let result: any
      
      switch (message.type) {
        case 'task':
          result = await processTask(message.data.taskType, message.data.taskData)
          break
        case 'cancel':
          // Handle cancellation
          result = { cancelled: true }
          break
        case 'status':
          result = { workerId, status: 'active' }
          break
        case 'shutdown':
          process.exit(0)
          break
        default:
          throw new Error(`Unknown message type: ${message.type}`)
      }

      const response: WorkerResponse = {
        id: message.id,
        success: true,
        result,
        duration: Date.now() - startTime,
        timestamp: new Date()
      }

      parentPort?.postMessage(response)
    } catch (error) {
      const response: WorkerResponse = {
        id: message.id,
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        timestamp: new Date()
      }

      parentPort?.postMessage(response)
    }
  })

  async function processTask(taskType: WorkerTaskType, taskData: any): Promise<any> {
    switch (taskType) {
      case 'index-file':
        return await indexFile(taskData)
      case 'analyze-code':
        return await analyzeCode(taskData)
      case 'generate-embeddings':
        return await generateEmbeddings(taskData)
      case 'parse-ast':
        return await parseAST(taskData)
      case 'compress-data':
        return await compressData(taskData)
      case 'calculate-similarity':
        return await calculateSimilarity(taskData)
      default:
        throw new Error(`Unknown task type: ${taskType}`)
    }
  }

  async function indexFile(data: { filePath: string }): Promise<any> {
    const { filePath } = data
    
    try {
      const stats = fs.statSync(filePath)
      const content = fs.readFileSync(filePath, 'utf8')
      
      return {
        filePath,
        size: stats.size,
        lastModified: stats.mtime,
        lineCount: content.split('\n').length,
        characterCount: content.length,
        wordCount: content.split(/\s+/).length,
        indexed: true
      }
    } catch (error) {
      throw new Error(`Failed to index file ${filePath}: ${error.message}`)
    }
  }

  async function analyzeCode(data: { code: string; language: string }): Promise<any> {
    const { code, language } = data
    
    // Simplified code analysis
    const lines = code.split('\n')
    const functions = lines.filter(line => 
      line.includes('function') || 
      line.includes('def ') || 
      line.includes('class ')
    )
    
    return {
      language,
      lineCount: lines.length,
      functionCount: functions.length,
      complexity: calculateComplexity(code),
      issues: findIssues(code),
      analyzed: true
    }
  }

  async function generateEmbeddings(data: { text: string }): Promise<any> {
    const { text } = data
    
    // Simplified embedding generation (in reality, this would use a model)
    const words = text.toLowerCase().split(/\s+/)
    const embedding = new Array(384).fill(0).map(() => Math.random() - 0.5)
    
    return {
      text: text.substring(0, 100), // Truncate for response
      embedding,
      dimension: embedding.length,
      wordCount: words.length,
      generated: true
    }
  }

  async function parseAST(data: { code: string; language: string }): Promise<any> {
    const { code, language } = data
    
    // Simplified AST parsing
    const nodes = []
    const lines = code.split('\n')
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line.includes('function') || line.includes('def ') || line.includes('class ')) {
        nodes.push({
          type: 'declaration',
          line: i + 1,
          content: line
        })
      }
    }
    
    return {
      language,
      nodeCount: nodes.length,
      nodes,
      parsed: true
    }
  }

  async function compressData(data: { data: any }): Promise<any> {
    const { data: inputData } = data
    
    // Simplified compression (in reality, would use proper compression)
    const serialized = JSON.stringify(inputData)
    const compressed = Buffer.from(serialized).toString('base64')
    
    return {
      originalSize: serialized.length,
      compressedSize: compressed.length,
      compressionRatio: compressed.length / serialized.length,
      compressed: compressed,
      success: true
    }
  }

  async function calculateSimilarity(data: { vector1: number[]; vector2: number[] }): Promise<any> {
    const { vector1, vector2 } = data
    
    if (vector1.length !== vector2.length) {
      throw new Error('Vectors must have the same length')
    }
    
    // Calculate cosine similarity
    let dotProduct = 0
    let magnitude1 = 0
    let magnitude2 = 0
    
    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i]
      magnitude1 += vector1[i] * vector1[i]
      magnitude2 += vector2[i] * vector2[i]
    }
    
    magnitude1 = Math.sqrt(magnitude1)
    magnitude2 = Math.sqrt(magnitude2)
    
    const similarity = dotProduct / (magnitude1 * magnitude2)
    
    return {
      similarity,
      dotProduct,
      magnitude1,
      magnitude2,
      vectorLength: vector1.length,
      calculated: true
    }
  }

  function calculateComplexity(code: string): number {
    // Simplified complexity calculation
    const complexityKeywords = ['if', 'else', 'for', 'while', 'switch', 'case', 'try', 'catch']
    let complexity = 1
    
    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g')
      const matches = code.match(regex)
      if (matches) {
        complexity += matches.length
      }
    }
    
    return complexity
  }

  function findIssues(code: string): string[] {
    const issues = []
    
    // Simple issue detection
    if (code.includes('var ')) {
      issues.push('Use let or const instead of var')
    }
    
    if (code.includes('==')) {
      issues.push('Use strict equality (===) instead of loose equality (==)')
    }
    
    if (code.includes('eval(')) {
      issues.push('Avoid using eval() for security reasons')
    }
    
    return issues
  }
}

// Convenience function to create worker pool
export function createWorkerPool(maxWorkers: number = 4): WorkerPoolManager {
  const workerScript = path.join(__dirname, 'indexWorker.js')
  return new WorkerPoolManager(workerScript, maxWorkers)
}

// High-level worker utilities
export class IndexWorkerManager {
  private workerPool: WorkerPoolManager

  constructor(maxWorkers: number = 4) {
    this.workerPool = createWorkerPool(maxWorkers)
  }

  public async indexFile(filePath: string): Promise<any> {
    const taskId = await this.workerPool.submitTask('index-file', { filePath }, 'medium')
    return this.waitForTask(taskId)
  }

  public async analyzeCode(code: string, language: string): Promise<any> {
    const taskId = await this.workerPool.submitTask('analyze-code', { code, language }, 'medium')
    return this.waitForTask(taskId)
  }

  public async generateEmbeddings(text: string): Promise<any> {
    const taskId = await this.workerPool.submitTask('generate-embeddings', { text }, 'high')
    return this.waitForTask(taskId)
  }

  public async batchIndexFiles(filePaths: string[]): Promise<any[]> {
    const taskIds = await Promise.all(
      filePaths.map(filePath => 
        this.workerPool.submitTask('index-file', { filePath }, 'medium')
      )
    )
    
    return Promise.all(taskIds.map(taskId => this.waitForTask(taskId)))
  }

  private async waitForTask(taskId: string, timeout: number = 30000): Promise<any> {
    return new Promise((resolve, reject) => {
      const checkResult = async () => {
        const result = await this.workerPool.getTaskResult(taskId)
        if (result) {
          if (result.success) {
            resolve(result.result)
          } else {
            reject(new Error(result.error))
          }
        } else {
          setTimeout(checkResult, 100)
        }
      }
      
      setTimeout(() => {
        reject(new Error('Task timeout'))
      }, timeout)
      
      checkResult()
    })
  }

  public getStats() {
    return this.workerPool.getWorkerStats()
  }

  public async dispose(): Promise<void> {
    await this.workerPool.terminate()
  }
}