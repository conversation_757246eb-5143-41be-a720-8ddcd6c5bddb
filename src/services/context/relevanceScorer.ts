import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'

export interface RelevanceScore {
  filePath: string
  score: number
  factors: RelevanceFactor[]
  category: RelevanceCategory
  reason: string
  confidence: number
}

export interface RelevanceFactor {
  type: FactorType
  score: number
  weight: number
  contribution: number
  description: string
  evidence?: string[]
}

export type FactorType = 
  | 'dependency'
  | 'reverse_dependency'
  | 'sibling'
  | 'shared_symbols'
  | 'import_relationship'
  | 'naming_similarity'
  | 'structural_similarity'
  | 'recent_activity'
  | 'edit_history'
  | 'test_relationship'
  | 'documentation'
  | 'error_correlation'
  | 'semantic_similarity'
  | 'usage_frequency'
  | 'proximity'

export type RelevanceCategory = 
  | 'directly_related'
  | 'structurally_related'
  | 'semantically_related'
  | 'contextually_related'
  | 'historically_related'
  | 'weakly_related'

export interface ScoringContext {
  currentFile: string
  cursorPosition?: { line: number, column: number }
  selectedText?: string
  recentFiles?: string[]
  projectRoot: string
  userIntent?: string
  timeWeight?: number
  includeTests?: boolean
  maxDistance?: number
}

export interface ScoringWeights {
  dependency: number
  reverseDependency: number
  sibling: number
  sharedSymbols: number
  importRelationship: number
  namingSimilarity: number
  structuralSimilarity: number
  recentActivity: number
  editHistory: number
  testRelationship: number
  documentation: number
  errorCorrelation: number
  semanticSimilarity: number
  usageFrequency: number
  proximity: number
}

export interface FileAnalysis {
  filePath: string
  size: number
  language: string
  symbols: SymbolInfo[]
  imports: string[]
  exports: string[]
  lastModified: Date
  editFrequency: number
  errorCount: number
  testCoverage?: number
}

export interface SymbolInfo {
  name: string
  type: string
  line: number
  scope: string
  isExported: boolean
  usageCount: number
}

export interface RelationshipGraph {
  dependencies: Map<string, string[]>
  reverseDependencies: Map<string, string[]>
  sharedSymbols: Map<string, Set<string>>
  siblingFiles: Map<string, string[]>
  testRelationships: Map<string, string[]>
}

export class RelevanceScorer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private defaultWeights: ScoringWeights
  private analysisCache = new Map<string, FileAnalysis>()
  private relationshipGraph?: RelationshipGraph

  constructor(fileSystem: FileSystemService, db: ConnectionManager) {
    this.fileSystem = fileSystem
    this.db = db
    this.defaultWeights = {
      dependency: 0.9,
      reverseDependency: 0.8,
      sibling: 0.3,
      sharedSymbols: 0.7,
      importRelationship: 0.8,
      namingSimilarity: 0.4,
      structuralSimilarity: 0.5,
      recentActivity: 0.6,
      editHistory: 0.4,
      testRelationship: 0.7,
      documentation: 0.3,
      errorCorrelation: 0.5,
      semanticSimilarity: 0.6,
      usageFrequency: 0.4,
      proximity: 0.5
    }
  }

  async scoreFiles(
    candidateFiles: string[],
    context: ScoringContext,
    weights?: Partial<ScoringWeights>
  ): Promise<RelevanceScore[]> {
    const scoringWeights = { ...this.defaultWeights, ...weights }
    const scores: RelevanceScore[] = []

    // Build relationship graph if not cached
    if (!this.relationshipGraph) {
      this.relationshipGraph = await this.buildRelationshipGraph()
    }

    // Analyze current file
    const currentFileAnalysis = await this.analyzeFile(context.currentFile)

    for (const filePath of candidateFiles) {
      if (filePath === context.currentFile) continue

      const fileAnalysis = await this.analyzeFile(filePath)
      const factors = await this.calculateFactors(
        currentFileAnalysis,
        fileAnalysis,
        context,
        scoringWeights
      )

      const totalScore = this.aggregateScore(factors)
      const category = this.categorizeRelevance(totalScore, factors)
      const reason = this.generateReason(factors)
      const confidence = this.calculateConfidence(factors)

      scores.push({
        filePath,
        score: totalScore,
        factors,
        category,
        reason,
        confidence
      })
    }

    return scores.sort((a, b) => b.score - a.score)
  }

  private async buildRelationshipGraph(): Promise<RelationshipGraph> {
    const dependencies = new Map<string, string[]>()
    const reverseDependencies = new Map<string, string[]>()
    const sharedSymbols = new Map<string, Set<string>>()
    const siblingFiles = new Map<string, string[]>()
    const testRelationships = new Map<string, string[]>()

    // Build dependency relationships
    const deps = await this.db.findMany(
      this.db.query()
        .select('f1.path as from_file, f2.path as to_file')
        .from('dependencies d')
        .innerJoin('files f1', 'f1.id = d.file_id')
        .innerJoin('files f2', 'f2.path = d.dependency_path')
    )

    for (const dep of deps) {
      if (!dependencies.has(dep.from_file)) {
        dependencies.set(dep.from_file, [])
      }
      dependencies.get(dep.from_file)!.push(dep.to_file)

      if (!reverseDependencies.has(dep.to_file)) {
        reverseDependencies.set(dep.to_file, [])
      }
      reverseDependencies.get(dep.to_file)!.push(dep.from_file)
    }

    // Build shared symbol relationships
    const symbols = await this.db.findMany(
      this.db.query()
        .select('f.path, s.name, s.type')
        .from('symbols s')
        .innerJoin('files f', 'f.id = s.file_id')
        .where('s.is_exported', '=', 1)
    )

    const symbolToFiles = new Map<string, Set<string>>()
    for (const symbol of symbols) {
      const key = `${symbol.name}:${symbol.type}`
      if (!symbolToFiles.has(key)) {
        symbolToFiles.set(key, new Set())
      }
      symbolToFiles.get(key)!.add(symbol.path)
    }

    for (const [symbolKey, files] of symbolToFiles) {
      if (files.size > 1) {
        for (const file1 of files) {
          if (!sharedSymbols.has(file1)) {
            sharedSymbols.set(file1, new Set())
          }
          for (const file2 of files) {
            if (file1 !== file2) {
              sharedSymbols.get(file1)!.add(file2)
            }
          }
        }
      }
    }

    // Build sibling file relationships
    const files = await this.db.findMany(
      this.db.query()
        .select('path')
        .from('files')
    )

    const dirToFiles = new Map<string, string[]>()
    for (const file of files) {
      const dir = this.fileSystem.dirname(file.path)
      if (!dirToFiles.has(dir)) {
        dirToFiles.set(dir, [])
      }
      dirToFiles.get(dir)!.push(file.path)
    }

    for (const [dir, dirFiles] of dirToFiles) {
      for (const file1 of dirFiles) {
        siblingFiles.set(file1, dirFiles.filter(f => f !== file1))
      }
    }

    // Build test relationships
    for (const file of files) {
      const testFiles = this.findTestFiles(file.path, files.map(f => f.path))
      if (testFiles.length > 0) {
        testRelationships.set(file.path, testFiles)
      }
    }

    return {
      dependencies,
      reverseDependencies,
      sharedSymbols,
      siblingFiles,
      testRelationships
    }
  }

  private findTestFiles(sourceFile: string, allFiles: string[]): string[] {
    const baseName = this.fileSystem.basename(sourceFile, this.fileSystem.extname(sourceFile))
    const testPatterns = [
      `${baseName}.test.`,
      `${baseName}.spec.`,
      `test_${baseName}.`,
      `${baseName}_test.`
    ]

    return allFiles.filter(file => {
      const fileName = this.fileSystem.basename(file)
      return testPatterns.some(pattern => fileName.includes(pattern)) ||
             file.includes('/__tests__/') ||
             file.includes('/tests/') ||
             file.includes('/test/')
    })
  }

  private async analyzeFile(filePath: string): Promise<FileAnalysis> {
    if (this.analysisCache.has(filePath)) {
      return this.analysisCache.get(filePath)!
    }

    const fileRecord = await this.db.findOne(
      this.db.query()
        .select('*')
        .from('files')
        .where('path', '=', filePath)
    )

    if (!fileRecord) {
      throw new Error(`File not found in database: ${filePath}`)
    }

    const symbols = await this.db.findMany(
      this.db.query()
        .select('name, type, line_start, scope, is_exported')
        .from('symbols')
        .where('file_id', '=', fileRecord.id)
    )

    const imports = await this.db.findMany(
      this.db.query()
        .select('dependency_path')
        .from('dependencies')
        .where('file_id', '=', fileRecord.id)
    )

    const exports = symbols.filter(s => s.is_exported).map(s => s.name)

    const analysis: FileAnalysis = {
      filePath,
      size: fileRecord.size,
      language: fileRecord.language || 'unknown',
      symbols: symbols.map(s => ({
        name: s.name,
        type: s.type,
        line: s.line_start,
        scope: s.scope,
        isExported: s.is_exported === 1,
        usageCount: 0 // Would need usage analysis
      })),
      imports: imports.map(i => i.dependency_path),
      exports,
      lastModified: new Date(fileRecord.modified_time),
      editFrequency: 0, // Would track from edit history
      errorCount: 0 // Would get from diagnostics
    }

    this.analysisCache.set(filePath, analysis)
    return analysis
  }

  private async calculateFactors(
    currentFile: FileAnalysis,
    candidateFile: FileAnalysis,
    context: ScoringContext,
    weights: ScoringWeights
  ): Promise<RelevanceFactor[]> {
    const factors: RelevanceFactor[] = []

    // Dependency relationship
    const dependencyScore = this.calculateDependencyScore(currentFile, candidateFile)
    if (dependencyScore > 0) {
      factors.push({
        type: 'dependency',
        score: dependencyScore,
        weight: weights.dependency,
        contribution: dependencyScore * weights.dependency,
        description: 'Direct dependency relationship',
        evidence: [`${currentFile.filePath} depends on ${candidateFile.filePath}`]
      })
    }

    // Reverse dependency relationship
    const reverseDependencyScore = this.calculateReverseDependencyScore(currentFile, candidateFile)
    if (reverseDependencyScore > 0) {
      factors.push({
        type: 'reverse_dependency',
        score: reverseDependencyScore,
        weight: weights.reverseDependency,
        contribution: reverseDependencyScore * weights.reverseDependency,
        description: 'File depends on current file',
        evidence: [`${candidateFile.filePath} depends on ${currentFile.filePath}`]
      })
    }

    // Sibling relationship
    const siblingScore = this.calculateSiblingScore(currentFile, candidateFile)
    if (siblingScore > 0) {
      factors.push({
        type: 'sibling',
        score: siblingScore,
        weight: weights.sibling,
        contribution: siblingScore * weights.sibling,
        description: 'Files in same directory',
        evidence: ['Same directory location']
      })
    }

    // Shared symbols
    const sharedSymbolsScore = this.calculateSharedSymbolsScore(currentFile, candidateFile)
    if (sharedSymbolsScore > 0) {
      const sharedSymbols = this.getSharedSymbols(currentFile, candidateFile)
      factors.push({
        type: 'shared_symbols',
        score: sharedSymbolsScore,
        weight: weights.sharedSymbols,
        contribution: sharedSymbolsScore * weights.sharedSymbols,
        description: 'Files share common symbols',
        evidence: sharedSymbols.map(s => `Shared symbol: ${s}`)
      })
    }

    // Import relationship
    const importScore = this.calculateImportRelationshipScore(currentFile, candidateFile)
    if (importScore > 0) {
      factors.push({
        type: 'import_relationship',
        score: importScore,
        weight: weights.importRelationship,
        contribution: importScore * weights.importRelationship,
        description: 'Import/export relationship',
        evidence: ['Files have import/export relationship']
      })
    }

    // Naming similarity
    const namingScore = this.calculateNamingSimilarityScore(currentFile, candidateFile)
    if (namingScore > 0) {
      factors.push({
        type: 'naming_similarity',
        score: namingScore,
        weight: weights.namingSimilarity,
        contribution: namingScore * weights.namingSimilarity,
        description: 'Similar file names',
        evidence: ['File names are similar']
      })
    }

    // Structural similarity
    const structuralScore = this.calculateStructuralSimilarityScore(currentFile, candidateFile)
    if (structuralScore > 0) {
      factors.push({
        type: 'structural_similarity',
        score: structuralScore,
        weight: weights.structuralSimilarity,
        contribution: structuralScore * weights.structuralSimilarity,
        description: 'Similar code structure',
        evidence: ['Similar symbols and structure']
      })
    }

    // Test relationship
    const testScore = this.calculateTestRelationshipScore(currentFile, candidateFile, context)
    if (testScore > 0) {
      factors.push({
        type: 'test_relationship',
        score: testScore,
        weight: weights.testRelationship,
        contribution: testScore * weights.testRelationship,
        description: 'Test file relationship',
        evidence: ['One file tests the other']
      })
    }

    // Recent activity
    const recentActivityScore = this.calculateRecentActivityScore(candidateFile, context)
    if (recentActivityScore > 0) {
      factors.push({
        type: 'recent_activity',
        score: recentActivityScore,
        weight: weights.recentActivity,
        contribution: recentActivityScore * weights.recentActivity,
        description: 'Recently modified or accessed',
        evidence: ['File has recent activity']
      })
    }

    return factors
  }

  private calculateDependencyScore(current: FileAnalysis, candidate: FileAnalysis): number {
    if (!this.relationshipGraph) return 0
    
    const dependencies = this.relationshipGraph.dependencies.get(current.filePath) || []
    return dependencies.includes(candidate.filePath) ? 1.0 : 0
  }

  private calculateReverseDependencyScore(current: FileAnalysis, candidate: FileAnalysis): number {
    if (!this.relationshipGraph) return 0
    
    const reverseDeps = this.relationshipGraph.reverseDependencies.get(current.filePath) || []
    return reverseDeps.includes(candidate.filePath) ? 1.0 : 0
  }

  private calculateSiblingScore(current: FileAnalysis, candidate: FileAnalysis): number {
    const currentDir = this.fileSystem.dirname(current.filePath)
    const candidateDir = this.fileSystem.dirname(candidate.filePath)
    return currentDir === candidateDir ? 1.0 : 0
  }

  private calculateSharedSymbolsScore(current: FileAnalysis, candidate: FileAnalysis): number {
    const sharedSymbols = this.getSharedSymbols(current, candidate)
    const totalSymbols = Math.max(current.symbols.length, candidate.symbols.length)
    return totalSymbols > 0 ? sharedSymbols.length / totalSymbols : 0
  }

  private getSharedSymbols(current: FileAnalysis, candidate: FileAnalysis): string[] {
    const currentSymbols = new Set(current.symbols.map(s => `${s.name}:${s.type}`))
    const candidateSymbols = new Set(candidate.symbols.map(s => `${s.name}:${s.type}`))
    
    const shared: string[] = []
    for (const symbol of currentSymbols) {
      if (candidateSymbols.has(symbol)) {
        shared.push(symbol.split(':')[0])
      }
    }
    
    return shared
  }

  private calculateImportRelationshipScore(current: FileAnalysis, candidate: FileAnalysis): number {
    const currentImports = current.imports.includes(candidate.filePath)
    const candidateImports = candidate.imports.includes(current.filePath)
    
    if (currentImports && candidateImports) return 1.0
    if (currentImports || candidateImports) return 0.7
    return 0
  }

  private calculateNamingSimilarityScore(current: FileAnalysis, candidate: FileAnalysis): number {
    const currentName = this.fileSystem.basename(current.filePath, this.fileSystem.extname(current.filePath))
    const candidateName = this.fileSystem.basename(candidate.filePath, this.fileSystem.extname(candidate.filePath))
    
    // Simple string similarity
    const longer = currentName.length > candidateName.length ? currentName : candidateName
    const shorter = currentName.length > candidateName.length ? candidateName : currentName
    
    if (longer.length === 0) return 0
    
    const matches = this.getMatches(longer, shorter)
    return matches / longer.length
  }

  private getMatches(str1: string, str2: string): number {
    let matches = 0
    const minLength = Math.min(str1.length, str2.length)
    
    for (let i = 0; i < minLength; i++) {
      if (str1[i].toLowerCase() === str2[i].toLowerCase()) {
        matches++
      }
    }
    
    return matches
  }

  private calculateStructuralSimilarityScore(current: FileAnalysis, candidate: FileAnalysis): number {
    if (current.language !== candidate.language) return 0
    
    const currentSymbolTypes = new Set(current.symbols.map(s => s.type))
    const candidateSymbolTypes = new Set(candidate.symbols.map(s => s.type))
    
    const intersection = new Set([...currentSymbolTypes].filter(x => candidateSymbolTypes.has(x)))
    const union = new Set([...currentSymbolTypes, ...candidateSymbolTypes])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }

  private calculateTestRelationshipScore(current: FileAnalysis, candidate: FileAnalysis, context: ScoringContext): number {
    if (!context.includeTests) return 0
    
    const currentBaseName = this.fileSystem.basename(current.filePath, this.fileSystem.extname(current.filePath))
    const candidateBaseName = this.fileSystem.basename(candidate.filePath, this.fileSystem.extname(candidate.filePath))
    
    // Check if one is a test file for the other
    const isTest = candidateBaseName.includes('test') || candidateBaseName.includes('spec') ||
                   candidate.filePath.includes('/test/') || candidate.filePath.includes('/__tests__/')
    
    if (isTest && candidateBaseName.includes(currentBaseName)) {
      return 1.0
    }
    
    return 0
  }

  private calculateRecentActivityScore(candidate: FileAnalysis, context: ScoringContext): number {
    if (!context.recentFiles) return 0
    
    const isRecent = context.recentFiles.includes(candidate.filePath)
    if (isRecent) return 1.0
    
    // Score based on modification time
    const now = new Date()
    const daysSinceModified = (now.getTime() - candidate.lastModified.getTime()) / (1000 * 60 * 60 * 24)
    
    if (daysSinceModified < 1) return 0.8
    if (daysSinceModified < 7) return 0.5
    if (daysSinceModified < 30) return 0.2
    
    return 0
  }

  private aggregateScore(factors: RelevanceFactor[]): number {
    const totalContribution = factors.reduce((sum, factor) => sum + factor.contribution, 0)
    const totalWeight = factors.reduce((sum, factor) => sum + factor.weight, 0)
    
    return totalWeight > 0 ? totalContribution / totalWeight : 0
  }

  private categorizeRelevance(score: number, factors: RelevanceFactor[]): RelevanceCategory {
    if (score >= 0.8) return 'directly_related'
    if (score >= 0.6) return 'structurally_related'
    if (score >= 0.4) return 'semantically_related'
    if (score >= 0.2) return 'contextually_related'
    if (score > 0) return 'historically_related'
    return 'weakly_related'
  }

  private generateReason(factors: RelevanceFactor[]): string {
    if (factors.length === 0) return 'No specific relationship found'
    
    const topFactor = factors.reduce((max, factor) => 
      factor.contribution > max.contribution ? factor : max
    )
    
    return topFactor.description
  }

  private calculateConfidence(factors: RelevanceFactor[]): number {
    if (factors.length === 0) return 0
    
    const avgScore = factors.reduce((sum, factor) => sum + factor.score, 0) / factors.length
    const factorCount = factors.length
    
    // Confidence increases with more factors and higher scores
    return Math.min(avgScore * (1 + (factorCount - 1) * 0.1), 1.0)
  }

  clearCache(): void {
    this.analysisCache.clear()
    this.relationshipGraph = undefined
  }

  async getTopRelatedFiles(
    currentFile: string,
    context: Partial<ScoringContext>,
    limit: number = 10
  ): Promise<RelevanceScore[]> {
    const allFiles = await this.db.findMany(
      this.db.query()
        .select('path')
        .from('files')
    )

    const fullContext: ScoringContext = {
      projectRoot: '',
      includeTests: true,
      ...context,
      currentFile
    }

    const scores = await this.scoreFiles(
      allFiles.map(f => f.path),
      fullContext
    )

    return scores.slice(0, limit)
  }
}