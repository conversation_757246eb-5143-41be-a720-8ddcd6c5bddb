import * as vscode from 'vscode'
import { activate, deactivate } from '../../extension'

describe('Extension Integration Tests', () => {
  let context: vscode.ExtensionContext

  beforeEach(() => {
    context = {
      subscriptions: [],
      extensionPath: '/test/extension/path',
      globalState: {
        get: jest.fn(),
        update: jest.fn()
      },
      workspaceState: {
        get: jest.fn(),
        update: jest.fn()
      },
      secrets: {
        get: jest.fn(),
        store: jest.fn(),
        delete: jest.fn()
      },
      globalStorageUri: vscode.Uri.file('/test/global/storage'),
      logUri: vscode.Uri.file('/test/log'),
      storagePath: '/test/storage',
      globalStoragePath: '/test/global/storage',
      logPath: '/test/log',
      extension: {
        id: 'test.extension',
        extensionPath: '/test/extension/path',
        isActive: true,
        packageJSON: {},
        exports: {}
      } as any,
      environmentVariableCollection: {} as any,
      extensionUri: vscode.Uri.file('/test/extension/path'),
      storageUri: vscode.Uri.file('/test/storage'),
      extensionMode: vscode.ExtensionMode.Test
    }
  })

  afterEach(() => {
    if (deactivate) {
      deactivate()
    }
  })

  describe('extension activation', () => {
    it('should activate without errors', async () => {
      expect(async () => {
        await activate(context)
      }).not.toThrow()
    })

    it('should register commands', async () => {
      const registerCommandSpy = jest.spyOn(vscode.commands, 'registerCommand')
      
      await activate(context)
      
      expect(registerCommandSpy).toHaveBeenCalledWith(
        'naturalLanguageTaskTransform.transform',
        expect.any(Function)
      )
      expect(registerCommandSpy).toHaveBeenCalledWith(
        'naturalLanguageTaskTransform.suggest',
        expect.any(Function)
      )
      expect(registerCommandSpy).toHaveBeenCalledWith(
        'naturalLanguageTaskTransform.configure',
        expect.any(Function)
      )
    })

    it('should register context menu items', async () => {
      await activate(context)
      
      expect(context.subscriptions.length).toBeGreaterThan(0)
    })
  })

  describe('command execution', () => {
    beforeEach(async () => {
      await activate(context)
    })

    it('should execute transform command', async () => {
      const showInputBoxSpy = jest.spyOn(vscode.window, 'showInputBox')
      showInputBoxSpy.mockResolvedValue('Create a new feature')
      
      const showInformationMessageSpy = jest.spyOn(vscode.window, 'showInformationMessage')
      
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.transform')
      
      expect(showInputBoxSpy).toHaveBeenCalled()
    })

    it('should execute suggest command', async () => {
      const activeTextEditor = {
        document: {
          getText: jest.fn().mockReturnValue('console.log("hello world")')
        },
        selection: {
          isEmpty: true
        }
      } as any
      
      Object.defineProperty(vscode.window, 'activeTextEditor', {
        value: activeTextEditor,
        writable: true
      })
      
      const showInformationMessageSpy = jest.spyOn(vscode.window, 'showInformationMessage')
      
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.suggest')
      
      expect(showInformationMessageSpy).toHaveBeenCalled()
    })

    it('should execute configure command', async () => {
      const showQuickPickSpy = jest.spyOn(vscode.window, 'showQuickPick')
      showQuickPickSpy.mockResolvedValue({ label: 'OpenAI' } as any)
      
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.configure')
      
      expect(showQuickPickSpy).toHaveBeenCalled()
    })
  })

  describe('configuration management', () => {
    it('should handle configuration changes', async () => {
      await activate(context)
      
      const config = vscode.workspace.getConfiguration('naturalLanguageTaskTransform')
      const onDidChangeConfigurationSpy = jest.spyOn(vscode.workspace, 'onDidChangeConfiguration')
      
      // Simulate configuration change
      const mockEvent = {
        affectsConfiguration: jest.fn().mockReturnValue(true)
      }
      
      onDidChangeConfigurationSpy.mock.calls[0]?.[0](mockEvent)
      
      expect(mockEvent.affectsConfiguration).toHaveBeenCalledWith(
        'naturalLanguageTaskTransform'
      )
    })
  })

  describe('workspace integration', () => {
    it('should handle workspace folder changes', async () => {
      await activate(context)
      
      const onDidChangeWorkspaceFoldersSpy = jest.spyOn(
        vscode.workspace,
        'onDidChangeWorkspaceFolders'
      )
      
      expect(onDidChangeWorkspaceFoldersSpy).toHaveBeenCalled()
    })

    it('should index workspace files', async () => {
      vscode.workspace.workspaceFolders = [
        {
          uri: vscode.Uri.file('/test/workspace'),
          name: 'test-workspace',
          index: 0
        }
      ]
      
      await activate(context)
      
      // Extension should start indexing process
      expect(context.subscriptions.length).toBeGreaterThan(0)
    })
  })

  describe('error handling', () => {
    it('should handle activation errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()
      
      // Mock an error during activation
      const originalRegisterCommand = vscode.commands.registerCommand
      vscode.commands.registerCommand = jest.fn(() => {
        throw new Error('Registration failed')
      })
      
      await expect(activate(context)).rejects.toThrow('Registration failed')
      
      // Restore original function
      vscode.commands.registerCommand = originalRegisterCommand
    })
  })
})