# Basic Usage

This guide covers the essential features and everyday usage of the Natural Language Task Transformer extension.

## Core Features

### Transform Natural Language to Tasks

The primary feature of the extension is converting natural language descriptions into structured, actionable tasks.

#### Method 1: Context Menu (Recommended)

1. **Select text** containing your natural language description
2. **Right-click** to open the context menu
3. **Select** "Transform Natural Language to Tasks"
4. **Review** the generated tasks

#### Method 2: Command Palette

1. **Open Command Palette** (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. **Type** "Transform Natural Language to Tasks"
3. **Press Enter** and provide your description
4. **Review** the generated output

#### Method 3: Keyboard Shortcut

You can assign a custom keyboard shortcut:

1. **Open** Keyboard Shortcuts (`Ctrl+K Ctrl+S`)
2. **Search** for "taskTransformer.transform"
3. **Assign** your preferred shortcut

### Example Transformations

#### Simple Feature Request

**Input:**
```
"Add a search bar to the header with autocomplete"
```

**Output:**
```markdown
## Search Bar Implementation

### Tasks:
1. **Create search component**
   - Design search input with icon
   - Add to header layout
   - Style with existing theme

2. **Implement autocomplete functionality**
   - Add search suggestions API
   - Create dropdown component
   - Handle keyboard navigation

3. **Add search logic**
   - Implement search algorithm
   - Handle search results
   - Add search history

### Files to modify:
- `src/components/Header.jsx`
- `src/components/SearchBar.jsx`
- `src/components/AutoComplete.jsx`
- `src/api/search.js`
- `src/styles/header.css`
```

#### Bug Fix Request

**Input:**
```
"Fix the login page where users can't submit the form when password is empty"
```

**Output:**
```markdown
## Login Form Bug Fix

### Tasks:
1. **Identify validation logic**
   - Review current form validation
   - Find password validation rules
   - Check submit button state

2. **Fix password validation**
   - Add empty password check
   - Update validation messages
   - Ensure form state updates

3. **Test fix**
   - Create test cases for empty password
   - Verify submit button behavior
   - Test error message display

### Files to modify:
- `src/components/LoginForm.jsx`
- `src/utils/validation.js`
- `src/tests/LoginForm.test.js`
```

### Context-Aware Suggestions

Get intelligent suggestions based on your current code selection.

#### How to Use

1. **Select code** you want suggestions for
2. **Right-click** and choose "Get Context-Aware Suggestions"
3. **Review** the contextual recommendations

#### Example Suggestions

**Selected Code:**
```javascript
async function fetchUserData(userId) {
    const response = await fetch(`/api/users/${userId}`);
    return response.json();
}
```

**Generated Suggestions:**
```markdown
## Suggestions for fetchUserData function

### Error Handling
- Add try-catch block for network errors
- Check response status before parsing JSON
- Handle 404 and 500 error cases

### Validation
- Validate userId parameter
- Check if userId is not null/undefined
- Sanitize userId to prevent injection

### Performance
- Add caching for frequently accessed users
- Implement request debouncing
- Consider adding loading states

### Testing
- Add unit tests for success cases
- Test error scenarios
- Mock fetch for testing

### TypeScript
- Add TypeScript types for better type safety
- Define User interface
- Add return type annotation
```

## Output Formats

The extension generates structured output in Markdown format with consistent sections:

### Standard Task Format

```markdown
## [Feature/Task Name]

### Tasks:
1. **Task Title**
   - Subtask 1
   - Subtask 2
   - Subtask 3

2. **Another Task**
   - Subtask details
   - Implementation notes

### Files to modify:
- `path/to/file1.js`
- `path/to/file2.css`

### Additional Notes:
- Any relevant context or considerations
```

### Code Review Format

```markdown
## Code Review: [Function/Component Name]

### Strengths:
- What's working well
- Good practices identified

### Improvements:
- Specific suggestions
- Best practice recommendations

### Security Considerations:
- Potential vulnerabilities
- Security improvements

### Performance:
- Optimization opportunities
- Efficiency improvements
```

## Workspace Integration

The extension integrates seamlessly with your VSCode workspace:

### File Context Analysis

- **Automatic Detection**: Recognizes your project type (React, Vue, Node.js, etc.)
- **File Structure**: Understands your directory organization
- **Dependencies**: Analyzes package.json and imports
- **Patterns**: Learns your coding patterns and conventions

### Smart Suggestions

Based on your codebase, the extension provides:

- **Framework-Specific**: Suggestions tailored to your framework
- **Consistent Naming**: Follows your existing naming conventions
- **File Organization**: Respects your project structure
- **Dependencies**: Suggests using existing libraries

## Tips for Better Results

### Writing Effective Descriptions

1. **Be Specific**: Include details about what you want to achieve
2. **Provide Context**: Mention related components or features
3. **Use Domain Language**: Include technical terms relevant to your project
4. **Specify Constraints**: Mention any limitations or requirements

#### Good Examples:
```
"Create a React component for user profile editing with form validation and image upload"
"Add JWT authentication middleware to protect API routes in Express.js"
"Implement pagination for the product listing page using React hooks"
```

#### Less Effective Examples:
```
"Make a form"
"Add auth"
"Fix the page"
```

### Selecting Code for Suggestions

1. **Complete Functions**: Select entire functions for comprehensive analysis
2. **Logical Blocks**: Select complete logical units (if statements, loops, etc.)
3. **Classes/Components**: Select complete classes or components
4. **Configuration**: Select configuration objects or files

### Working with Generated Tasks

1. **Review and Customize**: Always review generated tasks and modify as needed
2. **Add Details**: Expand on generated tasks with project-specific details
3. **Prioritize**: Reorder tasks based on your priorities
4. **Track Progress**: Use the tasks as a checklist for implementation

## Common Workflows

### Feature Development

1. **Plan** → Transform feature description to tasks
2. **Design** → Get suggestions for component architecture
3. **Implement** → Use tasks as implementation guide
4. **Review** → Get suggestions for code improvements

### Bug Fixing

1. **Analyze** → Select problematic code for suggestions
2. **Plan** → Transform bug description to fix tasks
3. **Implement** → Follow task breakdown
4. **Test** → Include testing tasks in the plan

### Code Review

1. **Select** → Choose code sections for review
2. **Analyze** → Get context-aware suggestions
3. **Document** → Use suggestions to create review comments
4. **Improve** → Transform suggestions into improvement tasks

## Next Steps

- Learn about [Advanced Features](advanced-features.md)
- Explore [Configuration Options](configuration.md)
- Understand the [Template System](templates.md)
- Read [Best Practices](best-practices.md)