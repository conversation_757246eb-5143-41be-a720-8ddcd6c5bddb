import * as vscode from 'vscode'
import { Logger } from '../../utils/logger'
import { CacheManager } from '../cache/cacheManager'

export interface BatchJob<T, R> {
  id: string
  name: string
  items: T[]
  processor: (items: T[]) => Promise<R[]>
  options: BatchOptions
  status: BatchStatus
  result?: R[]
  error?: Error
  startTime?: Date
  endTime?: Date
  progress: BatchProgress
  metadata: BatchMetadata
}

export interface BatchOptions {
  batchSize?: number
  maxConcurrency?: number
  retryCount?: number
  retryDelay?: number
  timeout?: number
  priority?: BatchPriority
  enableCache?: boolean
  cacheKey?: string
  cacheTtl?: number
  onProgress?: (progress: BatchProgress) => void
  onComplete?: (result: any) => void
  onError?: (error: Error) => void
  cancelToken?: vscode.CancellationToken
}

export interface BatchProgress {
  total: number
  completed: number
  failed: number
  percentage: number
  estimatedTimeRemaining?: number
  currentBatch?: number
  totalBatches?: number
  itemsPerSecond?: number
  errors: BatchError[]
}

export interface BatchError {
  itemIndex: number
  error: Error
  retryCount: number
  timestamp: Date
}

export interface BatchMetadata {
  createdAt: Date
  source: string
  tags: string[]
  priority: BatchPriority
  userId?: string
  workspaceId?: string
  context: Record<string, any>
}

export interface BatchResult<R> {
  jobId: string
  success: boolean
  results: R[]
  errors: BatchError[]
  totalItems: number
  processedItems: number
  failedItems: number
  duration: number
  throughput: number
  metadata: BatchMetadata
}

export type BatchStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused'
export type BatchPriority = 'low' | 'medium' | 'high' | 'critical'

export class BatchProcessor {
  private activeJobs: Map<string, BatchJob<any, any>> = new Map()
  private jobQueue: BatchJob<any, any>[] = []
  private maxConcurrentJobs: number = 3
  private isProcessing: boolean = false
  private processingTimer: NodeJS.Timeout | null = null

  constructor(
    private readonly logger: Logger,
    private readonly cacheManager?: CacheManager
  ) {
    this.startProcessing()
  }

  public async submitJob<T, R>(
    items: T[],
    processor: (items: T[]) => Promise<R[]>,
    options: BatchOptions = {}
  ): Promise<string> {
    const jobId = this.generateJobId()
    const job: BatchJob<T, R> = {
      id: jobId,
      name: options.cacheKey || `batch-job-${jobId}`,
      items,
      processor,
      options: {
        batchSize: 10,
        maxConcurrency: 3,
        retryCount: 3,
        retryDelay: 1000,
        timeout: 30000,
        priority: 'medium',
        enableCache: false,
        ...options
      },
      status: 'pending',
      progress: {
        total: items.length,
        completed: 0,
        failed: 0,
        percentage: 0,
        errors: []
      },
      metadata: {
        createdAt: new Date(),
        source: 'batch-processor',
        tags: [],
        priority: options.priority || 'medium',
        context: {}
      }
    }

    this.jobQueue.push(job)
    this.sortJobQueue()
    
    this.logger.info(`Submitted batch job ${jobId} with ${items.length} items`)
    return jobId
  }

  public async getJobStatus(jobId: string): Promise<BatchJob<any, any> | undefined> {
    return this.activeJobs.get(jobId) || this.jobQueue.find(job => job.id === jobId)
  }

  public async getJobResult<R>(jobId: string): Promise<BatchResult<R> | undefined> {
    const job = this.activeJobs.get(jobId)
    if (!job || job.status !== 'completed') {
      return undefined
    }

    return {
      jobId: job.id,
      success: job.status === 'completed',
      results: job.result || [],
      errors: job.progress.errors,
      totalItems: job.progress.total,
      processedItems: job.progress.completed,
      failedItems: job.progress.failed,
      duration: job.endTime && job.startTime ? 
        job.endTime.getTime() - job.startTime.getTime() : 0,
      throughput: job.progress.itemsPerSecond || 0,
      metadata: job.metadata
    }
  }

  public async cancelJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId) || this.jobQueue.find(j => j.id === jobId)
    if (!job) return false

    job.status = 'cancelled'
    
    // Remove from queue if pending
    const queueIndex = this.jobQueue.findIndex(j => j.id === jobId)
    if (queueIndex !== -1) {
      this.jobQueue.splice(queueIndex, 1)
    }

    // Set cancellation token if available
    if (job.options.cancelToken) {
      // VSCode cancellation tokens are read-only, so we use our own cancellation logic
    }

    this.logger.info(`Cancelled batch job ${jobId}`)
    return true
  }

  public async pauseJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId)
    if (!job || job.status !== 'running') return false

    job.status = 'paused'
    this.logger.info(`Paused batch job ${jobId}`)
    return true
  }

  public async resumeJob(jobId: string): Promise<boolean> {
    const job = this.activeJobs.get(jobId)
    if (!job || job.status !== 'paused') return false

    job.status = 'running'
    this.logger.info(`Resumed batch job ${jobId}`)
    return true
  }

  public getActiveJobs(): BatchJob<any, any>[] {
    return Array.from(this.activeJobs.values())
  }

  public getQueuedJobs(): BatchJob<any, any>[] {
    return [...this.jobQueue]
  }

  public getProcessingStats(): {
    activeJobs: number
    queuedJobs: number
    totalProcessed: number
    totalFailed: number
    averageThroughput: number
  } {
    const activeJobs = this.activeJobs.size
    const queuedJobs = this.jobQueue.length
    const allJobs = [...this.activeJobs.values()]
    
    const totalProcessed = allJobs.reduce((sum, job) => sum + job.progress.completed, 0)
    const totalFailed = allJobs.reduce((sum, job) => sum + job.progress.failed, 0)
    const averageThroughput = allJobs.reduce((sum, job) => sum + (job.progress.itemsPerSecond || 0), 0) / Math.max(allJobs.length, 1)

    return {
      activeJobs,
      queuedJobs,
      totalProcessed,
      totalFailed,
      averageThroughput
    }
  }

  // Specialized batch processing methods
  public async processFiles(
    filePaths: string[],
    processor: (filePath: string) => Promise<any>,
    options: BatchOptions = {}
  ): Promise<string> {
    return this.submitJob(
      filePaths,
      async (paths: string[]) => {
        const results = []
        for (const path of paths) {
          try {
            const result = await processor(path)
            results.push(result)
          } catch (error) {
            this.logger.error(`Error processing file ${path}:`, error)
            results.push({ error: error.message, path })
          }
        }
        return results
      },
      {
        batchSize: 5,
        maxConcurrency: 2,
        ...options
      }
    )
  }

  public async processWithProgress<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    progressCallback: (progress: BatchProgress) => void,
    options: BatchOptions = {}
  ): Promise<string> {
    return this.submitJob(
      items,
      async (batch: T[]) => {
        const results: R[] = []
        for (let i = 0; i < batch.length; i++) {
          try {
            const result = await processor(batch[i])
            results.push(result)
            
            // Update progress
            const progress: BatchProgress = {
              total: items.length,
              completed: results.length,
              failed: 0,
              percentage: (results.length / items.length) * 100,
              errors: []
            }
            progressCallback(progress)
          } catch (error) {
            this.logger.error(`Error processing item ${i}:`, error)
            results.push(null as any)
          }
        }
        return results
      },
      {
        batchSize: 10,
        onProgress: progressCallback,
        ...options
      }
    )
  }

  public async processWithRetry<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<string> {
    return this.submitJob(
      items,
      async (batch: T[]) => {
        const results: R[] = []
        
        for (const item of batch) {
          let lastError: Error | null = null
          let retryCount = 0
          
          while (retryCount <= maxRetries) {
            try {
              const result = await processor(item)
              results.push(result)
              break
            } catch (error) {
              lastError = error as Error
              retryCount++
              
              if (retryCount <= maxRetries) {
                await this.delay(retryDelay * retryCount)
              }
            }
          }
          
          if (lastError && retryCount > maxRetries) {
            this.logger.error(`Failed to process item after ${maxRetries} retries:`, lastError)
            results.push(null as any)
          }
        }
        
        return results
      },
      {
        retryCount: maxRetries,
        retryDelay
      }
    )
  }

  // Private methods
  private startProcessing(): void {
    if (this.isProcessing) return
    
    this.isProcessing = true
    this.processingTimer = setInterval(() => {
      this.processNextJob()
    }, 100)
  }

  private async processNextJob(): Promise<void> {
    if (this.activeJobs.size >= this.maxConcurrentJobs || this.jobQueue.length === 0) {
      return
    }

    const job = this.jobQueue.shift()
    if (!job) return

    this.activeJobs.set(job.id, job)
    job.status = 'running'
    job.startTime = new Date()

    try {
      // Check cache first
      if (job.options.enableCache && job.options.cacheKey && this.cacheManager) {
        const cachedResult = await this.cacheManager.get(job.options.cacheKey)
        if (cachedResult) {
          job.result = cachedResult
          job.status = 'completed'
          job.endTime = new Date()
          job.progress.completed = job.progress.total
          job.progress.percentage = 100
          
          if (job.options.onComplete) {
            job.options.onComplete(cachedResult)
          }
          
          this.completeJob(job)
          return
        }
      }

      const result = await this.processJobInBatches(job)
      job.result = result
      job.status = 'completed'
      job.endTime = new Date()
      job.progress.completed = job.progress.total
      job.progress.percentage = 100

      // Cache result if enabled
      if (job.options.enableCache && job.options.cacheKey && this.cacheManager) {
        await this.cacheManager.set(job.options.cacheKey, result, {
          ttl: job.options.cacheTtl || 60 * 60 * 1000 // 1 hour default
        })
      }

      if (job.options.onComplete) {
        job.options.onComplete(result)
      }

      this.logger.info(`Completed batch job ${job.id}`)
    } catch (error) {
      job.error = error as Error
      job.status = 'failed'
      job.endTime = new Date()
      
      if (job.options.onError) {
        job.options.onError(error as Error)
      }
      
      this.logger.error(`Failed batch job ${job.id}:`, error)
    }

    this.completeJob(job)
  }

  private async processJobInBatches<T, R>(job: BatchJob<T, R>): Promise<R[]> {
    const batchSize = job.options.batchSize || 10
    const maxConcurrency = job.options.maxConcurrency || 3
    const items = job.items
    const allResults: R[] = []

    const totalBatches = Math.ceil(items.length / batchSize)
    job.progress.totalBatches = totalBatches

    for (let i = 0; i < items.length; i += batchSize) {
      if (job.status === 'cancelled') {
        throw new Error('Job cancelled')
      }

      if (job.status === 'paused') {
        // Wait for resume
        while (job.status === 'paused') {
          await this.delay(100)
        }
      }

      const batch = items.slice(i, i + batchSize)
      const currentBatch = Math.floor(i / batchSize) + 1
      job.progress.currentBatch = currentBatch

      const startTime = Date.now()
      
      try {
        const batchResults = await this.processBatchWithTimeout(job, batch)
        allResults.push(...batchResults)
        
        const endTime = Date.now()
        const duration = endTime - startTime
        const itemsPerSecond = (batch.length / duration) * 1000
        
        job.progress.completed += batch.length
        job.progress.percentage = (job.progress.completed / job.progress.total) * 100
        job.progress.itemsPerSecond = itemsPerSecond
        
        // Calculate estimated time remaining
        const remainingItems = job.progress.total - job.progress.completed
        job.progress.estimatedTimeRemaining = remainingItems / itemsPerSecond * 1000

        if (job.options.onProgress) {
          job.options.onProgress(job.progress)
        }
      } catch (error) {
        job.progress.failed += batch.length
        job.progress.errors.push({
          itemIndex: i,
          error: error as Error,
          retryCount: 0,
          timestamp: new Date()
        })
        
        // Continue processing other batches
        this.logger.error(`Batch ${currentBatch} failed:`, error)
      }
    }

    return allResults
  }

  private async processBatchWithTimeout<T, R>(
    job: BatchJob<T, R>,
    batch: T[]
  ): Promise<R[]> {
    const timeout = job.options.timeout || 30000
    
    return new Promise((resolve, reject) => {
      const timeoutHandle = setTimeout(() => {
        reject(new Error(`Batch processing timed out after ${timeout}ms`))
      }, timeout)

      job.processor(batch)
        .then(result => {
          clearTimeout(timeoutHandle)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timeoutHandle)
          reject(error)
        })
    })
  }

  private completeJob(job: BatchJob<any, any>): void {
    // Keep completed jobs for a while for status checking
    setTimeout(() => {
      this.activeJobs.delete(job.id)
    }, 60000) // Keep for 1 minute
  }

  private sortJobQueue(): void {
    this.jobQueue.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
      const aPriority = priorityOrder[a.metadata.priority]
      const bPriority = priorityOrder[b.metadata.priority]
      
      if (aPriority !== bPriority) {
        return bPriority - aPriority
      }
      
      // If same priority, sort by creation time
      return a.metadata.createdAt.getTime() - b.metadata.createdAt.getTime()
    })
  }

  private generateJobId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  public dispose(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer)
      this.processingTimer = null
    }
    
    this.isProcessing = false
    
    // Cancel all active jobs
    for (const job of this.activeJobs.values()) {
      job.status = 'cancelled'
    }
    
    this.activeJobs.clear()
    this.jobQueue.length = 0
  }
}

// Specialized batch processors
export class FileIndexingBatchProcessor extends BatchProcessor {
  public async batchIndexFiles(
    filePaths: string[],
    indexer: (filePath: string) => Promise<any>,
    progressCallback?: (progress: BatchProgress) => void
  ): Promise<string> {
    return this.submitJob(
      filePaths,
      async (paths: string[]) => {
        const results = []
        for (const path of paths) {
          try {
            const result = await indexer(path)
            results.push({ path, result })
          } catch (error) {
            this.logger.error(`Error indexing file ${path}:`, error)
            results.push({ path, error: error.message })
          }
        }
        return results
      },
      {
        batchSize: 5,
        maxConcurrency: 2,
        priority: 'high',
        onProgress: progressCallback
      }
    )
  }
}

export class AnalysisBatchProcessor extends BatchProcessor {
  public async batchAnalyzeFiles(
    filePaths: string[],
    analyzer: (filePath: string) => Promise<any>,
    analysisType: string
  ): Promise<string> {
    return this.submitJob(
      filePaths,
      async (paths: string[]) => {
        const results = []
        for (const path of paths) {
          try {
            const result = await analyzer(path)
            results.push({ path, analysis: result, type: analysisType })
          } catch (error) {
            this.logger.error(`Error analyzing file ${path}:`, error)
            results.push({ path, error: error.message, type: analysisType })
          }
        }
        return results
      },
      {
        batchSize: 3,
        maxConcurrency: 2,
        priority: 'medium',
        enableCache: true,
        cacheKey: `analysis_${analysisType}_batch`,
        cacheTtl: 30 * 60 * 1000 // 30 minutes
      }
    )
  }
}

export class EmbeddingBatchProcessor extends BatchProcessor {
  public async batchGenerateEmbeddings(
    texts: string[],
    embeddingGenerator: (text: string) => Promise<number[]>
  ): Promise<string> {
    return this.submitJob(
      texts,
      async (textBatch: string[]) => {
        const results = []
        for (const text of textBatch) {
          try {
            const embedding = await embeddingGenerator(text)
            results.push({ text, embedding })
          } catch (error) {
            this.logger.error(`Error generating embedding for text:`, error)
            results.push({ text, error: error.message })
          }
        }
        return results
      },
      {
        batchSize: 10,
        maxConcurrency: 3,
        priority: 'medium',
        enableCache: true,
        cacheKey: 'embeddings_batch',
        cacheTtl: 60 * 60 * 1000 // 1 hour
      }
    )
  }
}