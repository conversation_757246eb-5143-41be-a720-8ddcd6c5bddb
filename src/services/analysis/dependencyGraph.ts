import { ConnectionManager } from '../database/connectionManager'
import { FileSystemService } from '../fileSystemService'
import { TypeScriptAnalyzer, TypeScriptImport, TypeScriptExport } from './typescriptAnalyzer'

export interface DependencyNode {
  id: string
  filePath: string
  relativePath: string
  name: string
  type: 'source' | 'module' | 'type_only'
  imports: string[]
  exports: string[]
  dependents: string[]
  dependencies: string[]
  level: number
  isEntryPoint: boolean
  isLeaf: boolean
  cycleId?: string
}

export interface DependencyEdge {
  from: string
  to: string
  type: 'static' | 'dynamic' | 'type_only'
  importNames: string[]
  line: number
  isLocal: boolean
  strength: number
}

export interface DependencyGraph {
  nodes: Map<string, DependencyNode>
  edges: DependencyEdge[]
  entryPoints: string[]
  leaves: string[]
  cycles: string[][]
  layers: Map<number, string[]>
  criticalPath: string[]
  statistics: DependencyStatistics
}

export interface DependencyStatistics {
  totalNodes: number
  totalEdges: number
  avgDependencies: number
  maxDependencies: number
  avgDependents: number
  maxDependents: number
  cycleCount: number
  layerCount: number
  complexityScore: number
}

export interface DependencyAnalysis {
  graph: DependencyGraph
  issues: DependencyIssue[]
  recommendations: string[]
  metrics: DependencyMetrics
}

export interface DependencyIssue {
  type: 'circular' | 'orphan' | 'heavy' | 'coupling' | 'unused'
  severity: 'low' | 'medium' | 'high'
  nodes: string[]
  description: string
  suggestion: string
}

export interface DependencyMetrics {
  instability: Map<string, number>
  abstractness: Map<string, number>
  distance: Map<string, number>
  coupling: Map<string, number>
  cohesion: Map<string, number>
}

export class DependencyGraphBuilder {
  private db: ConnectionManager
  private fileSystem: FileSystemService
  private tsAnalyzer: TypeScriptAnalyzer
  private graph: DependencyGraph
  private projectRoot: string = ''

  constructor(
    db: ConnectionManager,
    fileSystem: FileSystemService,
    tsAnalyzer: TypeScriptAnalyzer
  ) {
    this.db = db
    this.fileSystem = fileSystem
    this.tsAnalyzer = tsAnalyzer
    this.graph = {
      nodes: new Map(),
      edges: [],
      entryPoints: [],
      leaves: [],
      cycles: [],
      layers: new Map(),
      criticalPath: [],
      statistics: {
        totalNodes: 0,
        totalEdges: 0,
        avgDependencies: 0,
        maxDependencies: 0,
        avgDependents: 0,
        maxDependents: 0,
        cycleCount: 0,
        layerCount: 0,
        complexityScore: 0
      }
    }
  }

  async buildGraph(projectRoot: string, filePaths: string[]): Promise<DependencyGraph> {
    this.projectRoot = projectRoot
    this.graph.nodes.clear()
    this.graph.edges = []

    await this.createNodes(filePaths)
    await this.createEdges()
    await this.analyzeGraph()
    await this.calculateStatistics()

    return this.graph
  }

  private async createNodes(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      const relativePath = this.fileSystem.relative(this.projectRoot, filePath)
      const name = this.fileSystem.basename(filePath)
      const nodeId = this.generateNodeId(filePath)

      const node: DependencyNode = {
        id: nodeId,
        filePath,
        relativePath,
        name,
        type: 'source',
        imports: [],
        exports: [],
        dependents: [],
        dependencies: [],
        level: 0,
        isEntryPoint: false,
        isLeaf: false
      }

      this.graph.nodes.set(nodeId, node)
    }
  }

  private async createEdges(): Promise<void> {
    const analysisResults = await this.tsAnalyzer.analyzeProject(
      this.projectRoot,
      Array.from(this.graph.nodes.values()).map(node => node.filePath)
    )

    for (const [filePath, analysis] of analysisResults) {
      const fromNodeId = this.generateNodeId(filePath)
      const fromNode = this.graph.nodes.get(fromNodeId)
      
      if (!fromNode) continue

      for (const importInfo of analysis.imports) {
        const resolvedPath = await this.resolveImportPath(filePath, importInfo.module)
        
        if (resolvedPath) {
          const toNodeId = this.generateNodeId(resolvedPath)
          const toNode = this.graph.nodes.get(toNodeId)
          
          if (toNode) {
            const edge: DependencyEdge = {
              from: fromNodeId,
              to: toNodeId,
              type: importInfo.isTypeOnly ? 'type_only' : 'static',
              importNames: importInfo.namedImports,
              line: importInfo.line,
              isLocal: importInfo.isLocal,
              strength: this.calculateEdgeStrength(importInfo)
            }

            this.graph.edges.push(edge)
            fromNode.dependencies.push(toNodeId)
            toNode.dependents.push(fromNodeId)
          }
        }
      }

      // Update imports and exports
      fromNode.imports = analysis.imports.map(imp => imp.module)
      fromNode.exports = analysis.exports.map(exp => exp.name)
    }
  }

  private async resolveImportPath(fromFile: string, importPath: string): Promise<string | null> {
    if (!importPath.startsWith('./') && !importPath.startsWith('../')) {
      return null // External module
    }

    try {
      const fromDir = this.fileSystem.dirname(fromFile)
      const resolvedPath = this.fileSystem.resolve(fromDir, importPath)
      
      // Try different extensions
      const extensions = ['.ts', '.tsx', '.js', '.jsx']
      
      for (const ext of extensions) {
        const pathWithExt = resolvedPath + ext
        if (await this.fileSystem.exists(pathWithExt)) {
          return pathWithExt
        }
      }

      // Try index files
      for (const ext of extensions) {
        const indexPath = this.fileSystem.joinPath(resolvedPath, `index${ext}`)
        if (await this.fileSystem.exists(indexPath)) {
          return indexPath
        }
      }

      return null
    } catch {
      return null
    }
  }

  private calculateEdgeStrength(importInfo: TypeScriptImport): number {
    let strength = 0.5

    if (importInfo.isTypeOnly) {
      strength = 0.2
    } else if (importInfo.defaultImport) {
      strength += 0.3
    }

    strength += importInfo.namedImports.length * 0.1
    
    if (importInfo.namespaceImport) {
      strength += 0.4
    }

    return Math.min(strength, 1.0)
  }

  private async analyzeGraph(): Promise<void> {
    await this.detectCycles()
    await this.identifyEntryPoints()
    await this.identifyLeaves()
    await this.calculateLayers()
    await this.findCriticalPath()
  }

  private async detectCycles(): Promise<void> {
    const visited = new Set<string>()
    const recStack = new Set<string>()
    const cycles: string[][] = []

    const dfs = (nodeId: string, path: string[]): void => {
      if (recStack.has(nodeId)) {
        const cycleStart = path.indexOf(nodeId)
        if (cycleStart !== -1) {
          const cycle = path.slice(cycleStart)
          cycles.push([...cycle, nodeId])
        }
        return
      }

      if (visited.has(nodeId)) {
        return
      }

      visited.add(nodeId)
      recStack.add(nodeId)
      path.push(nodeId)

      const node = this.graph.nodes.get(nodeId)
      if (node) {
        for (const depId of node.dependencies) {
          dfs(depId, path)
        }
      }

      path.pop()
      recStack.delete(nodeId)
    }

    for (const nodeId of this.graph.nodes.keys()) {
      if (!visited.has(nodeId)) {
        dfs(nodeId, [])
      }
    }

    this.graph.cycles = cycles

    // Mark nodes that are part of cycles
    const cycleNodes = new Set<string>()
    cycles.forEach((cycle, index) => {
      cycle.forEach(nodeId => {
        cycleNodes.add(nodeId)
        const node = this.graph.nodes.get(nodeId)
        if (node) {
          node.cycleId = `cycle_${index}`
        }
      })
    })
  }

  private async identifyEntryPoints(): Promise<void> {
    const entryPoints: string[] = []
    
    for (const [nodeId, node] of this.graph.nodes) {
      if (node.dependents.length === 0 && node.dependencies.length > 0) {
        entryPoints.push(nodeId)
        node.isEntryPoint = true
      }
    }

    this.graph.entryPoints = entryPoints
  }

  private async identifyLeaves(): Promise<void> {
    const leaves: string[] = []
    
    for (const [nodeId, node] of this.graph.nodes) {
      if (node.dependencies.length === 0 && node.dependents.length > 0) {
        leaves.push(nodeId)
        node.isLeaf = true
      }
    }

    this.graph.leaves = leaves
  }

  private async calculateLayers(): Promise<void> {
    const layers = new Map<number, string[]>()
    const visited = new Set<string>()

    const calculateLevel = (nodeId: string): number => {
      if (visited.has(nodeId)) {
        return this.graph.nodes.get(nodeId)?.level || 0
      }

      visited.add(nodeId)
      const node = this.graph.nodes.get(nodeId)
      if (!node) return 0

      if (node.dependencies.length === 0) {
        node.level = 0
        return 0
      }

      let maxLevel = 0
      for (const depId of node.dependencies) {
        const depLevel = calculateLevel(depId)
        maxLevel = Math.max(maxLevel, depLevel)
      }

      node.level = maxLevel + 1
      return node.level
    }

    for (const nodeId of this.graph.nodes.keys()) {
      const level = calculateLevel(nodeId)
      if (!layers.has(level)) {
        layers.set(level, [])
      }
      layers.get(level)!.push(nodeId)
    }

    this.graph.layers = layers
  }

  private async findCriticalPath(): Promise<void> {
    let longestPath: string[] = []
    let maxLength = 0

    const findLongestPath = (nodeId: string, path: string[], visited: Set<string>): void => {
      if (visited.has(nodeId)) {
        return
      }

      visited.add(nodeId)
      path.push(nodeId)

      const node = this.graph.nodes.get(nodeId)
      if (node && node.dependencies.length === 0) {
        if (path.length > maxLength) {
          maxLength = path.length
          longestPath = [...path]
        }
      } else if (node) {
        for (const depId of node.dependencies) {
          findLongestPath(depId, path, visited)
        }
      }

      path.pop()
      visited.delete(nodeId)
    }

    for (const entryPoint of this.graph.entryPoints) {
      findLongestPath(entryPoint, [], new Set())
    }

    this.graph.criticalPath = longestPath
  }

  private async calculateStatistics(): Promise<void> {
    const stats = this.graph.statistics
    
    stats.totalNodes = this.graph.nodes.size
    stats.totalEdges = this.graph.edges.length
    stats.cycleCount = this.graph.cycles.length
    stats.layerCount = this.graph.layers.size

    const dependencies = Array.from(this.graph.nodes.values()).map(node => node.dependencies.length)
    const dependents = Array.from(this.graph.nodes.values()).map(node => node.dependents.length)

    stats.avgDependencies = dependencies.reduce((sum, count) => sum + count, 0) / dependencies.length
    stats.maxDependencies = Math.max(...dependencies)
    stats.avgDependents = dependents.reduce((sum, count) => sum + count, 0) / dependents.length
    stats.maxDependents = Math.max(...dependents)

    stats.complexityScore = this.calculateComplexityScore()
  }

  private calculateComplexityScore(): number {
    const stats = this.graph.statistics
    let score = 0

    // Base complexity from node and edge counts
    score += stats.totalNodes * 0.1
    score += stats.totalEdges * 0.2

    // Penalty for cycles
    score += stats.cycleCount * 5

    // Penalty for high coupling
    score += stats.maxDependencies * 0.5
    score += stats.maxDependents * 0.5

    // Penalty for deep nesting
    score += stats.layerCount * 0.3

    return Math.round(score)
  }

  async analyzeDependencies(): Promise<DependencyAnalysis> {
    if (this.graph.nodes.size === 0) {
      throw new Error('Graph not built. Call buildGraph first.')
    }

    const issues = await this.identifyIssues()
    const recommendations = await this.generateRecommendations(issues)
    const metrics = await this.calculateMetrics()

    return {
      graph: this.graph,
      issues,
      recommendations,
      metrics
    }
  }

  private async identifyIssues(): Promise<DependencyIssue[]> {
    const issues: DependencyIssue[] = []

    // Circular dependencies
    for (const cycle of this.graph.cycles) {
      issues.push({
        type: 'circular',
        severity: cycle.length > 3 ? 'high' : 'medium',
        nodes: cycle,
        description: `Circular dependency detected involving ${cycle.length} files`,
        suggestion: 'Break the cycle by extracting common dependencies or using dependency injection'
      })
    }

    // Heavy dependencies
    for (const [nodeId, node] of this.graph.nodes) {
      if (node.dependencies.length > 10) {
        issues.push({
          type: 'heavy',
          severity: node.dependencies.length > 20 ? 'high' : 'medium',
          nodes: [nodeId],
          description: `File has ${node.dependencies.length} dependencies`,
          suggestion: 'Consider breaking this file into smaller modules'
        })
      }
    }

    // Orphan files
    for (const [nodeId, node] of this.graph.nodes) {
      if (node.dependents.length === 0 && node.dependencies.length === 0) {
        issues.push({
          type: 'orphan',
          severity: 'low',
          nodes: [nodeId],
          description: 'File has no dependencies or dependents',
          suggestion: 'Consider removing if unused or adding proper imports/exports'
        })
      }
    }

    return issues
  }

  private async generateRecommendations(issues: DependencyIssue[]): Promise<string[]> {
    const recommendations: string[] = []
    
    const cycleCount = issues.filter(i => i.type === 'circular').length
    const heavyCount = issues.filter(i => i.type === 'heavy').length
    const orphanCount = issues.filter(i => i.type === 'orphan').length

    if (cycleCount > 0) {
      recommendations.push(`Resolve ${cycleCount} circular dependencies to improve maintainability`)
    }

    if (heavyCount > 0) {
      recommendations.push(`Refactor ${heavyCount} files with excessive dependencies`)
    }

    if (orphanCount > 0) {
      recommendations.push(`Review ${orphanCount} orphan files for removal or proper integration`)
    }

    if (this.graph.layers.size > 10) {
      recommendations.push('Consider flattening the dependency hierarchy')
    }

    return recommendations
  }

  private async calculateMetrics(): Promise<DependencyMetrics> {
    const instability = new Map<string, number>()
    const abstractness = new Map<string, number>()
    const distance = new Map<string, number>()
    const coupling = new Map<string, number>()
    const cohesion = new Map<string, number>()

    for (const [nodeId, node] of this.graph.nodes) {
      // Instability = Ce / (Ca + Ce)
      const efferent = node.dependencies.length
      const afferent = node.dependents.length
      const instabilityValue = efferent / (afferent + efferent) || 0
      instability.set(nodeId, instabilityValue)

      // Abstractness (simplified)
      const abstractnessValue = node.exports.length / (node.exports.length + 1)
      abstractness.set(nodeId, abstractnessValue)

      // Distance from main sequence
      const distanceValue = Math.abs(abstractnessValue + instabilityValue - 1)
      distance.set(nodeId, distanceValue)

      // Coupling
      const couplingValue = (efferent + afferent) / this.graph.nodes.size
      coupling.set(nodeId, couplingValue)

      // Cohesion (simplified)
      const cohesionValue = 1 - (node.dependencies.length / this.graph.nodes.size)
      cohesion.set(nodeId, cohesionValue)
    }

    return {
      instability,
      abstractness,
      distance,
      coupling,
      cohesion
    }
  }

  private generateNodeId(filePath: string): string {
    return filePath.replace(/[^a-zA-Z0-9]/g, '_')
  }

  getDependencyGraph(): DependencyGraph {
    return this.graph
  }

  async exportGraphToJson(): Promise<string> {
    const exportData = {
      nodes: Array.from(this.graph.nodes.values()),
      edges: this.graph.edges,
      statistics: this.graph.statistics,
      entryPoints: this.graph.entryPoints,
      leaves: this.graph.leaves,
      cycles: this.graph.cycles
    }

    return JSON.stringify(exportData, null, 2)
  }
}