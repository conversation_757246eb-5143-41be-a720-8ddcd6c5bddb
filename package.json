{"name": "natural-language-task-transformer", "displayName": "Natural Language to Structured Task Transformation", "description": "Transform informal developer instructions into structured, actionable engineering tasks using AI, context-aware templates, and intelligent learning.", "version": "0.0.1", "engines": {"vscode": "^1.82.0"}, "categories": ["Other"], "activationEvents": ["onCommand:taskTransformer.transform", "onCommand:taskTransformer.suggest", "onCommand:taskTransformer.configure"], "main": "./out/extension-minimal.js", "contributes": {"commands": [{"command": "taskTransformer.transform", "title": "Transform Natural Language to Tasks", "category": "Task Transformer"}, {"command": "taskTransformer.suggest", "title": "Get Context-Aware Suggestions", "category": "Task Transformer"}, {"command": "taskTransformer.configure", "title": "Configure Task Transformer", "category": "Task Transformer"}], "menus": {"editor/context": [{"when": "editorTextFocus", "command": "taskTransformer.transform", "group": "navigation"}, {"when": "editorHasSelection", "command": "taskTransformer.suggest", "group": "navigation"}], "commandPalette": [{"command": "taskTransformer.transform", "when": "editorIsOpen"}, {"command": "taskTransformer.suggest", "when": "editorIsOpen"}]}, "configuration": {"title": "Task Transformer", "properties": {"taskTransformer.aiProvider": {"type": "string", "enum": ["openai", "claude"], "default": "openai", "description": "AI service provider for task transformation"}, "taskTransformer.model": {"type": "string", "default": "gpt-4", "description": "AI model to use for task transformation"}, "taskTransformer.maxTokens": {"type": "number", "default": 4000, "description": "Maximum tokens for AI responses"}, "taskTransformer.temperature": {"type": "number", "default": 0.7, "minimum": 0, "maximum": 1, "description": "AI response creativity (0=focused, 1=creative)"}, "taskTransformer.enableLearning": {"type": "boolean", "default": true, "description": "Enable adaptive learning from user interactions"}, "taskTransformer.indexingEnabled": {"type": "boolean", "default": true, "description": "Enable automatic codebase indexing"}, "taskTransformer.maxIndexFiles": {"type": "number", "default": 10000, "description": "Maximum number of files to index"}, "taskTransformer.excludePatterns": {"type": "array", "items": {"type": "string"}, "default": ["node_modules", ".git", "dist", "build", "*.log"], "description": "File patterns to exclude from indexing"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/node": "20.x", "@types/vscode": "^1.82.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.4.1", "@vscode/test-electron": "^2.3.4", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.57.1", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.32.0", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.6.0", "ts-loader": "^9.4.4", "typescript": "^5.8.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.4"}, "dependencies": {"@anthropic-ai/sdk": "^0.17.0", "@lancedb/lancedb": "^0.5.0", "axios": "^1.6.0", "chokidar": "^3.5.3", "crypto-js": "^4.1.1", "dt-python-parser": "^0.9.0", "handlebars": "^4.7.8", "lodash": "^4.17.21", "openai": "^4.0.0", "sql.js": "^1.8.0"}}