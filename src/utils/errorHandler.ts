// Utility function to convert unknown errors to Error type
export function toError(error: unknown): Error {
  if (error instanceof Error) {
    return error;
  }
  if (typeof error === "string") {
    return new Error(error);
  }
  if (error && typeof error === "object" && "message" in error) {
    return new Error(String(error.message));
  }
  return new Error("Unknown error occurred");
}

export enum ErrorCode {
  UNKNOWN = "UNKNOWN",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  API_ERROR = "API_ERROR",
  FILE_SYSTEM_ERROR = "FILE_SYSTEM_ERROR",
  DATABASE_ERROR = "DATABASE_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR",
  PERMISSION_ERROR = "PERMISSION_ERROR",
  CONFIGURATION_ERROR = "CONFIGURATION_ERROR",
  TIMEOUT_ERROR = "TIMEOUT_ERROR",
}

export interface ErrorDetails {
  code: ErrorCode;
  message: string;
  originalError?: Error;
  context?: Record<string, any>;
  timestamp: Date;
  stack?: string;
}

export class ExtensionError extends Error {
  public readonly code: ErrorCode;
  public readonly context?: Record<string, any>;
  public readonly timestamp: Date;
  public readonly originalError?: Error;

  constructor(details: Omit<ErrorDetails, "timestamp">) {
    super(details.message);
    this.name = "ExtensionError";
    this.code = details.code;
    this.context = details.context;
    this.timestamp = new Date();
    this.originalError = details.originalError;

    if (details.stack) {
      this.stack = details.stack;
    } else if (details.originalError?.stack) {
      this.stack = details.originalError.stack;
    }
  }
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorListeners: ((error: ExtensionError) => void)[] = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  addErrorListener(listener: (error: ExtensionError) => void): void {
    this.errorListeners.push(listener);
  }

  removeErrorListener(listener: (error: ExtensionError) => void): void {
    const index = this.errorListeners.indexOf(listener);
    if (index > -1) {
      this.errorListeners.splice(index, 1);
    }
  }

  handleError(error: Error | ExtensionError, context?: Record<string, any>): ExtensionError {
    let extensionError: ExtensionError;

    if (error instanceof ExtensionError) {
      extensionError = error;
    } else {
      extensionError = new ExtensionError({
        code: this.categorizeError(error),
        message: error.message,
        originalError: error,
        context,
        stack: error.stack,
      });
    }

    this.notifyListeners(extensionError);
    return extensionError;
  }

  createError(code: ErrorCode, message: string, context?: Record<string, any>, originalError?: Error): ExtensionError {
    const error = new ExtensionError({
      code,
      message,
      context,
      originalError,
    });

    this.notifyListeners(error);
    return error;
  }

  private categorizeError(error: Error): ErrorCode {
    const message = error.message.toLowerCase();

    if (message.includes("validation") || message.includes("invalid")) {
      return ErrorCode.VALIDATION_ERROR;
    }
    if (message.includes("api") || message.includes("response")) {
      return ErrorCode.API_ERROR;
    }
    if (message.includes("file") || message.includes("directory") || message.includes("path")) {
      return ErrorCode.FILE_SYSTEM_ERROR;
    }
    if (message.includes("database") || message.includes("sqlite")) {
      return ErrorCode.DATABASE_ERROR;
    }
    if (message.includes("network") || message.includes("fetch") || message.includes("request")) {
      return ErrorCode.NETWORK_ERROR;
    }
    if (message.includes("auth") || message.includes("token") || message.includes("key")) {
      return ErrorCode.AUTHENTICATION_ERROR;
    }
    if (message.includes("permission") || message.includes("access")) {
      return ErrorCode.PERMISSION_ERROR;
    }
    if (message.includes("config") || message.includes("setting")) {
      return ErrorCode.CONFIGURATION_ERROR;
    }
    if (message.includes("timeout")) {
      return ErrorCode.TIMEOUT_ERROR;
    }

    return ErrorCode.UNKNOWN;
  }

  private notifyListeners(error: ExtensionError): void {
    this.errorListeners.forEach((listener) => {
      try {
        listener(error);
      } catch (listenerError) {
        console.error("Error in error listener:", listenerError);
      }
    });
  }
}

export const errorHandler = ErrorHandler.getInstance();

export function withErrorHandling<T extends any[], R>(fn: (...args: T) => R, context?: Record<string, any>): (...args: T) => R {
  return (...args: T): R => {
    try {
      return fn(...args);
    } catch (error) {
      throw errorHandler.handleError(error instanceof Error ? error : new Error(String(error)), context);
    }
  };
}

export function withAsyncErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: Record<string, any>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw errorHandler.handleError(error instanceof Error ? error : new Error(String(error)), context);
    }
  };
}
