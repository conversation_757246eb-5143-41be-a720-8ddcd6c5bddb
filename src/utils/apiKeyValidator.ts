import { ErrorCode, ExtensionError } from './errorHandler'
import { InputSanitizer } from './sanitizer'
import { logger } from './logger'

export interface ApiKeyValidation {
  isValid: boolean
  provider: string
  maskedKey: string
  errors: string[]
  warnings: string[]
}

export interface ApiKeyPattern {
  provider: string
  pattern: RegExp
  minLength: number
  maxLength: number
  prefix?: string
  description: string
}

export class ApiKeyValidator {
  private static readonly API_KEY_PATTERNS: ApiKeyPattern[] = [
    {
      provider: 'OpenAI',
      pattern: /^sk-[a-zA-Z0-9]{48}$/,
      minLength: 51,
      maxLength: 51,
      prefix: 'sk-',
      description: 'OpenAI API key (sk-...)'
    },
    {
      provider: 'Anthropic',
      pattern: /^sk-ant-[a-zA-Z0-9\-_]{95}$/,
      minLength: 103,
      maxLength: 103,
      prefix: 'sk-ant-',
      description: 'Anthropic API key (sk-ant-...)'
    },
    {
      provider: 'Azure OpenAI',
      pattern: /^[a-zA-Z0-9]{32}$/,
      minLength: 32,
      maxLength: 32,
      description: 'Azure OpenAI API key (32 characters)'
    },
    {
      provider: 'Cohere',
      pattern: /^[a-zA-Z0-9\-_]{40}$/,
      minLength: 40,
      maxLength: 40,
      description: 'Cohere API key (40 characters)'
    },
    {
      provider: 'Hugging Face',
      pattern: /^hf_[a-zA-Z0-9]{34}$/,
      minLength: 37,
      maxLength: 37,
      prefix: 'hf_',
      description: 'Hugging Face API key (hf_...)'
    }
  ]

  static validateApiKey(apiKey: string, expectedProvider?: string): ApiKeyValidation {
    const validation: ApiKeyValidation = {
      isValid: false,
      provider: 'Unknown',
      maskedKey: '',
      errors: [],
      warnings: []
    }

    // Sanitize the API key
    const sanitized = InputSanitizer.sanitizeApiKey(apiKey)
    if (!sanitized.isValid) {
      validation.errors.push('API key contains invalid characters')
      validation.errors.push(...sanitized.violations)
      return validation
    }

    const cleanKey = sanitized.sanitized

    // Check if key is empty
    if (!cleanKey) {
      validation.errors.push('API key is required')
      return validation
    }

    // Check minimum length
    if (cleanKey.length < 8) {
      validation.errors.push('API key is too short')
      return validation
    }

    // Check maximum length
    if (cleanKey.length > 200) {
      validation.errors.push('API key is too long')
      return validation
    }

    // Mask the key for logging
    validation.maskedKey = this.maskApiKey(cleanKey)

    // Try to match against known patterns
    let matchedPattern: ApiKeyPattern | null = null
    for (const pattern of this.API_KEY_PATTERNS) {
      if (pattern.pattern.test(cleanKey)) {
        matchedPattern = pattern
        break
      }
    }

    if (matchedPattern) {
      validation.provider = matchedPattern.provider
      validation.isValid = true

      // Check if provider matches expected
      if (expectedProvider && matchedPattern.provider !== expectedProvider) {
        validation.warnings.push(
          `API key appears to be for ${matchedPattern.provider} but ${expectedProvider} was expected`
        )
      }
    } else {
      // Check if it could be a valid key format but not recognized
      const hasValidChars = /^[a-zA-Z0-9\-_]+$/.test(cleanKey)
      const hasReasonableLength = cleanKey.length >= 16 && cleanKey.length <= 200

      if (hasValidChars && hasReasonableLength) {
        validation.provider = 'Generic'
        validation.isValid = true
        validation.warnings.push('API key format not recognized, but appears valid')
      } else {
        validation.errors.push('API key format not recognized')
      }
    }

    return validation
  }

  static maskApiKey(apiKey: string): string {
    if (!apiKey) return ''
    
    if (apiKey.length <= 8) {
      return '*'.repeat(apiKey.length)
    }
    
    const start = apiKey.substring(0, 4)
    const end = apiKey.substring(apiKey.length - 4)
    const middle = '*'.repeat(Math.min(apiKey.length - 8, 20))
    
    return `${start}${middle}${end}`
  }

  static validateAndStoreApiKey(apiKey: string, provider: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        const validation = this.validateApiKey(apiKey, provider)
        
        if (!validation.isValid) {
          const error = new ExtensionError({
            code: ErrorCode.VALIDATION_ERROR,
            message: `Invalid API key: ${validation.errors.join(', ')}`,
            context: { provider, maskedKey: validation.maskedKey }
          })
          reject(error)
          return
        }

        // Log warnings if any
        if (validation.warnings.length > 0) {
          logger.warn('API key validation warnings', {
            provider,
            warnings: validation.warnings,
            maskedKey: validation.maskedKey
          })
        }

        logger.info('API key validated successfully', {
          provider: validation.provider,
          maskedKey: validation.maskedKey
        })

        resolve(true)
      } catch (error) {
        logger.error('API key validation failed', error as Error)
        reject(new ExtensionError({
          code: ErrorCode.VALIDATION_ERROR,
          message: 'API key validation failed',
          originalError: error as Error
        }))
      }
    })
  }

  static getProviderFromApiKey(apiKey: string): string {
    const validation = this.validateApiKey(apiKey)
    return validation.provider
  }

  static getSupportedProviders(): string[] {
    return this.API_KEY_PATTERNS.map(pattern => pattern.provider)
  }

  static getProviderDescription(provider: string): string {
    const pattern = this.API_KEY_PATTERNS.find(p => p.provider === provider)
    return pattern ? pattern.description : 'Unknown provider'
  }

  static isKeyExpired(apiKey: string, lastUsed: Date): boolean {
    // Simple heuristic: if key hasn't been used in 30 days, consider it potentially expired
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    return lastUsed < thirtyDaysAgo
  }

  static generateKeyStrengthScore(apiKey: string): number {
    let score = 0
    
    // Length score (0-40 points)
    score += Math.min(apiKey.length, 40)
    
    // Character variety score (0-30 points)
    const hasLowercase = /[a-z]/.test(apiKey)
    const hasUppercase = /[A-Z]/.test(apiKey)
    const hasNumbers = /[0-9]/.test(apiKey)
    const hasSpecialChars = /[_\-]/.test(apiKey)
    
    if (hasLowercase) score += 7
    if (hasUppercase) score += 7
    if (hasNumbers) score += 8
    if (hasSpecialChars) score += 8
    
    // Entropy score (0-30 points)
    const uniqueChars = new Set(apiKey).size
    score += Math.min(uniqueChars, 30)
    
    return Math.min(score, 100)
  }

  static validateKeyRotation(currentKey: string, newKey: string): ApiKeyValidation {
    const currentValidation = this.validateApiKey(currentKey)
    const newValidation = this.validateApiKey(newKey)
    
    const rotationValidation: ApiKeyValidation = {
      isValid: false,
      provider: newValidation.provider,
      maskedKey: newValidation.maskedKey,
      errors: [],
      warnings: []
    }
    
    // Check if new key is valid
    if (!newValidation.isValid) {
      rotationValidation.errors.push(...newValidation.errors)
      return rotationValidation
    }
    
    // Check if keys are different
    if (currentKey === newKey) {
      rotationValidation.errors.push('New API key must be different from current key')
      return rotationValidation
    }
    
    // Check if providers match
    if (currentValidation.provider !== 'Unknown' && 
        newValidation.provider !== 'Unknown' && 
        currentValidation.provider !== newValidation.provider) {
      rotationValidation.warnings.push(
        `Provider changed from ${currentValidation.provider} to ${newValidation.provider}`
      )
    }
    
    rotationValidation.isValid = true
    return rotationValidation
  }
}