import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'

export interface ProgressState {
  id: string
  title: string
  description?: string
  progress: number
  total: number
  status: ProgressStatus
  startTime: Date
  endTime?: Date
  duration?: number
  cancellable: boolean
  cancelled: boolean
  error?: ProgressError
  metadata: ProgressMetadata
  steps: ProgressStep[]
  currentStep?: string
}

export interface ProgressStatus {
  type: 'pending' | 'running' | 'completed' | 'cancelled' | 'error' | 'paused'
  message?: string
  percentage: number
  estimatedTimeRemaining?: number
  throughput?: number
}

export interface ProgressError {
  message: string
  code: string
  details?: string
  recoverable: boolean
  retryCount: number
  maxRetries: number
}

export interface ProgressMetadata {
  category: 'indexing' | 'analysis' | 'transformation' | 'template' | 'ai' | 'export' | 'import' | 'sync' | 'other'
  priority: 'low' | 'medium' | 'high' | 'critical'
  source: string
  userId?: string
  workspaceId?: string
  tags: string[]
  context: Record<string, any>
}

export interface ProgressStep {
  id: string
  title: string
  description?: string
  weight: number
  progress: number
  status: 'pending' | 'running' | 'completed' | 'skipped' | 'error'
  startTime?: Date
  endTime?: Date
  duration?: number
  error?: string
  substeps: ProgressSubstep[]
}

export interface ProgressSubstep {
  id: string
  title: string
  progress: number
  status: 'pending' | 'running' | 'completed' | 'error'
  message?: string
}

export interface ProgressOptions {
  location: ProgressLocation
  title: string
  description?: string
  cancellable?: boolean
  modal?: boolean
  showDetails?: boolean
  showEstimate?: boolean
  showThroughput?: boolean
  autoClose?: boolean
  autoCloseDelay?: number
  persistHistory?: boolean
  category?: string
  priority?: 'low' | 'medium' | 'high' | 'critical'
  steps?: string[]
  metadata?: Record<string, any>
}

export interface ProgressLocation {
  type: 'notification' | 'statusBar' | 'window' | 'modal' | 'sidebar' | 'webview'
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center'
  duration?: number
  sticky?: boolean
}

export interface ProgressTheme {
  primaryColor: string
  backgroundColor: string
  textColor: string
  successColor: string
  errorColor: string
  warningColor: string
  borderRadius: number
  fontSize: number
  animation: boolean
}

export interface ProgressHistory {
  id: string
  state: ProgressState
  timestamp: Date
  outcome: 'success' | 'failure' | 'cancelled'
  duration: number
  metadata: Record<string, any>
}

export interface ProgressAnalytics {
  totalOperations: number
  successRate: number
  averageDuration: number
  commonErrors: Map<string, number>
  performanceMetrics: PerformanceMetrics
  categoryBreakdown: Map<string, number>
}

export interface PerformanceMetrics {
  fastestOperation: number
  slowestOperation: number
  medianDuration: number
  p95Duration: number
  p99Duration: number
  throughputTrend: number[]
}

export class ProgressManager {
  private activeProgress: Map<string, ProgressState> = new Map()
  private progressHistory: ProgressHistory[] = []
  private statusBarItem: vscode.StatusBarItem
  private webviewPanel: vscode.WebviewPanel | undefined
  private disposables: vscode.Disposable[] = []
  private theme: ProgressTheme
  private maxHistorySize = 100

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {
    this.theme = this.getDefaultTheme()
    this.setupStatusBarItem()
    this.loadHistory()
    this.registerCommands()
  }

  private setupStatusBarItem(): void {
    this.statusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Left,
      100
    )
    this.statusBarItem.command = 'progressIndicator.showDetails'
    this.context.subscriptions.push(this.statusBarItem)
  }

  private registerCommands(): void {
    const commands = [
      vscode.commands.registerCommand('progressIndicator.showDetails', () => {
        this.showProgressWebview()
      }),
      
      vscode.commands.registerCommand('progressIndicator.showHistory', () => {
        this.showHistoryWebview()
      }),
      
      vscode.commands.registerCommand('progressIndicator.clearHistory', () => {
        this.clearHistory()
      }),
      
      vscode.commands.registerCommand('progressIndicator.cancelAll', () => {
        this.cancelAllProgress()
      }),
      
      vscode.commands.registerCommand('progressIndicator.exportReport', () => {
        this.exportProgressReport()
      })
    ]

    this.disposables.push(...commands)
  }

  // Main progress management methods
  public async withProgress<T>(
    options: ProgressOptions,
    task: (progress: ProgressReporter, token: vscode.CancellationToken) => Thenable<T>
  ): Promise<T> {
    const progressId = this.generateProgressId()
    const state = this.createProgressState(progressId, options)
    
    this.activeProgress.set(progressId, state)
    this.updateDisplay()

    const cancellationTokenSource = new vscode.CancellationTokenSource()
    const reporter = new ProgressReporter(progressId, this)

    try {
      state.status.type = 'running'
      state.startTime = new Date()
      this.updateDisplay()

      const result = await task(reporter, cancellationTokenSource.token)

      state.status.type = 'completed'
      state.status.percentage = 100
      state.endTime = new Date()
      state.duration = state.endTime.getTime() - state.startTime.getTime()
      
      this.addToHistory(state, 'success')
      this.updateDisplay()

      // Auto-close if configured
      if (options.autoClose !== false) {
        setTimeout(() => {
          this.removeProgress(progressId)
        }, options.autoCloseDelay || 3000)
      }

      return result

    } catch (error) {
      state.status.type = 'error'
      state.error = {
        message: error.message || 'Unknown error',
        code: error.code || 'UNKNOWN_ERROR',
        details: error.stack,
        recoverable: false,
        retryCount: 0,
        maxRetries: 0
      }
      state.endTime = new Date()
      state.duration = state.endTime.getTime() - state.startTime.getTime()

      this.addToHistory(state, 'failure')
      this.updateDisplay()

      throw error

    } finally {
      cancellationTokenSource.dispose()
      
      // Remove from active progress after delay
      setTimeout(() => {
        this.activeProgress.delete(progressId)
        this.updateDisplay()
      }, 5000)
    }
  }

  public startProgress(options: ProgressOptions): ProgressController {
    const progressId = this.generateProgressId()
    const state = this.createProgressState(progressId, options)
    
    state.status.type = 'running'
    state.startTime = new Date()
    
    this.activeProgress.set(progressId, state)
    this.updateDisplay()

    return new ProgressController(progressId, this)
  }

  public updateProgress(
    progressId: string,
    progress: number,
    message?: string,
    step?: string
  ): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    state.progress = Math.max(0, Math.min(100, progress))
    state.status.percentage = state.progress
    state.status.message = message

    if (step) {
      this.updateStep(progressId, step, progress, message)
    }

    // Calculate estimated time remaining
    if (state.progress > 0 && state.startTime) {
      const elapsed = Date.now() - state.startTime.getTime()
      const estimatedTotal = (elapsed / state.progress) * 100
      state.status.estimatedTimeRemaining = estimatedTotal - elapsed
    }

    this.updateDisplay()
  }

  public completeProgress(progressId: string, message?: string): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    state.status.type = 'completed'
    state.status.percentage = 100
    state.progress = 100
    state.status.message = message || 'Completed'
    state.endTime = new Date()
    state.duration = state.endTime.getTime() - state.startTime.getTime()

    this.addToHistory(state, 'success')
    this.updateDisplay()

    // Auto-remove after delay
    setTimeout(() => {
      this.activeProgress.delete(progressId)
      this.updateDisplay()
    }, 3000)
  }

  public failProgress(progressId: string, error: string | Error): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    state.status.type = 'error'
    state.error = {
      message: typeof error === 'string' ? error : error.message,
      code: typeof error === 'object' && 'code' in error ? error.code as string : 'UNKNOWN_ERROR',
      details: typeof error === 'object' ? error.stack : undefined,
      recoverable: false,
      retryCount: 0,
      maxRetries: 0
    }
    state.endTime = new Date()
    state.duration = state.endTime.getTime() - state.startTime.getTime()

    this.addToHistory(state, 'failure')
    this.updateDisplay()
  }

  public cancelProgress(progressId: string): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    state.status.type = 'cancelled'
    state.cancelled = true
    state.endTime = new Date()
    state.duration = state.endTime.getTime() - state.startTime.getTime()

    this.addToHistory(state, 'cancelled')
    this.activeProgress.delete(progressId)
    this.updateDisplay()
  }

  public removeProgress(progressId: string): void {
    this.activeProgress.delete(progressId)
    this.updateDisplay()
  }

  // Step management
  public addStep(progressId: string, stepId: string, title: string, weight: number = 1): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    const step: ProgressStep = {
      id: stepId,
      title,
      weight,
      progress: 0,
      status: 'pending',
      substeps: []
    }

    state.steps.push(step)
    this.updateDisplay()
  }

  public updateStep(
    progressId: string,
    stepId: string,
    progress: number,
    message?: string
  ): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    const step = state.steps.find(s => s.id === stepId)
    if (!step) return

    step.progress = Math.max(0, Math.min(100, progress))
    step.status = progress === 100 ? 'completed' : 'running'
    
    if (message) {
      step.description = message
    }

    if (step.status === 'running' && !step.startTime) {
      step.startTime = new Date()
    }

    if (step.status === 'completed' && step.startTime && !step.endTime) {
      step.endTime = new Date()
      step.duration = step.endTime.getTime() - step.startTime.getTime()
    }

    state.currentStep = stepId

    // Calculate overall progress based on weighted steps
    const totalWeight = state.steps.reduce((sum, s) => sum + s.weight, 0)
    const completedWeight = state.steps.reduce((sum, s) => sum + (s.progress / 100) * s.weight, 0)
    state.progress = totalWeight > 0 ? (completedWeight / totalWeight) * 100 : 0
    state.status.percentage = state.progress

    this.updateDisplay()
  }

  public completeStep(progressId: string, stepId: string): void {
    this.updateStep(progressId, stepId, 100)
  }

  public failStep(progressId: string, stepId: string, error: string): void {
    const state = this.activeProgress.get(progressId)
    if (!state) return

    const step = state.steps.find(s => s.id === stepId)
    if (!step) return

    step.status = 'error'
    step.error = error
    step.endTime = new Date()
    
    if (step.startTime) {
      step.duration = step.endTime.getTime() - step.startTime.getTime()
    }

    this.updateDisplay()
  }

  // Display management
  private updateDisplay(): void {
    this.updateStatusBar()
    this.updateWebview()
  }

  private updateStatusBar(): void {
    const activeCount = this.activeProgress.size
    
    if (activeCount === 0) {
      this.statusBarItem.hide()
      return
    }

    const states = Array.from(this.activeProgress.values())
    const runningStates = states.filter(s => s.status.type === 'running')
    const errorStates = states.filter(s => s.status.type === 'error')

    let text = ''
    let tooltip = ''
    let color: vscode.ThemeColor | undefined

    if (errorStates.length > 0) {
      text = `$(error) ${errorStates.length} error${errorStates.length > 1 ? 's' : ''}`
      tooltip = `${errorStates.length} operation${errorStates.length > 1 ? 's' : ''} failed`
      color = new vscode.ThemeColor('statusBarItem.errorBackground')
    } else if (runningStates.length > 0) {
      const avgProgress = runningStates.reduce((sum, s) => sum + s.progress, 0) / runningStates.length
      text = `$(sync~spin) ${Math.round(avgProgress)}%`
      tooltip = `${runningStates.length} operation${runningStates.length > 1 ? 's' : ''} in progress`
      color = new vscode.ThemeColor('statusBarItem.activeBackground')
    } else {
      const completedStates = states.filter(s => s.status.type === 'completed')
      text = `$(check) ${completedStates.length} completed`
      tooltip = `${completedStates.length} operation${completedStates.length > 1 ? 's' : ''} completed`
      color = new vscode.ThemeColor('statusBarItem.activeBackground')
    }

    this.statusBarItem.text = text
    this.statusBarItem.tooltip = tooltip
    this.statusBarItem.backgroundColor = color
    this.statusBarItem.show()
  }

  private updateWebview(): void {
    if (this.webviewPanel) {
      this.webviewPanel.webview.postMessage({
        command: 'updateProgress',
        data: Array.from(this.activeProgress.values())
      })
    }
  }

  // Webview management
  private async showProgressWebview(): Promise<void> {
    if (this.webviewPanel) {
      this.webviewPanel.reveal()
      return
    }

    this.webviewPanel = vscode.window.createWebviewPanel(
      'progressIndicator',
      'Progress Monitor',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(this.context.extensionUri, 'media'),
          vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews')
        ]
      }
    )

    this.webviewPanel.webview.html = this.getWebviewContent()
    
    this.webviewPanel.webview.onDidReceiveMessage(
      async (message) => {
        switch (message.command) {
          case 'cancel':
            this.cancelProgress(message.progressId)
            break
          case 'retry':
            await this.retryProgress(message.progressId)
            break
          case 'clear':
            this.removeProgress(message.progressId)
            break
          case 'clearAll':
            this.clearAllProgress()
            break
          case 'exportReport':
            await this.exportProgressReport()
            break
        }
      },
      undefined,
      this.disposables
    )

    this.webviewPanel.onDidDispose(() => {
      this.webviewPanel = undefined
    })

    // Send initial data
    this.updateWebview()
  }

  private async showHistoryWebview(): Promise<void> {
    const panel = vscode.window.createWebviewPanel(
      'progressHistory',
      'Progress History',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true
      }
    )

    panel.webview.html = this.getHistoryWebviewContent()
    
    panel.webview.onDidReceiveMessage(
      async (message) => {
        switch (message.command) {
          case 'clearHistory':
            this.clearHistory()
            panel.webview.postMessage({
              command: 'historyCleared'
            })
            break
          case 'exportHistory':
            await this.exportHistory()
            break
        }
      },
      undefined,
      this.disposables
    )

    // Send history data
    panel.webview.postMessage({
      command: 'loadHistory',
      data: this.progressHistory
    })
  }

  private getWebviewContent(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Monitor</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        
        .progress-container {
            margin-bottom: 20px;
            border: 1px solid var(--vscode-widget-border);
            border-radius: 6px;
            padding: 15px;
            background-color: var(--vscode-input-background);
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .progress-title {
            font-weight: 600;
            font-size: 16px;
        }
        
        .progress-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn {
            padding: 4px 8px;
            border: 1px solid var(--vscode-button-border);
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--vscode-progressBar-background);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--vscode-progressBar-foreground);
            transition: width 0.3s ease;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .progress-steps {
            margin-top: 15px;
        }
        
        .step {
            padding: 5px 0;
            border-left: 3px solid var(--vscode-widget-border);
            padding-left: 10px;
            margin: 5px 0;
        }
        
        .step.running {
            border-left-color: var(--vscode-progressBar-foreground);
        }
        
        .step.completed {
            border-left-color: var(--vscode-charts-green);
        }
        
        .step.error {
            border-left-color: var(--vscode-errorForeground);
        }
        
        .status-running {
            color: var(--vscode-charts-blue);
        }
        
        .status-completed {
            color: var(--vscode-charts-green);
        }
        
        .status-error {
            color: var(--vscode-errorForeground);
        }
        
        .status-cancelled {
            color: var(--vscode-charts-orange);
        }
        
        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 40px;
        }
    </style>
</head>
<body>
    <div id="progressList">
        <div class="empty-state">
            No active operations
        </div>
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'updateProgress':
                    updateProgressDisplay(message.data);
                    break;
            }
        });
        
        function updateProgressDisplay(progressStates) {
            const container = document.getElementById('progressList');
            
            if (progressStates.length === 0) {
                container.innerHTML = '<div class="empty-state">No active operations</div>';
                return;
            }
            
            container.innerHTML = progressStates.map(state => createProgressHtml(state)).join('');
        }
        
        function createProgressHtml(state) {
            const statusClass = 'status-' + state.status.type;
            const percentage = Math.round(state.status.percentage);
            
            return \`
                <div class="progress-container">
                    <div class="progress-header">
                        <div class="progress-title \${statusClass}">\${state.title}</div>
                        <div class="progress-actions">
                            \${state.cancellable && state.status.type === 'running' ? 
                                \`<button class="btn" onclick="cancelProgress('\${state.id}')">Cancel</button>\` : ''}
                            \${state.status.type === 'error' ? 
                                \`<button class="btn" onclick="retryProgress('\${state.id}')">Retry</button>\` : ''}
                            <button class="btn" onclick="clearProgress('\${state.id}')">Clear</button>
                        </div>
                    </div>
                    
                    \${state.description ? \`<div>\${state.description}</div>\` : ''}
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: \${percentage}%"></div>
                    </div>
                    
                    <div class="progress-info">
                        <span>\${state.status.message || ''}</span>
                        <span>\${percentage}%</span>
                    </div>
                    
                    \${state.steps.length > 0 ? \`
                        <div class="progress-steps">
                            \${state.steps.map(step => \`
                                <div class="step \${step.status}">
                                    <strong>\${step.title}</strong> - \${step.progress}%
                                    \${step.description ? \`<div>\${step.description}</div>\` : ''}
                                </div>
                            \`).join('')}
                        </div>
                    \` : ''}
                    
                    \${state.error ? \`
                        <div style="color: var(--vscode-errorForeground); margin-top: 10px;">
                            Error: \${state.error.message}
                        </div>
                    \` : ''}
                </div>
            \`;
        }
        
        function cancelProgress(id) {
            vscode.postMessage({ command: 'cancel', progressId: id });
        }
        
        function retryProgress(id) {
            vscode.postMessage({ command: 'retry', progressId: id });
        }
        
        function clearProgress(id) {
            vscode.postMessage({ command: 'clear', progressId: id });
        }
    </script>
</body>
</html>
    `
  }

  private getHistoryWebviewContent(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress History</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .history-item {
            border: 1px solid var(--vscode-widget-border);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: var(--vscode-input-background);
        }
        
        .history-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .history-meta {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }
        
        .outcome-success { color: var(--vscode-charts-green); }
        .outcome-failure { color: var(--vscode-errorForeground); }
        .outcome-cancelled { color: var(--vscode-charts-orange); }
        
        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 40px;
        }
        
        .btn {
            padding: 6px 12px;
            border: 1px solid var(--vscode-button-border);
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 3px;
            cursor: pointer;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="history-header">
        <h2>Progress History</h2>
        <div>
            <button class="btn" onclick="exportHistory()">Export</button>
            <button class="btn" onclick="clearHistory()">Clear History</button>
        </div>
    </div>
    
    <div id="historyList">
        <div class="empty-state">Loading history...</div>
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'loadHistory':
                    displayHistory(message.data);
                    break;
                case 'historyCleared':
                    document.getElementById('historyList').innerHTML = 
                        '<div class="empty-state">No history available</div>';
                    break;
            }
        });
        
        function displayHistory(history) {
            const container = document.getElementById('historyList');
            
            if (history.length === 0) {
                container.innerHTML = '<div class="empty-state">No history available</div>';
                return;
            }
            
            container.innerHTML = history.map(item => \`
                <div class="history-item">
                    <div class="history-title">\${item.state.title}</div>
                    <div class="history-meta">
                        <span class="outcome-\${item.outcome}">\${item.outcome.toUpperCase()}</span> • 
                        Duration: \${formatDuration(item.duration)} • 
                        \${new Date(item.timestamp).toLocaleString()}
                    </div>
                </div>
            \`).join('');
        }
        
        function formatDuration(ms) {
            if (ms < 1000) return ms + 'ms';
            if (ms < 60000) return (ms / 1000).toFixed(1) + 's';
            return (ms / 60000).toFixed(1) + 'm';
        }
        
        function clearHistory() {
            vscode.postMessage({ command: 'clearHistory' });
        }
        
        function exportHistory() {
            vscode.postMessage({ command: 'exportHistory' });
        }
    </script>
</body>
</html>
    `
  }

  // Utility methods
  private createProgressState(id: string, options: ProgressOptions): ProgressState {
    return {
      id,
      title: options.title,
      description: options.description,
      progress: 0,
      total: 100,
      status: {
        type: 'pending',
        percentage: 0
      },
      startTime: new Date(),
      cancellable: options.cancellable || false,
      cancelled: false,
      metadata: {
        category: (options.category as any) || 'other',
        priority: options.priority || 'medium',
        source: 'unknown',
        tags: [],
        context: options.metadata || {}
      },
      steps: []
    }
  }

  private generateProgressId(): string {
    return `progress_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private addToHistory(state: ProgressState, outcome: 'success' | 'failure' | 'cancelled'): void {
    const historyItem: ProgressHistory = {
      id: state.id,
      state: { ...state },
      timestamp: new Date(),
      outcome,
      duration: state.duration || 0,
      metadata: state.metadata.context
    }

    this.progressHistory.unshift(historyItem)
    
    // Limit history size
    if (this.progressHistory.length > this.maxHistorySize) {
      this.progressHistory = this.progressHistory.slice(0, this.maxHistorySize)
    }

    this.saveHistory()
  }

  private async loadHistory(): Promise<void> {
    try {
      const history = await this.storageService.get<ProgressHistory[]>('progressHistory')
      if (history) {
        this.progressHistory = history
      }
    } catch (error) {
      this.logger.error('Error loading progress history:', error)
    }
  }

  private async saveHistory(): Promise<void> {
    try {
      await this.storageService.set('progressHistory', this.progressHistory)
    } catch (error) {
      this.logger.error('Error saving progress history:', error)
    }
  }

  private clearHistory(): void {
    this.progressHistory = []
    this.saveHistory()
  }

  private clearAllProgress(): void {
    this.activeProgress.clear()
    this.updateDisplay()
  }

  private cancelAllProgress(): void {
    for (const [id, state] of this.activeProgress) {
      if (state.cancellable && state.status.type === 'running') {
        this.cancelProgress(id)
      }
    }
  }

  private async retryProgress(progressId: string): Promise<void> {
    // Implementation would depend on the specific operation
    // This is a placeholder for retry logic
    this.logger.info(`Retry requested for progress: ${progressId}`)
  }

  private async exportProgressReport(): Promise<void> {
    const analytics = this.generateAnalytics()
    const report = {
      generated: new Date().toISOString(),
      analytics,
      activeProgress: Array.from(this.activeProgress.values()),
      history: this.progressHistory
    }

    const reportJson = JSON.stringify(report, null, 2)
    
    const uri = await vscode.window.showSaveDialog({
      defaultUri: vscode.Uri.file(`progress-report-${Date.now()}.json`),
      filters: { 'JSON': ['json'] }
    })

    if (uri) {
      await vscode.workspace.fs.writeFile(uri, Buffer.from(reportJson))
      vscode.window.showInformationMessage('Progress report exported successfully')
    }
  }

  private async exportHistory(): Promise<void> {
    const historyJson = JSON.stringify(this.progressHistory, null, 2)
    
    const uri = await vscode.window.showSaveDialog({
      defaultUri: vscode.Uri.file(`progress-history-${Date.now()}.json`),
      filters: { 'JSON': ['json'] }
    })

    if (uri) {
      await vscode.workspace.fs.writeFile(uri, Buffer.from(historyJson))
      vscode.window.showInformationMessage('Progress history exported successfully')
    }
  }

  private generateAnalytics(): ProgressAnalytics {
    const totalOperations = this.progressHistory.length
    const successfulOperations = this.progressHistory.filter(h => h.outcome === 'success').length
    const durations = this.progressHistory.map(h => h.duration)
    
    return {
      totalOperations,
      successRate: totalOperations > 0 ? successfulOperations / totalOperations : 0,
      averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
      commonErrors: new Map(),
      performanceMetrics: {
        fastestOperation: Math.min(...durations) || 0,
        slowestOperation: Math.max(...durations) || 0,
        medianDuration: this.calculateMedian(durations),
        p95Duration: this.calculatePercentile(durations, 95),
        p99Duration: this.calculatePercentile(durations, 99),
        throughputTrend: []
      },
      categoryBreakdown: new Map()
    }
  }

  private calculateMedian(numbers: number[]): number {
    const sorted = [...numbers].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid]
  }

  private calculatePercentile(numbers: number[], percentile: number): number {
    const sorted = [...numbers].sort((a, b) => a - b)
    const index = Math.ceil((percentile / 100) * sorted.length) - 1
    return sorted[index] || 0
  }

  private getDefaultTheme(): ProgressTheme {
    return {
      primaryColor: '#007ACC',
      backgroundColor: '#1E1E1E',
      textColor: '#CCCCCC',
      successColor: '#4CAF50',
      errorColor: '#F44336',
      warningColor: '#FF9800',
      borderRadius: 4,
      fontSize: 13,
      animation: true
    }
  }

  public getActiveProgress(): ProgressState[] {
    return Array.from(this.activeProgress.values())
  }

  public getProgressHistory(): ProgressHistory[] {
    return [...this.progressHistory]
  }

  public dispose(): void {
    this.statusBarItem.dispose()
    this.webviewPanel?.dispose()
    this.disposables.forEach(d => d.dispose())
  }
}

export class ProgressReporter {
  constructor(
    private readonly progressId: string,
    private readonly manager: ProgressManager
  ) {}

  report(increment: number, message?: string): void {
    this.manager.updateProgress(this.progressId, increment, message)
  }
}

export class ProgressController {
  constructor(
    private readonly progressId: string,
    private readonly manager: ProgressManager
  ) {}

  update(progress: number, message?: string, step?: string): void {
    this.manager.updateProgress(this.progressId, progress, message, step)
  }

  complete(message?: string): void {
    this.manager.completeProgress(this.progressId, message)
  }

  fail(error: string | Error): void {
    this.manager.failProgress(this.progressId, error)
  }

  cancel(): void {
    this.manager.cancelProgress(this.progressId)
  }

  addStep(stepId: string, title: string, weight?: number): void {
    this.manager.addStep(this.progressId, stepId, title, weight)
  }

  updateStep(stepId: string, progress: number, message?: string): void {
    this.manager.updateStep(this.progressId, stepId, progress, message)
  }

  completeStep(stepId: string): void {
    this.manager.completeStep(this.progressId, stepId)
  }

  failStep(stepId: string, error: string): void {
    this.manager.failStep(this.progressId, stepId, error)
  }
}