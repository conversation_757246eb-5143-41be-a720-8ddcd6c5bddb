import { LanceService, SearchResult, QueryOptions } from './lanceService'
import { EmbeddingGenerator } from './embeddingGenerator'
import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'

export interface SearchQuery {
  text: string
  type?: 'all' | 'file' | 'function' | 'class' | 'symbol' | 'snippet'
  language?: string
  filePath?: string
  includeContent?: boolean
  contextRadius?: number
  boostFactors?: BoostFactors
}

export interface BoostFactors {
  recentFiles: number
  exportedSymbols: number
  largerFiles: number
  documentation: number
  testFiles: number
  importance: number
}

export interface SearchContext {
  currentFile?: string
  recentFiles?: string[]
  activeSymbols?: string[]
  workspaceRoot?: string
  userPreferences?: UserPreferences
}

export interface UserPreferences {
  preferredLanguages: string[]
  favoritePatterns: string[]
  excludePatterns: string[]
  searchDepth: 'shallow' | 'medium' | 'deep'
  resultLimit: number
}

export interface EnhancedSearchResult extends SearchResult {
  contextSnippets: string[]
  relatedSymbols: string[]
  fileSummary: string
  relevanceReason: string
  codePreview?: string
  symbolContext?: SymbolContext
}

export interface SymbolContext {
  containingClass?: string
  containingFunction?: string
  parameters?: string[]
  returnType?: string
  imports?: string[]
  usages?: number
}

export interface SearchStats {
  totalQueries: number
  averageResponseTime: number
  mostSearchedTerms: string[]
  popularFileTypes: string[]
  searchAccuracy: number
}

export interface AggregatedResults {
  results: EnhancedSearchResult[]
  categories: ResultCategory[]
  suggestions: string[]
  relatedSearches: string[]
  totalMatches: number
  searchTime: number
}

export interface ResultCategory {
  type: string
  count: number
  topResults: EnhancedSearchResult[]
  averageScore: number
}

export class SemanticSearch {
  private lanceService: LanceService
  private embeddingGenerator: EmbeddingGenerator
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private stats: SearchStats
  private defaultBoostFactors: BoostFactors

  constructor(
    lanceService: LanceService,
    embeddingGenerator: EmbeddingGenerator,
    fileSystem: FileSystemService,
    db: ConnectionManager
  ) {
    this.lanceService = lanceService
    this.embeddingGenerator = embeddingGenerator
    this.fileSystem = fileSystem
    this.db = db
    this.stats = this.initializeStats()
    this.defaultBoostFactors = {
      recentFiles: 1.2,
      exportedSymbols: 1.3,
      largerFiles: 0.9,
      documentation: 1.1,
      testFiles: 0.8,
      importance: 1.5
    }
  }

  private initializeStats(): SearchStats {
    return {
      totalQueries: 0,
      averageResponseTime: 0,
      mostSearchedTerms: [],
      popularFileTypes: [],
      searchAccuracy: 0
    }
  }

  async search(query: SearchQuery, context?: SearchContext): Promise<AggregatedResults> {
    const startTime = Date.now()
    
    try {
      // Generate embedding for search query
      const queryEmbedding = await this.embeddingGenerator.generateEmbedding(query.text)
      
      // Build search options
      const searchOptions = this.buildSearchOptions(query, context)
      
      // Perform vector search
      const vectorResults = await this.lanceService.searchSimilar(queryEmbedding, searchOptions)
      
      // Enhance results with additional context
      const enhancedResults = await this.enhanceResults(vectorResults, query, context)
      
      // Apply relevance boosting
      const boostedResults = this.applyBoostFactors(enhancedResults, query, context)
      
      // Aggregate and categorize results
      const aggregated = this.aggregateResults(boostedResults, query)
      
      // Update stats
      const searchTime = Date.now() - startTime
      this.updateStats(query.text, searchTime)
      
      aggregated.searchTime = searchTime
      
      return aggregated
    } catch (error) {
      console.error('Semantic search error:', error)
      throw new Error(`Search failed: ${error.message}`)
    }
  }

  private buildSearchOptions(query: SearchQuery, context?: SearchContext): QueryOptions {
    const options: QueryOptions = {
      limit: context?.userPreferences?.resultLimit || 50,
      threshold: 0.6,
      includeMetadata: true,
      includeVectors: false,
      filter: {}
    }

    // Apply type filter
    if (query.type && query.type !== 'all') {
      options.filter!.type = query.type
    }

    // Apply language filter
    if (query.language) {
      options.filter!.language = query.language
    }

    // Apply file path filter
    if (query.filePath) {
      options.filter!.filePath = query.filePath
    }

    // Apply user preferences
    if (context?.userPreferences) {
      const prefs = context.userPreferences
      
      if (prefs.preferredLanguages.length > 0) {
        options.filter!.language = prefs.preferredLanguages
      }

      if (prefs.excludePatterns.length > 0) {
        // This would need to be implemented in the filter expression
        // For now, we'll handle it in post-processing
      }
    }

    return options
  }

  private async enhanceResults(
    results: SearchResult[],
    query: SearchQuery,
    context?: SearchContext
  ): Promise<EnhancedSearchResult[]> {
    const enhanced: EnhancedSearchResult[] = []

    for (const result of results) {
      try {
        const contextSnippets = await this.getContextSnippets(result, query.contextRadius || 3)
        const relatedSymbols = await this.getRelatedSymbols(result)
        const fileSummary = await this.getFileSummary(result.metadata.filePath)
        const relevanceReason = this.generateRelevanceReason(result, query)
        const codePreview = query.includeContent ? await this.getCodePreview(result) : undefined
        const symbolContext = await this.getSymbolContext(result)

        enhanced.push({
          ...result,
          contextSnippets,
          relatedSymbols,
          fileSummary,
          relevanceReason,
          codePreview,
          symbolContext
        })
      } catch (error) {
        console.error('Error enhancing result:', error)
        // Add basic enhanced result
        enhanced.push({
          ...result,
          contextSnippets: [],
          relatedSymbols: [],
          fileSummary: '',
          relevanceReason: 'Semantic similarity'
        })
      }
    }

    return enhanced
  }

  private async getContextSnippets(result: SearchResult, radius: number): Promise<string[]> {
    const snippets: string[] = []
    
    if (!result.metadata.filePath) return snippets

    try {
      const content = await this.fileSystem.readFile(result.metadata.filePath)
      const lines = content.split('\n')
      
      if (result.metadata.startLine !== undefined) {
        const startLine = Math.max(0, result.metadata.startLine - radius)
        const endLine = Math.min(lines.length - 1, (result.metadata.endLine || result.metadata.startLine) + radius)
        
        for (let i = startLine; i <= endLine; i++) {
          snippets.push(`${i + 1}: ${lines[i]}`)
        }
      } else {
        // For whole file results, get the first few lines
        const previewLines = Math.min(10, lines.length)
        for (let i = 0; i < previewLines; i++) {
          snippets.push(`${i + 1}: ${lines[i]}`)
        }
      }
    } catch (error) {
      console.error('Error getting context snippets:', error)
    }

    return snippets
  }

  private async getRelatedSymbols(result: SearchResult): Promise<string[]> {
    const symbols: string[] = []
    
    if (!result.metadata.filePath) return symbols

    try {
      const fileRecord = await this.db.findOne(
        this.db.query()
          .select('id')
          .from('files')
          .where('path', '=', result.metadata.filePath)
      )

      if (fileRecord) {
        const fileSymbols = await this.db.findMany(
          this.db.query()
            .select('name, type')
            .from('symbols')
            .where('file_id', '=', fileRecord.id)
            .limit(10)
        )

        symbols.push(...fileSymbols.map(s => `${s.type}:${s.name}`))
      }
    } catch (error) {
      console.error('Error getting related symbols:', error)
    }

    return symbols
  }

  private async getFileSummary(filePath: string): Promise<string> {
    try {
      const fileName = this.fileSystem.basename(filePath)
      const fileExt = this.fileSystem.extname(filePath)
      const stats = await this.fileSystem.stat(filePath)
      
      return `${fileName} (${fileExt}, ${Math.round(stats.size / 1024)}KB)`
    } catch (error) {
      return this.fileSystem.basename(filePath)
    }
  }

  private generateRelevanceReason(result: SearchResult, query: SearchQuery): string {
    const reasons: string[] = []

    if (result.similarity > 0.9) {
      reasons.push('High semantic similarity')
    } else if (result.similarity > 0.7) {
      reasons.push('Good semantic match')
    } else {
      reasons.push('Moderate semantic similarity')
    }

    if (result.metadata.type === 'symbol' && result.metadata.symbolName) {
      reasons.push(`Contains symbol: ${result.metadata.symbolName}`)
    }

    if (result.metadata.type === 'function' || result.metadata.type === 'class') {
      reasons.push(`${result.metadata.type} definition`)
    }

    if (query.language && result.metadata.language === query.language) {
      reasons.push(`Matching language: ${query.language}`)
    }

    return reasons.join(', ')
  }

  private async getCodePreview(result: SearchResult): Promise<string | undefined> {
    if (!result.metadata.filePath) return undefined

    try {
      const content = await this.fileSystem.readFile(result.metadata.filePath)
      
      if (result.metadata.startLine !== undefined && result.metadata.endLine !== undefined) {
        const lines = content.split('\n')
        const startLine = Math.max(0, result.metadata.startLine - 1)
        const endLine = Math.min(lines.length, result.metadata.endLine)
        
        return lines.slice(startLine, endLine).join('\n')
      } else if (result.text) {
        return result.text.substring(0, 500) + (result.text.length > 500 ? '...' : '')
      }
    } catch (error) {
      console.error('Error getting code preview:', error)
    }

    return undefined
  }

  private async getSymbolContext(result: SearchResult): Promise<SymbolContext | undefined> {
    if (result.metadata.type !== 'symbol' || !result.metadata.symbolName) {
      return undefined
    }

    try {
      const fileRecord = await this.db.findOne(
        this.db.query()
          .select('id')
          .from('files')
          .where('path', '=', result.metadata.filePath)
      )

      if (fileRecord) {
        const symbolData = await this.db.findOne(
          this.db.query()
            .select('signature, scope, type')
            .from('symbols')
            .where('file_id', '=', fileRecord.id)
            .where('name', '=', result.metadata.symbolName)
        )

        if (symbolData) {
          return {
            containingClass: symbolData.scope !== 'global' ? symbolData.scope : undefined,
            parameters: symbolData.signature ? this.extractParameters(symbolData.signature) : undefined,
            returnType: symbolData.type,
            usages: 0 // Would need usage tracking
          }
        }
      }
    } catch (error) {
      console.error('Error getting symbol context:', error)
    }

    return undefined
  }

  private extractParameters(signature: string): string[] {
    const match = signature.match(/\(([^)]*)\)/)
    if (match && match[1]) {
      return match[1].split(',').map(param => param.trim()).filter(param => param.length > 0)
    }
    return []
  }

  private applyBoostFactors(
    results: EnhancedSearchResult[],
    query: SearchQuery,
    context?: SearchContext
  ): EnhancedSearchResult[] {
    const boostFactors = { ...this.defaultBoostFactors, ...query.boostFactors }
    
    return results.map(result => {
      let boostedScore = result.score

      // Boost recent files
      if (context?.recentFiles?.includes(result.metadata.filePath)) {
        boostedScore *= boostFactors.recentFiles
      }

      // Boost exported symbols
      if (result.metadata.type === 'symbol' && result.metadata.symbolName) {
        boostedScore *= boostFactors.exportedSymbols
      }

      // Boost by importance
      if (result.metadata.importance) {
        boostedScore *= (1 + (result.metadata.importance - 1) * boostFactors.importance)
      }

      // Penalty for test files (unless specifically searching for tests)
      if (query.type !== 'all' && result.metadata.filePath.includes('test')) {
        boostedScore *= boostFactors.testFiles
      }

      return {
        ...result,
        score: boostedScore
      }
    }).sort((a, b) => b.score - a.score)
  }

  private aggregateResults(results: EnhancedSearchResult[], query: SearchQuery): AggregatedResults {
    const categories = this.categorizeResults(results)
    const suggestions = this.generateSuggestions(results, query)
    const relatedSearches = this.generateRelatedSearches(results, query)

    return {
      results,
      categories,
      suggestions,
      relatedSearches,
      totalMatches: results.length,
      searchTime: 0 // Will be set by caller
    }
  }

  private categorizeResults(results: EnhancedSearchResult[]): ResultCategory[] {
    const categories = new Map<string, EnhancedSearchResult[]>()

    // Group by type
    for (const result of results) {
      const type = result.metadata.type
      if (!categories.has(type)) {
        categories.set(type, [])
      }
      categories.get(type)!.push(result)
    }

    // Convert to category format
    return Array.from(categories.entries()).map(([type, results]) => ({
      type,
      count: results.length,
      topResults: results.slice(0, 3),
      averageScore: results.reduce((sum, r) => sum + r.score, 0) / results.length
    }))
  }

  private generateSuggestions(results: EnhancedSearchResult[], query: SearchQuery): string[] {
    const suggestions: string[] = []
    
    // Extract common terms from high-scoring results
    const highScoreResults = results.filter(r => r.score > 0.8)
    const commonTerms = new Map<string, number>()
    
    for (const result of highScoreResults) {
      if (result.metadata.symbolName) {
        const term = result.metadata.symbolName.toLowerCase()
        commonTerms.set(term, (commonTerms.get(term) || 0) + 1)
      }
    }
    
    // Sort by frequency and add top terms
    const sortedTerms = Array.from(commonTerms.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([term]) => term)
    
    suggestions.push(...sortedTerms)
    
    return suggestions
  }

  private generateRelatedSearches(results: EnhancedSearchResult[], query: SearchQuery): string[] {
    const relatedSearches: string[] = []
    
    // Generate searches based on file types
    const languages = new Set(results.map(r => r.metadata.language))
    for (const lang of languages) {
      if (lang !== query.language) {
        relatedSearches.push(`${query.text} in ${lang}`)
      }
    }
    
    // Generate searches based on symbol types
    const symbolTypes = new Set(results.map(r => r.metadata.symbolType).filter(Boolean))
    for (const symbolType of symbolTypes) {
      relatedSearches.push(`${symbolType} ${query.text}`)
    }
    
    return relatedSearches.slice(0, 3)
  }

  private updateStats(searchTerm: string, responseTime: number): void {
    this.stats.totalQueries++
    this.stats.averageResponseTime = 
      (this.stats.averageResponseTime * (this.stats.totalQueries - 1) + responseTime) / this.stats.totalQueries
    
    // Update most searched terms (simplified)
    if (!this.stats.mostSearchedTerms.includes(searchTerm)) {
      this.stats.mostSearchedTerms.push(searchTerm)
    }
  }

  async findSimilarCode(filePath: string, startLine: number, endLine: number): Promise<EnhancedSearchResult[]> {
    try {
      const content = await this.fileSystem.readFile(filePath)
      const lines = content.split('\n')
      const codeSnippet = lines.slice(startLine - 1, endLine).join('\n')
      
      const query: SearchQuery = {
        text: codeSnippet,
        type: 'snippet',
        includeContent: true,
        contextRadius: 5
      }
      
      const results = await this.search(query)
      
      // Filter out the exact same code snippet
      return results.results.filter(result => 
        result.metadata.filePath !== filePath ||
        result.metadata.startLine !== startLine ||
        result.metadata.endLine !== endLine
      )
    } catch (error) {
      console.error('Error finding similar code:', error)
      return []
    }
  }

  async findRelatedSymbols(symbolName: string, symbolType: string): Promise<EnhancedSearchResult[]> {
    const query: SearchQuery = {
      text: `${symbolType} ${symbolName}`,
      type: 'symbol',
      includeContent: true
    }
    
    const results = await this.search(query)
    return results.results
  }

  getStats(): SearchStats {
    return { ...this.stats }
  }

  resetStats(): void {
    this.stats = this.initializeStats()
  }
}