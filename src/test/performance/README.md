# Performance Benchmarks

This directory contains performance tests and benchmarks for the Natural Language Task Transformer extension.

## Benchmark Categories

### Response Time Benchmarks
- **AI Provider Response Time**: Measure API call latency
- **Task Generation Speed**: Time to generate structured tasks
- **Context Analysis Time**: Codebase analysis and context extraction
- **Template Processing**: Template rendering and formatting

### Resource Usage Benchmarks
- **Memory Usage**: Memory consumption during operations
- **CPU Usage**: Processing overhead and efficiency
- **Disk I/O**: File indexing and storage operations
- **Network Usage**: API call frequency and data transfer

### Scalability Benchmarks
- **Large Codebase Handling**: Performance with large projects
- **Concurrent Operations**: Multiple simultaneous requests
- **Index Size Impact**: Performance vs. index size
- **User Load**: Multiple users in enterprise scenarios

## Running Benchmarks

### Prerequisites
```bash
# Install benchmark dependencies
npm install --save-dev benchmark clinic autocannon
```

### Command Line
```bash
# Run all benchmarks
npm run benchmark

# Run specific benchmark suite
npm run benchmark:response-time
npm run benchmark:memory
npm run benchmark:scalability

# Run with profiling
npm run benchmark:profile
```

### Benchmark Configuration
```json
{
  "benchmarks": {
    "iterations": 100,
    "warmup": 10,
    "timeout": 30000,
    "scenarios": [
      {
        "name": "small-project",
        "files": 50,
        "size": "1MB"
      },
      {
        "name": "medium-project", 
        "files": 500,
        "size": "10MB"
      },
      {
        "name": "large-project",
        "files": 5000,
        "size": "100MB"
      }
    ]
  }
}
```

## Benchmark Results

### Response Time Targets
```
Task Generation:
- Simple tasks: < 2 seconds
- Complex tasks: < 5 seconds
- Context analysis: < 3 seconds

AI Provider Calls:
- OpenAI GPT-4: < 8 seconds
- Anthropic Claude: < 10 seconds
- Error handling: < 1 second

Template Processing:
- Simple templates: < 100ms
- Complex templates: < 500ms
- Custom templates: < 1 second
```

### Memory Usage Targets
```
Base Extension:
- Initial load: < 50MB
- Idle state: < 20MB
- Active usage: < 100MB

Indexing Operations:
- Small project (< 100 files): < 30MB
- Medium project (< 1000 files): < 100MB
- Large project (< 10000 files): < 500MB

Peak Usage:
- Maximum allowed: < 1GB
- Cleanup threshold: > 800MB
- Warning threshold: > 600MB
```

### Performance Metrics

#### Latency Percentiles
```
Response Time Distribution:
- P50 (median): < 3 seconds
- P90: < 8 seconds
- P95: < 12 seconds
- P99: < 20 seconds
- P99.9: < 30 seconds
```

#### Throughput Metrics
```
Operations per Minute:
- Task transformations: > 10
- Context suggestions: > 20
- Template processing: > 100
- Index updates: > 500
```

## Test Scenarios

### Scenario 1: Small Project
```typescript
const smallProjectBenchmark = {
  name: 'Small React Project',
  files: 25,
  components: 10,
  totalSize: '500KB',
  expectedTime: '< 2 seconds',
  expectedMemory: '< 30MB'
};
```

### Scenario 2: Medium Project
```typescript
const mediumProjectBenchmark = {
  name: 'Medium Node.js API',
  files: 200,
  routes: 50,
  totalSize: '5MB',
  expectedTime: '< 5 seconds',
  expectedMemory: '< 80MB'
};
```

### Scenario 3: Large Project
```typescript
const largeProjectBenchmark = {
  name: 'Large Enterprise App',
  files: 2000,
  modules: 500,
  totalSize: '50MB',
  expectedTime: '< 15 seconds',
  expectedMemory: '< 300MB'
};
```

## Monitoring and Profiling

### CPU Profiling
```bash
# Profile CPU usage during benchmarks
npm run benchmark:cpu-profile

# Generate flame graphs
npm run benchmark:flame-graph
```

### Memory Profiling
```bash
# Monitor memory usage
npm run benchmark:memory-profile

# Generate heap snapshots
npm run benchmark:heap-snapshot
```

### Network Profiling
```bash
# Monitor API call patterns
npm run benchmark:network-profile

# Analyze request/response sizes
npm run benchmark:network-analysis
```

## Optimization Guidelines

### Performance Targets
1. **Startup Time**: Extension should activate in < 1 second
2. **First Use**: Initial response in < 5 seconds
3. **Subsequent Uses**: Response in < 3 seconds
4. **Memory Growth**: Linear growth with project size
5. **Cleanup**: Effective garbage collection

### Optimization Strategies

#### Caching
- Cache AI responses for similar inputs
- Cache template processing results
- Cache context analysis results
- Implement LRU cache with size limits

#### Lazy Loading
- Load AI providers on first use
- Defer index building until needed
- Load templates on demand
- Initialize services lazily

#### Batch Processing
- Batch multiple API calls
- Process files in chunks
- Batch template operations
- Aggregate similar requests

#### Resource Management
- Monitor memory usage
- Implement cleanup strategies
- Set resource limits
- Use streaming for large data

## Continuous Monitoring

### Performance Regression Detection
```typescript
// Example performance test
describe('Performance Regression Tests', () => {
  it('should maintain response time under 5 seconds', async () => {
    const startTime = Date.now();
    await taskTransformer.transform('Create a login form');
    const duration = Date.now() - startTime;
    
    expect(duration).to.be.lessThan(5000);
  });
});
```

### Automated Benchmarking
```yaml
# GitHub Actions workflow
name: Performance Benchmarks
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  benchmark:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run Benchmarks
        run: npm run benchmark
      - name: Upload Results
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-results
          path: benchmark-results.json
```

### Performance Dashboard
- Track metrics over time
- Monitor performance trends
- Set up alerts for regressions
- Compare across versions

## Results Analysis

### Benchmark Report Format
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "0.1.0",
  "environment": {
    "os": "macOS",
    "node": "18.17.0",
    "vscode": "1.84.0"
  },
  "results": {
    "taskGeneration": {
      "mean": 3.2,
      "median": 2.8,
      "p95": 5.1,
      "p99": 8.7
    },
    "memoryUsage": {
      "peak": 95.2,
      "average": 62.4,
      "baseline": 18.1
    }
  }
}
```

### Performance Trends
- Response time improvements over versions
- Memory usage optimization
- Scalability enhancements
- Regression identification

## Troubleshooting Performance Issues

### Common Performance Problems
1. **Slow AI Responses**: Check network latency, API limits
2. **High Memory Usage**: Review caching strategies, cleanup
3. **Slow Indexing**: Optimize file processing, add filters
4. **Template Bottlenecks**: Cache compiled templates

### Debugging Tools
- VS Code Performance Profiler
- Node.js built-in profiler
- Chrome DevTools (for webview components)
- System monitoring tools

### Performance Optimization Checklist
- [ ] Implement efficient caching
- [ ] Use lazy loading where appropriate
- [ ] Optimize API call patterns
- [ ] Manage memory usage
- [ ] Profile and identify bottlenecks
- [ ] Set up monitoring and alerts
- [ ] Document performance requirements
- [ ] Regular performance testing

## Performance Best Practices

### Code Level
- Use efficient algorithms and data structures
- Minimize object creation in hot paths
- Implement proper error handling
- Use streaming for large data processing

### Architecture Level
- Design for scalability
- Implement circuit breakers
- Use connection pooling
- Optimize database queries

### Deployment Level
- Monitor production performance
- Set up performance alerts
- Regular performance reviews
- Capacity planning

## Contributing

### Adding New Benchmarks
1. Create benchmark test file
2. Define performance criteria
3. Add to benchmark suite
4. Update documentation
5. Configure CI/CD integration