import { Error<PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>, ExtensionError } from '../../../utils/errorHandler'

describe('ErrorHandler', () => {
  let errorHandler: ErrorHandler

  beforeEach(() => {
    errorHandler = ErrorHandler.getInstance()
  })

  afterEach(() => {
    // Clear listeners
    errorHandler['errorListeners'] = []
  })

  describe('error categorization', () => {
    it('should categorize API errors correctly', () => {
      const error = new Error('API response failed')
      const extensionError = errorHandler.handleError(error)
      
      expect(extensionError.code).toBe(ErrorCode.API_ERROR)
    })

    it('should categorize file system errors correctly', () => {
      const error = new Error('File not found at path')
      const extensionError = errorHandler.handleError(error)
      
      expect(extensionError.code).toBe(ErrorCode.FILE_SYSTEM_ERROR)
    })

    it('should categorize validation errors correctly', () => {
      const error = new Error('Invalid input provided')
      const extensionError = errorHandler.handleError(error)
      
      expect(extensionError.code).toBe(ErrorCode.VALIDATION_ERROR)
    })

    it('should default to unknown error code', () => {
      const error = new Error('Something went wrong')
      const extensionError = errorHandler.handleError(error)
      
      expect(extensionError.code).toBe(ErrorCode.UNKNOWN)
    })
  })

  describe('error creation', () => {
    it('should create error with all details', () => {
      const context = { userId: '123', action: 'transform' }
      const originalError = new Error('Original error')
      
      const error = errorHandler.createError(
        ErrorCode.VALIDATION_ERROR,
        'Custom error message',
        context,
        originalError
      )
      
      expect(error).toBeInstanceOf(ExtensionError)
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR)
      expect(error.message).toBe('Custom error message')
      expect(error.context).toBe(context)
      expect(error.originalError).toBe(originalError)
      expect(error.timestamp).toBeInstanceOf(Date)
    })
  })

  describe('error listeners', () => {
    it('should notify listeners when error occurs', () => {
      const listener = jest.fn()
      errorHandler.addErrorListener(listener)
      
      const error = new Error('Test error')
      errorHandler.handleError(error)
      
      expect(listener).toHaveBeenCalledTimes(1)
      expect(listener).toHaveBeenCalledWith(expect.any(ExtensionError))
    })

    it('should remove listeners correctly', () => {
      const listener = jest.fn()
      errorHandler.addErrorListener(listener)
      errorHandler.removeErrorListener(listener)
      
      const error = new Error('Test error')
      errorHandler.handleError(error)
      
      expect(listener).not.toHaveBeenCalled()
    })

    it('should handle listener errors gracefully', () => {
      const badListener = jest.fn(() => {
        throw new Error('Listener error')
      })
      const goodListener = jest.fn()
      
      errorHandler.addErrorListener(badListener)
      errorHandler.addErrorListener(goodListener)
      
      const error = new Error('Test error')
      expect(() => errorHandler.handleError(error)).not.toThrow()
      expect(goodListener).toHaveBeenCalled()
    })
  })

  describe('ExtensionError', () => {
    it('should preserve original error stack', () => {
      const originalError = new Error('Original error')
      const extensionError = new ExtensionError({
        code: ErrorCode.API_ERROR,
        message: 'Extension error',
        originalError
      })
      
      expect(extensionError.stack).toBe(originalError.stack)
    })

    it('should use custom stack if provided', () => {
      const customStack = 'Custom stack trace'
      const extensionError = new ExtensionError({
        code: ErrorCode.API_ERROR,
        message: 'Extension error',
        stack: customStack
      })
      
      expect(extensionError.stack).toBe(customStack)
    })
  })

  describe('error handling decorators', () => {
    it('should handle synchronous function errors', () => {
      const { withErrorHandling } = require('../../../utils/errorHandler')
      
      const throwingFunction = () => {
        throw new Error('Function error')
      }
      
      const wrappedFunction = withErrorHandling(throwingFunction)
      
      expect(() => wrappedFunction()).toThrow(ExtensionError)
    })

    it('should handle asynchronous function errors', async () => {
      const { withAsyncErrorHandling } = require('../../../utils/errorHandler')
      
      const throwingAsyncFunction = async () => {
        throw new Error('Async function error')
      }
      
      const wrappedFunction = withAsyncErrorHandling(throwingAsyncFunction)
      
      await expect(wrappedFunction()).rejects.toThrow(ExtensionError)
    })
  })
})