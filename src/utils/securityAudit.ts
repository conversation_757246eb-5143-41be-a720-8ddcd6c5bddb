import * as vscode from 'vscode'
import * as fs from 'fs'
import * as path from 'path'
import { logger } from './logger'
import { ErrorCode, ExtensionError } from './errorHandler'

export interface SecurityIssue {
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'secrets' | 'permissions' | 'input' | 'storage' | 'network' | 'code'
  description: string
  file?: string
  line?: number
  recommendation: string
  ruleId: string
}

export interface SecurityAuditReport {
  timestamp: Date
  issues: SecurityIssue[]
  summary: {
    critical: number
    high: number
    medium: number
    low: number
    total: number
  }
  scannedFiles: number
  duration: number
}

export class SecurityAuditor {
  private static readonly SECURITY_PATTERNS = [
    {
      id: 'hardcoded-api-key',
      pattern: /(?:api[_-]?key|apikey|secret[_-]?key|access[_-]?token)\s*[:=]\s*['"][a-zA-Z0-9\-_]{8,}['"]/gi,
      severity: 'critical' as const,
      category: 'secrets' as const,
      description: 'Hardcoded API key or secret detected',
      recommendation: 'Store secrets in VS Code SecretStorage or environment variables'
    },
    {
      id: 'hardcoded-password',
      pattern: /(?:password|passwd|pwd)\s*[:=]\s*['"][^'"]{1,}['"]/gi,
      severity: 'high' as const,
      category: 'secrets' as const,
      description: 'Hardcoded password detected',
      recommendation: 'Use secure credential storage instead of hardcoded passwords'
    },
    {
      id: 'sql-injection-risk',
      pattern: /(?:query|execute|run)\s*\(\s*['"][^'"]*\$\{[^}]*\}[^'"]*['"]/gi,
      severity: 'high' as const,
      category: 'input' as const,
      description: 'Potential SQL injection vulnerability',
      recommendation: 'Use parameterized queries or prepared statements'
    },
    {
      id: 'command-injection-risk',
      pattern: /(?:exec|spawn|execSync)\s*\(\s*['"][^'"]*\$\{[^}]*\}[^'"]*['"]/gi,
      severity: 'high' as const,
      category: 'input' as const,
      description: 'Potential command injection vulnerability',
      recommendation: 'Sanitize user input before executing commands'
    },
    {
      id: 'unsafe-eval',
      pattern: /(?:eval|Function)\s*\(/gi,
      severity: 'medium' as const,
      category: 'code' as const,
      description: 'Use of eval() or Function constructor',
      recommendation: 'Avoid eval() and use safer alternatives like JSON.parse()'
    },
    {
      id: 'insecure-random',
      pattern: /Math\.random\(\)/gi,
      severity: 'low' as const,
      category: 'code' as const,
      description: 'Use of cryptographically weak random number generator',
      recommendation: 'Use crypto.randomBytes() for cryptographic purposes'
    },
    {
      id: 'console-log-secrets',
      pattern: /console\.log\([^)]*(?:key|token|password|secret)[^)]*\)/gi,
      severity: 'medium' as const,
      category: 'secrets' as const,
      description: 'Potential secret logging in console',
      recommendation: 'Avoid logging sensitive information to console'
    },
    {
      id: 'file-path-traversal',
      pattern: /\.\.\/|\.\.\\|path\.join\([^)]*\.\.[^)]*\)/gi,
      severity: 'medium' as const,
      category: 'input' as const,
      description: 'Potential path traversal vulnerability',
      recommendation: 'Validate and sanitize file paths to prevent directory traversal'
    },
    {
      id: 'unsafe-file-operations',
      pattern: /fs\.(?:writeFile|writeFileSync|unlink|unlinkSync|rmdir|rmdirSync)\s*\([^)]*\$\{[^}]*\}[^)]*\)/gi,
      severity: 'medium' as const,
      category: 'input' as const,
      description: 'Unsafe file operations with user input',
      recommendation: 'Validate and sanitize file paths before file operations'
    },
    {
      id: 'insecure-http',
      pattern: /http:\/\/(?!localhost|127\.0\.0\.1|0\.0\.0\.0)/gi,
      severity: 'low' as const,
      category: 'network' as const,
      description: 'Insecure HTTP connection',
      recommendation: 'Use HTTPS for external connections'
    }
  ]

  static async auditWorkspace(): Promise<SecurityAuditReport> {
    const startTime = Date.now()
    const issues: SecurityIssue[] = []
    let scannedFiles = 0

    logger.info('Starting security audit')

    try {
      if (!vscode.workspace.workspaceFolders) {
        throw new ExtensionError({
          code: ErrorCode.CONFIGURATION_ERROR,
          message: 'No workspace folders found'
        })
      }

      for (const folder of vscode.workspace.workspaceFolders) {
        const workspaceIssues = await this.scanDirectory(folder.uri.fsPath)
        issues.push(...workspaceIssues.issues)
        scannedFiles += workspaceIssues.scannedFiles
      }

      // Add extension-specific security checks
      const extensionIssues = await this.auditExtensionSecurity()
      issues.push(...extensionIssues)

    } catch (error) {
      logger.error('Security audit failed', error as Error)
      throw new ExtensionError({
        code: ErrorCode.UNKNOWN,
        message: 'Security audit failed',
        originalError: error as Error
      })
    }

    const duration = Date.now() - startTime
    const summary = this.createSummary(issues)

    logger.info('Security audit completed', {
      duration,
      scannedFiles,
      issuesFound: issues.length,
      summary
    })

    return {
      timestamp: new Date(),
      issues,
      summary,
      scannedFiles,
      duration
    }
  }

  private static async scanDirectory(dirPath: string): Promise<{ issues: SecurityIssue[], scannedFiles: number }> {
    const issues: SecurityIssue[] = []
    let scannedFiles = 0

    try {
      const files = await this.getFilesToScan(dirPath)
      
      for (const file of files) {
        try {
          const content = await fs.promises.readFile(file, 'utf-8')
          const fileIssues = this.scanFileContent(content, file)
          issues.push(...fileIssues)
          scannedFiles++
        } catch (error) {
          logger.warn(`Failed to scan file: ${file}`, { error })
        }
      }
    } catch (error) {
      logger.error(`Failed to scan directory: ${dirPath}`, error as Error)
    }

    return { issues, scannedFiles }
  }

  private static async getFilesToScan(dirPath: string): Promise<string[]> {
    const files: string[] = []
    const extensions = ['.ts', '.js', '.json', '.yaml', '.yml', '.md']
    const excludeDirs = ['node_modules', '.git', 'dist', 'build', 'coverage']

    const scanRecursively = async (currentPath: string): Promise<void> => {
      try {
        const entries = await fs.promises.readdir(currentPath, { withFileTypes: true })
        
        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name)
          
          if (entry.isDirectory()) {
            if (!excludeDirs.includes(entry.name)) {
              await scanRecursively(fullPath)
            }
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase()
            if (extensions.includes(ext)) {
              files.push(fullPath)
            }
          }
        }
      } catch (error) {
        logger.warn(`Failed to read directory: ${currentPath}`, { error })
      }
    }

    await scanRecursively(dirPath)
    return files
  }

  private static scanFileContent(content: string, filePath: string): SecurityIssue[] {
    const issues: SecurityIssue[] = []
    const lines = content.split('\n')

    for (const pattern of this.SECURITY_PATTERNS) {
      const matches = content.matchAll(pattern.pattern)
      
      for (const match of matches) {
        const lineNumber = this.getLineNumber(content, match.index || 0)
        
        issues.push({
          severity: pattern.severity,
          category: pattern.category,
          description: pattern.description,
          file: filePath,
          line: lineNumber,
          recommendation: pattern.recommendation,
          ruleId: pattern.id
        })
      }
    }

    return issues
  }

  private static getLineNumber(content: string, index: number): number {
    const lines = content.substring(0, index).split('\n')
    return lines.length
  }

  private static async auditExtensionSecurity(): Promise<SecurityIssue[]> {
    const issues: SecurityIssue[] = []

    // Check VS Code permissions
    const packageJsonPath = path.join(__dirname, '../../../package.json')
    try {
      const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf-8'))
      
      // Check for overly broad permissions
      if (packageJson.contributes?.commands) {
        const commands = packageJson.contributes.commands
        if (commands.length > 10) {
          issues.push({
            severity: 'low',
            category: 'permissions',
            description: 'Extension defines many commands',
            recommendation: 'Review if all commands are necessary',
            ruleId: 'excessive-commands'
          })
        }
      }

      // Check activation events
      if (packageJson.activationEvents?.includes('*')) {
        issues.push({
          severity: 'medium',
          category: 'permissions',
          description: 'Extension activates on all events',
          recommendation: 'Use specific activation events instead of "*"',
          ruleId: 'broad-activation'
        })
      }

    } catch (error) {
      logger.warn('Failed to audit package.json', { error })
    }

    // Check for insecure dependencies
    const dependencyIssues = await this.auditDependencies()
    issues.push(...dependencyIssues)

    return issues
  }

  private static async auditDependencies(): Promise<SecurityIssue[]> {
    const issues: SecurityIssue[] = []
    
    try {
      const packageJsonPath = path.join(__dirname, '../../../package.json')
      const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf-8'))
      
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }
      
      for (const [name, version] of Object.entries(dependencies)) {
        if (typeof version === 'string' && version.includes('*')) {
          issues.push({
            severity: 'medium',
            category: 'code',
            description: `Wildcard dependency version: ${name}@${version}`,
            recommendation: 'Pin dependency versions to specific versions',
            ruleId: 'wildcard-dependency'
          })
        }
      }
    } catch (error) {
      logger.warn('Failed to audit dependencies', { error })
    }

    return issues
  }

  private static createSummary(issues: SecurityIssue[]): SecurityAuditReport['summary'] {
    const summary = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      total: issues.length
    }

    for (const issue of issues) {
      summary[issue.severity]++
    }

    return summary
  }

  static async generateSecurityReport(report: SecurityAuditReport): Promise<string> {
    const criticalCount = report.summary.critical
    const highCount = report.summary.high
    const mediumCount = report.summary.medium
    const lowCount = report.summary.low

    let markdown = `# Security Audit Report\n\n`
    markdown += `**Generated:** ${report.timestamp.toLocaleString()}\n\n`
    markdown += `**Scanned Files:** ${report.scannedFiles}\n\n`
    markdown += `**Duration:** ${report.duration}ms\n\n`

    markdown += `## Summary\n\n`
    markdown += `- 🔴 Critical: ${criticalCount}\n`
    markdown += `- 🟠 High: ${highCount}\n`
    markdown += `- 🟡 Medium: ${mediumCount}\n`
    markdown += `- 🟢 Low: ${lowCount}\n`
    markdown += `- **Total:** ${report.summary.total}\n\n`

    if (report.issues.length > 0) {
      markdown += `## Issues Found\n\n`
      
      const groupedIssues = this.groupIssuesBySeverity(report.issues)
      
      for (const [severity, issues] of Object.entries(groupedIssues)) {
        if (issues.length > 0) {
          markdown += `### ${severity.toUpperCase()} Issues\n\n`
          
          for (const issue of issues) {
            markdown += `#### ${issue.description}\n\n`
            markdown += `- **Rule ID:** ${issue.ruleId}\n`
            markdown += `- **Category:** ${issue.category}\n`
            if (issue.file) {
              markdown += `- **File:** ${issue.file}${issue.line ? `:${issue.line}` : ''}\n`
            }
            markdown += `- **Recommendation:** ${issue.recommendation}\n\n`
          }
        }
      }
    } else {
      markdown += `## ✅ No security issues found!\n\n`
    }

    return markdown
  }

  private static groupIssuesBySeverity(issues: SecurityIssue[]): Record<string, SecurityIssue[]> {
    const grouped: Record<string, SecurityIssue[]> = {
      critical: [],
      high: [],
      medium: [],
      low: []
    }

    for (const issue of issues) {
      grouped[issue.severity].push(issue)
    }

    return grouped
  }

  static async saveSecurityReport(report: SecurityAuditReport): Promise<void> {
    try {
      const markdown = await this.generateSecurityReport(report)
      const filename = `security-audit-${new Date().toISOString().replace(/[:.]/g, '-')}.md`
      
      const uri = await vscode.window.showSaveDialog({
        defaultUri: vscode.Uri.file(filename),
        filters: {
          'Markdown files': ['md'],
          'JSON files': ['json']
        }
      })

      if (uri) {
        if (uri.fsPath.endsWith('.json')) {
          await vscode.workspace.fs.writeFile(uri, Buffer.from(JSON.stringify(report, null, 2)))
        } else {
          await vscode.workspace.fs.writeFile(uri, Buffer.from(markdown))
        }
        
        vscode.window.showInformationMessage(`Security report saved to ${uri.fsPath}`)
      }
    } catch (error) {
      logger.error('Failed to save security report', error as Error)
      vscode.window.showErrorMessage('Failed to save security report')
    }
  }
}