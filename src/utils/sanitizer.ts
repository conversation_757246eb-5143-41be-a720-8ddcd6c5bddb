import { ErrorCode } from './errorHandler'

export interface SanitizationOptions {
  maxLength?: number
  allowedChars?: RegExp
  stripHtml?: boolean
  escapeHtml?: boolean
  trimWhitespace?: boolean
  removeControlChars?: boolean
}

export interface SanitizationResult {
  sanitized: string
  violations: string[]
  isValid: boolean
}

export class InputSanitizer {
  private static readonly DEFAULT_MAX_LENGTH = 10000
  private static readonly HTML_ESCAPE_MAP: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;'
  }

  static sanitizeString(input: string, options: SanitizationOptions = {}): SanitizationResult {
    const violations: string[] = []
    let sanitized = input
    
    if (typeof input !== 'string') {
      return {
        sanitized: '',
        violations: ['Input must be a string'],
        isValid: false
      }
    }

    // Check length
    const maxLength = options.maxLength || this.DEFAULT_MAX_LENGTH
    if (sanitized.length > maxLength) {
      violations.push(`Input exceeds maximum length of ${maxLength} characters`)
      sanitized = sanitized.substring(0, maxLength)
    }

    // Remove control characters
    if (options.removeControlChars !== false) {
      const controlCharsRemoved = sanitized.replace(/[\x00-\x1F\x7F-\x9F]/g, '')
      if (controlCharsRemoved !== sanitized) {
        violations.push('Control characters removed')
        sanitized = controlCharsRemoved
      }
    }

    // Trim whitespace
    if (options.trimWhitespace !== false) {
      sanitized = sanitized.trim()
    }

    // Strip HTML tags
    if (options.stripHtml) {
      const htmlStripped = sanitized.replace(/<[^>]*>/g, '')
      if (htmlStripped !== sanitized) {
        violations.push('HTML tags removed')
        sanitized = htmlStripped
      }
    }

    // Escape HTML
    if (options.escapeHtml) {
      sanitized = sanitized.replace(/[&<>"'\/]/g, (char) => this.HTML_ESCAPE_MAP[char] || char)
    }

    // Check allowed characters
    if (options.allowedChars) {
      const allowedOnly = sanitized.replace(options.allowedChars, '')
      if (allowedOnly !== sanitized) {
        violations.push('Disallowed characters removed')
        sanitized = sanitized.replace(new RegExp(`[^${options.allowedChars.source}]`, 'g'), '')
      }
    }

    return {
      sanitized,
      violations,
      isValid: violations.length === 0
    }
  }

  static sanitizeUserInput(input: string): SanitizationResult {
    return this.sanitizeString(input, {
      maxLength: 5000,
      stripHtml: true,
      trimWhitespace: true,
      removeControlChars: true
    })
  }

  static sanitizeFilePath(path: string): SanitizationResult {
    return this.sanitizeString(path, {
      maxLength: 1000,
      allowedChars: /[a-zA-Z0-9._\-\/\\]/,
      trimWhitespace: true,
      removeControlChars: true
    })
  }

  static sanitizeCommandInput(input: string): SanitizationResult {
    return this.sanitizeString(input, {
      maxLength: 2000,
      stripHtml: true,
      trimWhitespace: true,
      removeControlChars: true,
      allowedChars: /[a-zA-Z0-9\s._\-:;,!?'"()[\]{}]/
    })
  }

  static sanitizeApiKey(key: string): SanitizationResult {
    return this.sanitizeString(key, {
      maxLength: 200,
      allowedChars: /[a-zA-Z0-9\-_]/,
      trimWhitespace: true,
      removeControlChars: true
    })
  }

  static sanitizeTemplateContent(content: string): SanitizationResult {
    return this.sanitizeString(content, {
      maxLength: 50000,
      trimWhitespace: true,
      removeControlChars: true
    })
  }

  static validateEmail(email: string): SanitizationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const sanitized = this.sanitizeString(email, {
      maxLength: 254,
      trimWhitespace: true,
      removeControlChars: true
    })

    const violations = [...sanitized.violations]
    if (!emailRegex.test(sanitized.sanitized)) {
      violations.push('Invalid email format')
    }

    return {
      sanitized: sanitized.sanitized,
      violations,
      isValid: violations.length === 0
    }
  }

  static validateUrl(url: string): SanitizationResult {
    const sanitized = this.sanitizeString(url, {
      maxLength: 2000,
      trimWhitespace: true,
      removeControlChars: true
    })

    const violations = [...sanitized.violations]
    
    try {
      const urlObj = new URL(sanitized.sanitized)
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        violations.push('Only HTTP and HTTPS protocols are allowed')
      }
    } catch (error) {
      violations.push('Invalid URL format')
    }

    return {
      sanitized: sanitized.sanitized,
      violations,
      isValid: violations.length === 0
    }
  }

  static sanitizeJsonInput(input: string): SanitizationResult {
    const sanitized = this.sanitizeString(input, {
      maxLength: 100000,
      trimWhitespace: true,
      removeControlChars: true
    })

    const violations = [...sanitized.violations]
    
    try {
      JSON.parse(sanitized.sanitized)
    } catch (error) {
      violations.push('Invalid JSON format')
    }

    return {
      sanitized: sanitized.sanitized,
      violations,
      isValid: violations.length === 0
    }
  }

  static sanitizeRegexPattern(pattern: string): SanitizationResult {
    const sanitized = this.sanitizeString(pattern, {
      maxLength: 1000,
      trimWhitespace: true,
      removeControlChars: true
    })

    const violations = [...sanitized.violations]
    
    try {
      new RegExp(sanitized.sanitized)
    } catch (error) {
      violations.push('Invalid regex pattern')
    }

    return {
      sanitized: sanitized.sanitized,
      violations,
      isValid: violations.length === 0
    }
  }

  static sanitizeAndValidateInput(input: any, type: 'string' | 'number' | 'boolean' | 'array' | 'object'): SanitizationResult {
    let sanitized: string
    const violations: string[] = []

    switch (type) {
      case 'string':
        if (typeof input !== 'string') {
          violations.push(`Expected string, got ${typeof input}`)
          sanitized = String(input)
        } else {
          sanitized = input
        }
        break
      
      case 'number':
        if (typeof input !== 'number') {
          const parsed = Number(input)
          if (isNaN(parsed)) {
            violations.push('Invalid number format')
            sanitized = '0'
          } else {
            sanitized = String(parsed)
          }
        } else {
          sanitized = String(input)
        }
        break
      
      case 'boolean':
        if (typeof input !== 'boolean') {
          violations.push(`Expected boolean, got ${typeof input}`)
          sanitized = String(Boolean(input))
        } else {
          sanitized = String(input)
        }
        break
      
      case 'array':
        if (!Array.isArray(input)) {
          violations.push('Expected array')
          sanitized = '[]'
        } else {
          sanitized = JSON.stringify(input)
        }
        break
      
      case 'object':
        if (typeof input !== 'object' || input === null || Array.isArray(input)) {
          violations.push('Expected object')
          sanitized = '{}'
        } else {
          sanitized = JSON.stringify(input)
        }
        break
      
      default:
        violations.push(`Unknown type: ${type}`)
        sanitized = String(input)
    }

    return {
      sanitized,
      violations,
      isValid: violations.length === 0
    }
  }
}