# Troubleshooting Guide

This guide helps you resolve common issues with the Natural Language Task Transformer extension.

## Common Issues

### API Key Issues

#### "Invalid API Key" Error

**Symptoms:**
- Error message: "Invalid API key"
- Extension fails to generate tasks
- Authentication errors in output

**Solutions:**

1. **Check API Key Format**
   - OpenAI keys start with `sk-`
   - Anthropic keys start with `sk-ant-`
   - Ensure no extra spaces or characters

2. **Verify API Key Status**
   - Log into your provider's dashboard
   - Check if the key is active
   - Verify usage limits haven't been exceeded

3. **Re-enter API Key**
   - Open Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
   - Run `Task Transformer: Configure Task Transformer`
   - Enter your API key again

#### "API Key Not Found" Error

**Symptoms:**
- Extension prompts for API key repeatedly
- No API key stored in settings

**Solutions:**

1. **Clear and Reset**
   ```bash
   # Clear stored secrets
   # Open Command Palette and run:
   # "Developer: Reload Window"
   ```

2. **Manual Configuration**
   - Go to VSCode Settings
   - Search for "Task Transformer"
   - Verify `aiProvider` is set correctly
   - Re-run configuration command

### Performance Issues

#### Slow Task Generation

**Symptoms:**
- Long wait times for responses
- Timeout errors
- Unresponsive interface

**Solutions:**

1. **Optimize Token Usage**
   ```json
   {
     "taskTransformer.maxTokens": 2000,
     "taskTransformer.temperature": 0.5
   }
   ```

2. **Reduce Context Size**
   ```json
   {
     "taskTransformer.maxIndexFiles": 5000,
     "taskTransformer.excludePatterns": [
       "node_modules",
       ".git",
       "dist",
       "build",
       "*.log",
       "*.min.js"
     ]
   }
   ```

3. **Check Network Connection**
   - Verify internet connectivity
   - Check if firewall blocks API requests
   - Try different AI provider

#### High Memory Usage

**Symptoms:**
- VSCode becomes slow
- System memory usage increases
- Extension crashes

**Solutions:**

1. **Limit Indexing**
   ```json
   {
     "taskTransformer.indexingEnabled": false,
     "taskTransformer.maxIndexFiles": 1000
   }
   ```

2. **Restart VSCode**
   - Close and reopen VSCode
   - Reload window: `Ctrl+Shift+P` → "Developer: Reload Window"

3. **Clear Extension Cache**
   - Close VSCode
   - Delete workspace `.vscode` folder (if safe to do so)
   - Restart VSCode

### Context and Indexing Issues

#### "No Context Found" Warning

**Symptoms:**
- Warning messages about missing context
- Generic task suggestions
- No file references in output

**Solutions:**

1. **Enable Indexing**
   ```json
   {
     "taskTransformer.indexingEnabled": true
   }
   ```

2. **Check File Types**
   - Ensure your project contains supported file types
   - Supported: `.js`, `.ts`, `.jsx`, `.tsx`, `.py`, `.java`, `.go`, `.rs`

3. **Wait for Indexing**
   - Initial indexing takes time
   - Check status bar for indexing progress
   - Avoid using extension during initial indexing

#### Incorrect Context Analysis

**Symptoms:**
- Suggestions not relevant to your project
- Wrong framework assumptions
- Incorrect file references

**Solutions:**

1. **Review Project Structure**
   - Ensure `package.json` is present
   - Check if main files are in standard locations
   - Verify import statements are correct

2. **Manual Context Hints**
   - Include framework names in descriptions
   - Mention specific file names
   - Provide additional context in requests

3. **Reset Learning Data**
   - Clear extension workspace storage
   - Allow extension to relearn patterns

### Output and Formatting Issues

#### Malformed Output

**Symptoms:**
- Broken markdown formatting
- Incomplete task lists
- Missing sections

**Solutions:**

1. **Check Input Quality**
   - Use clear, grammatical language
   - Avoid special characters
   - Provide sufficient detail

2. **Adjust AI Parameters**
   ```json
   {
     "taskTransformer.temperature": 0.3,
     "taskTransformer.maxTokens": 3000
   }
   ```

3. **Try Different AI Provider**
   - Switch between OpenAI and Anthropic
   - Test with different models

#### No Output Generated

**Symptoms:**
- Extension runs but produces no output
- Empty response from AI
- Silent failures

**Solutions:**

1. **Check Console Errors**
   - Open Developer Tools: `Help` → `Toggle Developer Tools`
   - Look for error messages
   - Check network tab for failed requests

2. **Verify Input Length**
   - Ensure input isn't too long or too short
   - Optimal length: 50-500 characters
   - Break down complex requests

3. **Test with Simple Input**
   - Try basic request: "Create a login form"
   - If works, gradually add complexity

### Installation and Setup Issues

#### Extension Not Loading

**Symptoms:**
- Extension doesn't appear in command palette
- No context menu options
- Extension not listed in installed extensions

**Solutions:**

1. **Verify Installation**
   - Check Extensions view (`Ctrl+Shift+X`)
   - Ensure extension is enabled
   - Reload window if needed

2. **Check VSCode Version**
   - Requires VSCode 1.82.0 or later
   - Update VSCode if needed
   - Restart after update

3. **Reinstall Extension**
   - Uninstall extension
   - Restart VSCode
   - Reinstall from marketplace

#### Command Not Found

**Symptoms:**
- "Command not found" errors
- Commands don't appear in palette
- Context menu options missing

**Solutions:**

1. **Reload Window**
   - `Ctrl+Shift+P` → "Developer: Reload Window"
   - Wait for extension to activate

2. **Check Extension Activation**
   - Look for extension in bottom status bar
   - Verify no error notifications
   - Check if workspace is supported

3. **Manual Activation**
   - Open any supported file (`.js`, `.ts`, etc.)
   - Extension should activate automatically

### Configuration Issues

#### Settings Not Saved

**Symptoms:**
- Settings revert to defaults
- API key not persisting
- Configuration changes ignored

**Solutions:**

1. **Check Settings Scope**
   - Use workspace settings for project-specific config
   - Use user settings for global config
   - Verify settings.json syntax

2. **Verify Permissions**
   - Check if VSCode can write to settings
   - Ensure workspace folder isn't read-only
   - Check file system permissions

3. **Reset Configuration**
   - Delete `.vscode/settings.json`
   - Reconfigure extension
   - Test with minimal settings

#### Template Issues

**Symptoms:**
- Custom templates not working
- Template errors in output
- Formatting problems

**Solutions:**

1. **Check Template Syntax**
   - Verify Handlebars syntax
   - Test templates with simple data
   - Check for syntax errors

2. **Template Location**
   - Ensure templates are in `.vscode/task-templates/`
   - Check file permissions
   - Verify template file extensions

3. **Validate Template Data**
   - Check if required template variables exist
   - Ensure data format matches template expectations

## Debugging Steps

### Enable Debug Mode

1. Open VSCode Settings
2. Search for "Task Transformer"
3. Enable detailed logging
4. Check Output panel for debug information

### Collect Diagnostic Information

1. **Extension Version**
   - Open Extensions view
   - Note exact version number

2. **VSCode Information**
   - `Help` → `About` → Copy version info

3. **System Information**
   - Operating system version
   - Node.js version (if applicable)
   - Available memory

4. **Error Logs**
   - Open Developer Tools
   - Copy console errors
   - Check Output panel for extension logs

### Test with Minimal Configuration

1. **Create Test Workspace**
   ```bash
   mkdir test-workspace
   cd test-workspace
   echo '{}' > package.json
   code .
   ```

2. **Use Default Settings**
   - Don't customize any settings
   - Use basic API key configuration
   - Test with simple inputs

3. **Isolate Issues**
   - Test one feature at a time
   - Note which specific actions fail
   - Document exact error messages

## Getting Help

### Before Reporting Issues

1. **Check FAQ** (see below)
2. **Review Recent Changes**
   - Did you recently update the extension?
   - Any recent VSCode updates?
   - Recent system changes?

3. **Try Different Scenarios**
   - Test with different projects
   - Try different AI providers
   - Test with different input types

### Reporting Issues

When reporting issues, include:

1. **Environment Information**
   - Operating system
   - VSCode version
   - Extension version

2. **Configuration**
   - Relevant settings (redact API keys)
   - AI provider being used
   - Any custom templates

3. **Steps to Reproduce**
   - Exact steps taken
   - Input provided
   - Expected vs actual behavior

4. **Error Messages**
   - Complete error messages
   - Console output
   - Extension logs

### Support Channels

1. **GitHub Issues**
   - [Report bugs](https://github.com/yourusername/natural-language-task-transformer/issues)
   - Search existing issues first

2. **Community Discussions**
   - [Ask questions](https://github.com/yourusername/natural-language-task-transformer/discussions)
   - Share usage tips

3. **Documentation**
   - [User Guide](user-guide/README.md)
   - [API Documentation](api/README.md)

## FAQ

### General Questions

**Q: Which AI provider should I use?**
A: OpenAI GPT-4 is recommended for general use. Anthropic Claude is good for complex reasoning tasks. Try both to see which works better for your use case.

**Q: How much do API calls cost?**
A: Costs vary by provider and usage. OpenAI GPT-4 costs about $0.03 per 1000 tokens. Monitor your usage in the provider's dashboard.

**Q: Can I use the extension offline?**
A: No, the extension requires internet connection to communicate with AI providers. All processing happens via API calls.

### Technical Questions

**Q: Does the extension store my code?**
A: No, the extension only sends context information to AI providers. Your full codebase is never uploaded. Review the privacy policy for details.

**Q: How does the learning system work?**
A: The extension tracks your preferences and patterns locally. This data is used to improve suggestions but is never shared externally.

**Q: Can I customize the output format?**
A: Yes, you can create custom templates in `.vscode/task-templates/` to control output format and structure.

### Performance Questions

**Q: Why is task generation slow?**
A: Response time depends on AI provider, input complexity, and network speed. Try reducing `maxTokens` or simplifying inputs for faster responses.

**Q: How can I improve suggestion quality?**
A: Provide clear, detailed descriptions. Include relevant context. Use proper grammar and technical terminology. Enable learning to improve over time.

**Q: Can I use custom AI models?**
A: Currently limited to supported providers (OpenAI, Anthropic). Custom model support may be added in future versions.

## Recovery Procedures

### Reset Extension to Default State

1. **Clear All Settings**
   ```json
   // Remove all taskTransformer settings from settings.json
   ```

2. **Clear Stored Data**
   - Command Palette → "Developer: Reload Window"
   - May need to reconfigure API keys

3. **Reinstall Extension**
   - Uninstall extension
   - Restart VSCode
   - Install fresh copy

### Recover from Corrupted Data

1. **Delete Workspace Storage**
   - Close VSCode
   - Delete `.vscode` folder (backup first)
   - Restart VSCode

2. **Reset Learning Data**
   - Extension will relearn patterns
   - May take time to optimize again

3. **Restore from Backup**
   - If you have settings backups
   - Restore `.vscode/settings.json`
   - Reconfigure API keys

## Prevention Tips

### Best Practices

1. **Regular Updates**
   - Keep extension updated
   - Update VSCode regularly
   - Monitor for security updates

2. **Backup Configuration**
   - Save settings.json
   - Document custom templates
   - Note important preferences

3. **Monitor Usage**
   - Check API usage regularly
   - Set up billing alerts
   - Monitor performance metrics

4. **Security Practices**
   - Use API keys with minimum required permissions
   - Regularly rotate API keys
   - Don't share API keys in settings files

### Maintenance

1. **Regular Cleanup**
   - Clear extension cache periodically
   - Remove unused templates
   - Clean up workspace storage

2. **Performance Monitoring**
   - Watch for memory usage increases
   - Monitor response times
   - Check for error patterns

3. **Configuration Review**
   - Periodically review settings
   - Update exclude patterns
   - Optimize for your workflow