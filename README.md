# Natural Language to Structured Task Transformation

A powerful VSCode extension that transforms informal developer instructions into structured, actionable engineering tasks using AI, context-aware templates, and intelligent learning.

## Features

- **AI-Powered Task Transformation**: Convert natural language descriptions into structured engineering tasks
- **Context-Aware Analysis**: Analyzes your codebase to provide relevant, project-specific suggestions
- **Multiple AI Providers**: Supports OpenAI GPT-4 and Anthropic Claude
- **Intelligent Learning**: Adapts to your preferences and coding patterns over time
- **Security-First Design**: Input sanitization, API key validation, and security auditing built-in
- **Template System**: Customizable templates for different project types and workflows

## Installation

1. Open VSCode
2. Go to the Extensions view (`Ctrl+Shift+X` or `Cmd+Shift+X`)
3. Search for "Natural Language to Structured Task Transformation"
4. Click Install

## Quick Start

### 1. Configure AI Provider

1. Open Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Run `Task Transformer: Configure Task Transformer`
3. Set your AI provider (OpenAI or Anthropic)
4. Enter your API key when prompted

### 2. Transform Natural Language

1. Select text in your editor or place cursor where you want to add tasks
2. Right-click and select "Transform Natural Language to Tasks"
3. Or use Command Palette: `Task Transformer: Transform Natural Language to Tasks`
4. Enter your natural language description
5. Get structured, actionable tasks with context

### 3. Get Context-Aware Suggestions

1. Select code in your editor
2. Right-click and select "Get Context-Aware Suggestions"
3. Or use Command Palette: `Task Transformer: Get Context-Aware Suggestions`
4. Receive relevant suggestions based on your codebase

## Configuration

Configure the extension through VSCode settings:

```json
{
  "taskTransformer.aiProvider": "openai",
  "taskTransformer.model": "gpt-4",
  "taskTransformer.maxTokens": 4000,
  "taskTransformer.temperature": 0.7,
  "taskTransformer.enableLearning": true,
  "taskTransformer.indexingEnabled": true,
  "taskTransformer.maxIndexFiles": 10000,
  "taskTransformer.excludePatterns": ["node_modules", ".git", "dist", "build", "*.log"]
}
```

### Settings Reference

| Setting | Description | Default |
|---------|-------------|---------|
| `aiProvider` | AI service provider (openai, claude) | openai |
| `model` | AI model to use | gpt-4 |
| `maxTokens` | Maximum tokens for AI responses | 4000 |
| `temperature` | AI response creativity (0-1) | 0.7 |
| `enableLearning` | Enable adaptive learning | true |
| `indexingEnabled` | Enable automatic codebase indexing | true |
| `maxIndexFiles` | Maximum files to index | 10000 |
| `excludePatterns` | File patterns to exclude from indexing | ["node_modules", ".git", "dist", "build", "*.log"] |

## Usage Examples

### Basic Task Transformation

**Input:**
```
"Add a login form with email validation and password strength checking"
```

**Output:**
```markdown
## Login Form Implementation

### Tasks:
1. **Create login form component**
   - Add email input field with validation
   - Add password input with strength indicator
   - Include form submission handling

2. **Implement email validation**
   - Add regex pattern for email format
   - Display validation errors inline
   - Prevent submission with invalid email

3. **Add password strength checking**
   - Implement strength calculation algorithm
   - Display strength indicator (weak/medium/strong)
   - Provide password requirements feedback

### Files to modify:
- `src/components/LoginForm.tsx`
- `src/utils/validation.ts`
- `src/styles/login.css`
```

### Context-Aware Suggestions

When you select code in your editor, the extension analyzes the context and provides relevant suggestions:

- **React Components**: Suggests improvements, testing strategies, accessibility enhancements
- **API Endpoints**: Recommends error handling, validation, documentation
- **Database Models**: Suggests indexing, relationships, migrations
- **Configuration Files**: Proposes security improvements, optimization

## Architecture

The extension follows a modular architecture:

- **AI Integration**: Abstract AI service layer supporting multiple providers
- **Context Analysis**: Codebase analysis and indexing system
- **Template Engine**: Handlebars-based template system
- **Learning System**: Adaptive preference tracking and pattern recognition
- **Security**: Input sanitization, API key validation, security auditing

## Security Features

- **Input Sanitization**: All user inputs are sanitized to prevent injection attacks
- **API Key Validation**: Secure storage and validation of API keys
- **Security Auditing**: Built-in security scanning for common vulnerabilities
- **Safe Defaults**: Secure configuration defaults and best practices

## Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/yourusername/natural-language-task-transformer.git
cd natural-language-task-transformer

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Watch for changes during development
npm run watch

# Run tests
npm test

# Package extension
npm run package
```

### Project Structure

```
src/
├── commands/           # VSCode command handlers
├── services/          # Core business logic
│   ├── ai/           # AI provider integrations
│   ├── analysis/     # Code analysis and AST parsing
│   ├── context/      # Context analysis and scoring
│   ├── embeddings/   # Vector embeddings and semantic search
│   ├── memory/       # Learning and preference tracking
│   └── templates/    # Template engine and management
├── utils/            # Utility functions and helpers
├── types/            # TypeScript type definitions
└── extension.ts      # Main extension entry point
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: Check the [docs](docs/) directory for detailed guides
- **Issues**: Report bugs and request features on [GitHub Issues](https://github.com/yourusername/natural-language-task-transformer/issues)
- **Discussions**: Join the conversation on [GitHub Discussions](https://github.com/yourusername/natural-language-task-transformer/discussions)

## Changelog

### v0.0.1 (Initial Release)
- AI-powered task transformation
- Context-aware code analysis
- Template system with Handlebars
- Security features and input validation
- Support for OpenAI and Anthropic providers
- Adaptive learning system
- Vector embeddings with LanceDB