import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'
import { PreferenceTracker } from '../services/memory/preferenceTracker'

export interface Notification {
  id: string
  type: NotificationType
  title: string
  message: string
  severity: NotificationSeverity
  category: NotificationCategory
  source: string
  timestamp: Date
  read: boolean
  dismissed: boolean
  actions: NotificationAction[]
  metadata: NotificationMetadata
  expiresAt?: Date
  priority: NotificationPriority
  persistent: boolean
  grouped: boolean
  groupId?: string
}

export interface NotificationType {
  kind: 'info' | 'warning' | 'error' | 'success' | 'progress' | 'reminder' | 'update' | 'feature' | 'system'
  icon?: string
  sound?: NotificationSound
  vibration?: boolean
}

export interface NotificationSeverity {
  level: 'low' | 'medium' | 'high' | 'critical' | 'urgent'
  requiresAcknowledgment: boolean
  blockingUi: boolean
  autoTimeout?: number
}

export interface NotificationCategory {
  name: string
  description: string
  enabled: boolean
  preferences: CategoryPreferences
}

export interface CategoryPreferences {
  showInStatusBar: boolean
  showAsPopup: boolean
  showInSidebar: boolean
  enableSound: boolean
  enableBadge: boolean
  respectDoNotDisturb: boolean
  batchNotifications: boolean
  maxBatchSize: number
  batchTimeout: number
}

export interface NotificationAction {
  id: string
  label: string
  description?: string
  icon?: string
  command?: vscode.Command
  callback?: () => Promise<void> | void
  isPrimary: boolean
  isDestructive: boolean
  requiresConfirmation: boolean
  shortcut?: string
}

export interface NotificationMetadata {
  userId?: string
  workspaceId?: string
  fileId?: string
  projectId?: string
  sessionId?: string
  context: Record<string, any>
  tags: string[]
  relatedNotifications: string[]
  analytics: NotificationAnalytics
}

export interface NotificationAnalytics {
  shown: Date
  clicked?: Date
  dismissed?: Date
  actionTaken?: string
  timeToAction?: number
  viewDuration?: number
  interactionCount: number
}

export interface NotificationSound {
  type: 'default' | 'info' | 'warning' | 'error' | 'success' | 'custom'
  volume: number
  duration?: number
  customPath?: string
}

export interface NotificationPriority {
  level: number // 1-10, where 10 is highest priority
  boost: boolean
  bypass: boolean // Bypass do-not-disturb
  escalate: boolean // Escalate if not acknowledged
  escalationDelay: number
}

export interface NotificationTemplate {
  id: string
  name: string
  title: string
  message: string
  type: NotificationType
  severity: NotificationSeverity
  category: string
  actions: NotificationAction[]
  variables: TemplateVariable[]
  conditions: TemplateCondition[]
  scheduling: TemplateScheduling
}

export interface TemplateVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'object'
  required: boolean
  defaultValue?: any
  description?: string
}

export interface TemplateCondition {
  field: string
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'regex'
  value: any
  description?: string
}

export interface TemplateScheduling {
  immediate: boolean
  delay?: number
  recurring?: RecurringSchedule
  conditions?: SchedulingCondition[]
}

export interface RecurringSchedule {
  frequency: 'minutely' | 'hourly' | 'daily' | 'weekly' | 'monthly'
  interval: number
  endDate?: Date
  maxOccurrences?: number
}

export interface SchedulingCondition {
  type: 'time' | 'date' | 'event' | 'context'
  condition: string
  value: any
}

export interface NotificationQueue {
  notifications: Notification[]
  processing: boolean
  batchedNotifications: Map<string, Notification[]>
  scheduledNotifications: Map<string, NodeJS.Timeout>
}

export interface NotificationPreferences {
  enabled: boolean
  doNotDisturb: boolean
  doNotDisturbSchedule: DoNotDisturbSchedule
  categories: Map<string, CategoryPreferences>
  general: GeneralPreferences
  appearance: AppearancePreferences
  behavior: BehaviorPreferences
}

export interface DoNotDisturbSchedule {
  enabled: boolean
  startTime: string // HH:MM format
  endTime: string
  days: number[] // 0-6, Sunday to Saturday
  exceptions: string[] // Category names that bypass DND
}

export interface GeneralPreferences {
  maxNotifications: number
  autoCleanup: boolean
  cleanupInterval: number // hours
  retentionPeriod: number // days
  enableAnalytics: boolean
  showNotificationCount: boolean
  groupSimilarNotifications: boolean
  enablePreview: boolean
}

export interface AppearancePreferences {
  theme: 'system' | 'light' | 'dark'
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center'
  size: 'small' | 'medium' | 'large'
  animation: boolean
  showIcons: boolean
  showTimestamp: boolean
  opacity: number
  borderRadius: number
}

export interface BehaviorPreferences {
  defaultTimeout: number
  stackNotifications: boolean
  maxStackSize: number
  playSound: boolean
  showBadges: boolean
  enableVibration: boolean
  focusOnClick: boolean
  closeOnAction: boolean
  confirmDestructiveActions: boolean
}

export interface NotificationHistory {
  notifications: Notification[]
  stats: NotificationStats
  trends: NotificationTrend[]
}

export interface NotificationStats {
  total: number
  read: number
  dismissed: number
  actioned: number
  expired: number
  byCategory: Map<string, number>
  byType: Map<string, number>
  bySeverity: Map<string, number>
  averageResponseTime: number
  mostActiveHours: number[]
}

export interface NotificationTrend {
  date: Date
  count: number
  categories: Map<string, number>
  responseTime: number
}

export class NotificationManager {
  private notifications: Map<string, Notification> = new Map()
  private templates: Map<string, NotificationTemplate> = new Map()
  private preferences: NotificationPreferences
  private queue: NotificationQueue
  private statusBarItem: vscode.StatusBarItem
  private webviewPanel: vscode.WebviewPanel | undefined
  private disposables: vscode.Disposable[] = []
  private history: NotificationHistory
  private cleanupTimer?: NodeJS.Timeout

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService,
    private readonly preferenceTracker: PreferenceTracker
  ) {
    this.preferences = this.getDefaultPreferences()
    this.queue = {
      notifications: [],
      processing: false,
      batchedNotifications: new Map(),
      scheduledNotifications: new Map()
    }
    this.history = {
      notifications: [],
      stats: this.createEmptyStats(),
      trends: []
    }

    this.setupStatusBarItem()
    this.loadPreferences()
    this.loadHistory()
    this.loadTemplates()
    this.registerCommands()
    this.startCleanupTimer()
    this.startQueueProcessor()
  }

  private setupStatusBarItem(): void {
    this.statusBarItem = vscode.window.createStatusBarItem(
      vscode.StatusBarAlignment.Right,
      100
    )
    this.statusBarItem.command = 'notificationManager.showPanel'
    this.statusBarItem.tooltip = 'Notifications'
    this.context.subscriptions.push(this.statusBarItem)
    this.updateStatusBar()
  }

  private registerCommands(): void {
    const commands = [
      vscode.commands.registerCommand('notificationManager.showPanel', () => {
        this.showNotificationPanel()
      }),
      
      vscode.commands.registerCommand('notificationManager.showHistory', () => {
        this.showHistoryPanel()
      }),
      
      vscode.commands.registerCommand('notificationManager.clearAll', () => {
        this.clearAllNotifications()
      }),
      
      vscode.commands.registerCommand('notificationManager.markAllRead', () => {
        this.markAllAsRead()
      }),
      
      vscode.commands.registerCommand('notificationManager.toggleDoNotDisturb', () => {
        this.toggleDoNotDisturb()
      }),
      
      vscode.commands.registerCommand('notificationManager.showPreferences', () => {
        this.showPreferencesPanel()
      }),
      
      vscode.commands.registerCommand('notificationManager.exportNotifications', () => {
        this.exportNotifications()
      }),
      
      vscode.commands.registerCommand('notificationManager.testNotification', () => {
        this.sendTestNotification()
      })
    ]

    this.disposables.push(...commands)
  }

  // Main notification methods
  public async notify(
    title: string,
    message: string,
    type: NotificationType['kind'] = 'info',
    options?: Partial<Notification>
  ): Promise<string> {
    const notification = this.createNotification(title, message, type, options)
    return this.sendNotification(notification)
  }

  public async notifyInfo(title: string, message: string, actions?: NotificationAction[]): Promise<string> {
    return this.notify(title, message, 'info', { actions })
  }

  public async notifyWarning(title: string, message: string, actions?: NotificationAction[]): Promise<string> {
    return this.notify(title, message, 'warning', { 
      actions,
      severity: { level: 'medium', requiresAcknowledgment: false, blockingUi: false }
    })
  }

  public async notifyError(title: string, message: string, actions?: NotificationAction[]): Promise<string> {
    return this.notify(title, message, 'error', { 
      actions,
      severity: { level: 'high', requiresAcknowledgment: true, blockingUi: false },
      persistent: true
    })
  }

  public async notifySuccess(title: string, message: string, actions?: NotificationAction[]): Promise<string> {
    return this.notify(title, message, 'success', { 
      actions,
      severity: { level: 'low', requiresAcknowledgment: false, blockingUi: false, autoTimeout: 5000 }
    })
  }

  public async notifyProgress(title: string, message: string, progress?: number): Promise<string> {
    return this.notify(title, message, 'progress', {
      metadata: {
        context: { progress: progress || 0 },
        tags: ['progress'],
        relatedNotifications: [],
        analytics: this.createEmptyAnalytics()
      }
    })
  }

  public async sendNotification(notification: Notification): Promise<string> {
    // Check if notifications are enabled
    if (!this.preferences.enabled) {
      this.logger.debug('Notifications disabled, skipping:', notification.title)
      return notification.id
    }

    // Check do-not-disturb mode
    if (this.isDoNotDisturbActive() && !this.shouldBypassDoNotDisturb(notification)) {
      this.logger.debug('Do-not-disturb active, queuing notification:', notification.title)
      this.queue.notifications.push(notification)
      return notification.id
    }

    // Check category preferences
    const categoryPrefs = this.preferences.categories.get(notification.category.name)
    if (categoryPrefs && !categoryPrefs.enabled) {
      this.logger.debug('Category disabled, skipping notification:', notification.category.name)
      return notification.id
    }

    // Add to notifications
    this.notifications.set(notification.id, notification)
    
    // Update analytics
    notification.metadata.analytics.shown = new Date()
    
    // Show notification based on preferences
    await this.displayNotification(notification)
    
    // Update UI
    this.updateStatusBar()
    this.updateWebview()
    
    // Add to history
    this.addToHistory(notification)
    
    // Handle expiration
    if (notification.expiresAt) {
      const timeout = notification.expiresAt.getTime() - Date.now()
      if (timeout > 0) {
        setTimeout(() => {
          this.expireNotification(notification.id)
        }, timeout)
      }
    }

    this.logger.info('Notification sent:', notification.title)
    return notification.id
  }

  public async sendFromTemplate(
    templateId: string,
    variables: Record<string, any> = {},
    options?: Partial<Notification>
  ): Promise<string> {
    const template = this.templates.get(templateId)
    if (!template) {
      throw new Error(`Template not found: ${templateId}`)
    }

    // Check conditions
    if (!this.checkTemplateConditions(template, variables)) {
      this.logger.debug('Template conditions not met:', templateId)
      return ''
    }

    // Render template
    const notification = this.renderTemplate(template, variables, options)
    
    // Handle scheduling
    if (template.scheduling.immediate) {
      return this.sendNotification(notification)
    } else if (template.scheduling.delay) {
      return this.scheduleNotification(notification, template.scheduling.delay)
    }

    return notification.id
  }

  private async displayNotification(notification: Notification): Promise<void> {
    const categoryPrefs = this.preferences.categories.get(notification.category.name)
    
    // Show as VS Code notification
    if (!categoryPrefs || categoryPrefs.showAsPopup) {
      await this.showVSCodeNotification(notification)
    }

    // Show in sidebar/webview
    if (!categoryPrefs || categoryPrefs.showInSidebar) {
      this.updateWebview()
    }

    // Show in status bar
    if (!categoryPrefs || categoryPrefs.showInStatusBar) {
      this.updateStatusBar()
    }

    // Play sound
    if (this.preferences.behavior.playSound && (!categoryPrefs || categoryPrefs.enableSound)) {
      this.playNotificationSound(notification)
    }

    // Show badge
    if (this.preferences.behavior.showBadges && (!categoryPrefs || categoryPrefs.enableBadge)) {
      this.updateBadge()
    }
  }

  private async showVSCodeNotification(notification: Notification): Promise<void> {
    const actions = notification.actions.map(action => action.label)
    let selectedAction: string | undefined

    switch (notification.type.kind) {
      case 'error':
        selectedAction = await vscode.window.showErrorMessage(
          `${notification.title}: ${notification.message}`,
          ...actions
        )
        break
      case 'warning':
        selectedAction = await vscode.window.showWarningMessage(
          `${notification.title}: ${notification.message}`,
          ...actions
        )
        break
      case 'success':
      case 'info':
      default:
        selectedAction = await vscode.window.showInformationMessage(
          `${notification.title}: ${notification.message}`,
          ...actions
        )
        break
    }

    if (selectedAction) {
      const action = notification.actions.find(a => a.label === selectedAction)
      if (action) {
        await this.executeAction(notification.id, action)
      }
    }
  }

  // Notification management
  public markAsRead(notificationId: string): void {
    const notification = this.notifications.get(notificationId)
    if (notification && !notification.read) {
      notification.read = true
      notification.metadata.analytics.clicked = new Date()
      this.updateStatusBar()
      this.updateWebview()
      this.saveNotifications()
    }
  }

  public markAllAsRead(): void {
    for (const notification of this.notifications.values()) {
      if (!notification.read) {
        notification.read = true
        notification.metadata.analytics.clicked = new Date()
      }
    }
    this.updateStatusBar()
    this.updateWebview()
    this.saveNotifications()
  }

  public dismissNotification(notificationId: string): void {
    const notification = this.notifications.get(notificationId)
    if (notification) {
      notification.dismissed = true
      notification.metadata.analytics.dismissed = new Date()
      this.notifications.delete(notificationId)
      this.updateStatusBar()
      this.updateWebview()
      this.saveNotifications()
    }
  }

  public clearAllNotifications(): void {
    for (const notification of this.notifications.values()) {
      notification.dismissed = true
      notification.metadata.analytics.dismissed = new Date()
    }
    this.notifications.clear()
    this.updateStatusBar()
    this.updateWebview()
    this.saveNotifications()
  }

  public async executeAction(notificationId: string, action: NotificationAction): Promise<void> {
    const notification = this.notifications.get(notificationId)
    if (!notification) return

    // Update analytics
    notification.metadata.analytics.actionTaken = action.id
    notification.metadata.analytics.timeToAction = 
      Date.now() - notification.metadata.analytics.shown.getTime()
    notification.metadata.analytics.interactionCount++

    // Show confirmation if required
    if (action.requiresConfirmation) {
      const confirmed = await vscode.window.showWarningMessage(
        `Are you sure you want to ${action.label.toLowerCase()}?`,
        { modal: true },
        'Yes',
        'No'
      )
      
      if (confirmed !== 'Yes') {
        return
      }
    }

    try {
      // Execute command
      if (action.command) {
        await vscode.commands.executeCommand(action.command.command, ...(action.command.arguments || []))
      }

      // Execute callback
      if (action.callback) {
        await action.callback()
      }

      // Close notification if configured
      if (this.preferences.behavior.closeOnAction) {
        this.dismissNotification(notificationId)
      }

    } catch (error) {
      this.logger.error('Error executing notification action:', error)
      await vscode.window.showErrorMessage(`Failed to execute action: ${error.message}`)
    }
  }

  // Templates
  public registerTemplate(template: NotificationTemplate): void {
    this.templates.set(template.id, template)
    this.saveTemplates()
  }

  public unregisterTemplate(templateId: string): void {
    this.templates.delete(templateId)
    this.saveTemplates()
  }

  private renderTemplate(
    template: NotificationTemplate,
    variables: Record<string, any>,
    options?: Partial<Notification>
  ): Notification {
    // Simple template rendering (would use a real template engine in production)
    let title = template.title
    let message = template.message

    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`
      title = title.replace(new RegExp(placeholder, 'g'), String(value))
      message = message.replace(new RegExp(placeholder, 'g'), String(value))
    }

    return this.createNotification(title, message, template.type.kind, {
      ...options,
      type: template.type,
      severity: template.severity,
      category: { name: template.category, description: '', enabled: true, preferences: this.getDefaultCategoryPreferences() },
      actions: template.actions
    })
  }

  private checkTemplateConditions(template: NotificationTemplate, variables: Record<string, any>): boolean {
    for (const condition of template.conditions) {
      const value = variables[condition.field]
      
      switch (condition.operator) {
        case 'eq':
          if (value !== condition.value) return false
          break
        case 'neq':
          if (value === condition.value) return false
          break
        case 'gt':
          if (value <= condition.value) return false
          break
        case 'gte':
          if (value < condition.value) return false
          break
        case 'lt':
          if (value >= condition.value) return false
          break
        case 'lte':
          if (value > condition.value) return false
          break
        case 'contains':
          if (!String(value).includes(String(condition.value))) return false
          break
        case 'regex':
          if (!new RegExp(condition.value).test(String(value))) return false
          break
      }
    }
    
    return true
  }

  // Scheduling
  private scheduleNotification(notification: Notification, delay: number): string {
    const timeoutId = setTimeout(() => {
      this.sendNotification(notification)
      this.queue.scheduledNotifications.delete(notification.id)
    }, delay)

    this.queue.scheduledNotifications.set(notification.id, timeoutId)
    return notification.id
  }

  // Preferences and settings
  public updatePreferences(preferences: Partial<NotificationPreferences>): void {
    this.preferences = { ...this.preferences, ...preferences }
    this.savePreferences()
  }

  public toggleDoNotDisturb(): void {
    this.preferences.doNotDisturb = !this.preferences.doNotDisturb
    this.savePreferences()
    this.updateStatusBar()
    
    if (!this.preferences.doNotDisturb) {
      this.processQueuedNotifications()
    }

    const status = this.preferences.doNotDisturb ? 'enabled' : 'disabled'
    vscode.window.showInformationMessage(`Do Not Disturb ${status}`)
  }

  private isDoNotDisturbActive(): boolean {
    if (!this.preferences.doNotDisturb) return false
    
    const schedule = this.preferences.doNotDisturbSchedule
    if (!schedule.enabled) return true

    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    const currentDay = now.getDay()

    if (!schedule.days.includes(currentDay)) return false

    return currentTime >= schedule.startTime && currentTime <= schedule.endTime
  }

  private shouldBypassDoNotDisturb(notification: Notification): boolean {
    const schedule = this.preferences.doNotDisturbSchedule
    return schedule.exceptions.includes(notification.category.name) ||
           notification.priority.bypass ||
           notification.severity.level === 'critical' ||
           notification.severity.level === 'urgent'
  }

  // Queue management
  private startQueueProcessor(): void {
    setInterval(() => {
      this.processQueuedNotifications()
    }, 60000) // Check every minute
  }

  private async processQueuedNotifications(): Promise<void> {
    if (this.queue.processing || this.queue.notifications.length === 0) return

    this.queue.processing = true

    try {
      const notificationsToProcess = [...this.queue.notifications]
      this.queue.notifications = []

      for (const notification of notificationsToProcess) {
        if (!this.isDoNotDisturbActive() || this.shouldBypassDoNotDisturb(notification)) {
          await this.sendNotification(notification)
        } else {
          this.queue.notifications.push(notification) // Re-queue if still in DND
        }
      }
    } finally {
      this.queue.processing = false
    }
  }

  // UI management
  private updateStatusBar(): void {
    const unreadCount = Array.from(this.notifications.values()).filter(n => !n.read).length
    
    if (unreadCount === 0) {
      this.statusBarItem.hide()
      return
    }

    let text = `$(bell) ${unreadCount}`
    let tooltip = `${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}`
    
    if (this.preferences.doNotDisturb) {
      text = `$(bell-slash) ${unreadCount}`
      tooltip += ' (Do Not Disturb)'
    }

    this.statusBarItem.text = text
    this.statusBarItem.tooltip = tooltip
    this.statusBarItem.show()
  }

  private updateWebview(): void {
    if (this.webviewPanel) {
      this.webviewPanel.webview.postMessage({
        command: 'updateNotifications',
        data: Array.from(this.notifications.values())
      })
    }
  }

  private updateBadge(): void {
    const unreadCount = Array.from(this.notifications.values()).filter(n => !n.read).length
    // Would implement badge update for the extension icon
    this.logger.debug(`Badge count: ${unreadCount}`)
  }

  // Webview panels
  private async showNotificationPanel(): Promise<void> {
    if (this.webviewPanel) {
      this.webviewPanel.reveal()
      return
    }

    this.webviewPanel = vscode.window.createWebviewPanel(
      'notifications',
      'Notifications',
      vscode.ViewColumn.Beside,
      {
        enableScripts: true,
        retainContextWhenHidden: true
      }
    )

    this.webviewPanel.webview.html = this.getNotificationWebviewContent()
    
    this.webviewPanel.webview.onDidReceiveMessage(
      async (message) => {
        switch (message.command) {
          case 'markRead':
            this.markAsRead(message.notificationId)
            break
          case 'dismiss':
            this.dismissNotification(message.notificationId)
            break
          case 'executeAction':
            const notification = this.notifications.get(message.notificationId)
            const action = notification?.actions.find(a => a.id === message.actionId)
            if (action) {
              await this.executeAction(message.notificationId, action)
            }
            break
          case 'clearAll':
            this.clearAllNotifications()
            break
          case 'markAllRead':
            this.markAllAsRead()
            break
        }
      },
      undefined,
      this.disposables
    )

    this.webviewPanel.onDidDispose(() => {
      this.webviewPanel = undefined
    })

    this.updateWebview()
  }

  private async showHistoryPanel(): Promise<void> {
    // Implementation similar to showNotificationPanel but for history
    vscode.window.showInformationMessage('History panel not implemented in demo')
  }

  private async showPreferencesPanel(): Promise<void> {
    // Implementation similar to showNotificationPanel but for preferences
    vscode.window.showInformationMessage('Preferences panel not implemented in demo')
  }

  private getNotificationWebviewContent(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        
        .notification-item {
            border: 1px solid var(--vscode-widget-border);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: var(--vscode-input-background);
        }
        
        .notification-item.unread {
            border-left: 3px solid var(--vscode-focusBorder);
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .notification-title {
            font-weight: 600;
            margin: 0;
        }
        
        .notification-type {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .type-info { background: var(--vscode-charts-blue); color: white; }
        .type-warning { background: var(--vscode-charts-orange); color: white; }
        .type-error { background: var(--vscode-errorForeground); color: white; }
        .type-success { background: var(--vscode-charts-green); color: white; }
        
        .notification-message {
            margin: 8px 0;
            color: var(--vscode-descriptionForeground);
        }
        
        .notification-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }
        
        .btn {
            padding: 4px 8px;
            border: 1px solid var(--vscode-button-border);
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn:hover {
            background: var(--vscode-button-hoverBackground);
        }
        
        .btn-primary {
            background: var(--vscode-button-background);
        }
        
        .btn-destructive {
            background: var(--vscode-errorForeground);
            color: white;
        }
        
        .notification-meta {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            margin-top: 8px;
        }
        
        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            padding: 40px;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--vscode-widget-border);
        }
    </style>
</head>
<body>
    <div class="header-actions">
        <button class="btn" onclick="markAllRead()">Mark All Read</button>
        <button class="btn" onclick="clearAll()">Clear All</button>
    </div>
    
    <div id="notificationList">
        <div class="empty-state">Loading notifications...</div>
    </div>
    
    <script>
        const vscode = acquireVsCodeApi();
        
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'updateNotifications':
                    displayNotifications(message.data);
                    break;
            }
        });
        
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationList');
            
            if (notifications.length === 0) {
                container.innerHTML = '<div class="empty-state">No notifications</div>';
                return;
            }
            
            container.innerHTML = notifications.map(createNotificationHtml).join('');
        }
        
        function createNotificationHtml(notification) {
            const typeClass = 'type-' + notification.type.kind;
            const unreadClass = notification.read ? '' : 'unread';
            
            return \`
                <div class="notification-item \${unreadClass}">
                    <div class="notification-header">
                        <h3 class="notification-title">\${notification.title}</h3>
                        <span class="notification-type \${typeClass}">\${notification.type.kind}</span>
                    </div>
                    
                    <div class="notification-message">\${notification.message}</div>
                    
                    \${notification.actions.length > 0 ? \`
                        <div class="notification-actions">
                            \${notification.actions.map(action => \`
                                <button class="btn \${action.isPrimary ? 'btn-primary' : ''} \${action.isDestructive ? 'btn-destructive' : ''}"
                                        onclick="executeAction('\${notification.id}', '\${action.id}')">
                                    \${action.label}
                                </button>
                            \`).join('')}
                        </div>
                    \` : ''}
                    
                    <div class="notification-meta">
                        \${new Date(notification.timestamp).toLocaleString()} • 
                        \${notification.category.name}
                        \${!notification.read ? ' • <button class="btn" onclick="markRead(\\''+notification.id+'\\')">Mark Read</button>' : ''}
                        <button class="btn" onclick="dismiss('\${notification.id}')">Dismiss</button>
                    </div>
                </div>
            \`;
        }
        
        function markRead(id) {
            vscode.postMessage({ command: 'markRead', notificationId: id });
        }
        
        function dismiss(id) {
            vscode.postMessage({ command: 'dismiss', notificationId: id });
        }
        
        function executeAction(notificationId, actionId) {
            vscode.postMessage({ command: 'executeAction', notificationId, actionId });
        }
        
        function markAllRead() {
            vscode.postMessage({ command: 'markAllRead' });
        }
        
        function clearAll() {
            vscode.postMessage({ command: 'clearAll' });
        }
    </script>
</body>
</html>
    `
  }

  // Utility methods
  private createNotification(
    title: string,
    message: string,
    type: NotificationType['kind'],
    options?: Partial<Notification>
  ): Notification {
    const id = this.generateNotificationId()
    const now = new Date()

    return {
      id,
      title,
      message,
      type: {
        kind: type,
        icon: options?.type?.icon,
        sound: options?.type?.sound
      },
      severity: options?.severity || {
        level: type === 'error' ? 'high' : type === 'warning' ? 'medium' : 'low',
        requiresAcknowledgment: false,
        blockingUi: false
      },
      category: options?.category || {
        name: 'general',
        description: 'General notifications',
        enabled: true,
        preferences: this.getDefaultCategoryPreferences()
      },
      source: options?.source || 'extension',
      timestamp: now,
      read: false,
      dismissed: false,
      actions: options?.actions || [],
      metadata: options?.metadata || {
        context: {},
        tags: [],
        relatedNotifications: [],
        analytics: this.createEmptyAnalytics()
      },
      expiresAt: options?.expiresAt,
      priority: options?.priority || {
        level: 5,
        boost: false,
        bypass: false,
        escalate: false,
        escalationDelay: 0
      },
      persistent: options?.persistent || false,
      grouped: options?.grouped || false,
      groupId: options?.groupId
    }
  }

  private generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private createEmptyAnalytics(): NotificationAnalytics {
    return {
      shown: new Date(),
      interactionCount: 0
    }
  }

  private createEmptyStats(): NotificationStats {
    return {
      total: 0,
      read: 0,
      dismissed: 0,
      actioned: 0,
      expired: 0,
      byCategory: new Map(),
      byType: new Map(),
      bySeverity: new Map(),
      averageResponseTime: 0,
      mostActiveHours: []
    }
  }

  private getDefaultPreferences(): NotificationPreferences {
    return {
      enabled: true,
      doNotDisturb: false,
      doNotDisturbSchedule: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
        days: [0, 1, 2, 3, 4, 5, 6],
        exceptions: ['error', 'critical']
      },
      categories: new Map(),
      general: {
        maxNotifications: 50,
        autoCleanup: true,
        cleanupInterval: 24,
        retentionPeriod: 7,
        enableAnalytics: true,
        showNotificationCount: true,
        groupSimilarNotifications: true,
        enablePreview: true
      },
      appearance: {
        theme: 'system',
        position: 'top-right',
        size: 'medium',
        animation: true,
        showIcons: true,
        showTimestamp: true,
        opacity: 0.95,
        borderRadius: 6
      },
      behavior: {
        defaultTimeout: 5000,
        stackNotifications: true,
        maxStackSize: 5,
        playSound: true,
        showBadges: true,
        enableVibration: false,
        focusOnClick: true,
        closeOnAction: true,
        confirmDestructiveActions: true
      }
    }
  }

  private getDefaultCategoryPreferences(): CategoryPreferences {
    return {
      showInStatusBar: true,
      showAsPopup: true,
      showInSidebar: true,
      enableSound: true,
      enableBadge: true,
      respectDoNotDisturb: true,
      batchNotifications: false,
      maxBatchSize: 5,
      batchTimeout: 30000
    }
  }

  // Persistence methods
  private async loadPreferences(): Promise<void> {
    try {
      const saved = await this.storageService.get<NotificationPreferences>('notificationPreferences')
      if (saved) {
        this.preferences = { ...this.preferences, ...saved }
      }
    } catch (error) {
      this.logger.error('Error loading notification preferences:', error)
    }
  }

  private async savePreferences(): Promise<void> {
    try {
      await this.storageService.set('notificationPreferences', this.preferences)
    } catch (error) {
      this.logger.error('Error saving notification preferences:', error)
    }
  }

  private async loadHistory(): Promise<void> {
    try {
      const saved = await this.storageService.get<NotificationHistory>('notificationHistory')
      if (saved) {
        this.history = saved
      }
    } catch (error) {
      this.logger.error('Error loading notification history:', error)
    }
  }

  private async saveHistory(): Promise<void> {
    try {
      await this.storageService.set('notificationHistory', this.history)
    } catch (error) {
      this.logger.error('Error saving notification history:', error)
    }
  }

  private async loadTemplates(): Promise<void> {
    try {
      const saved = await this.storageService.get<Map<string, NotificationTemplate>>('notificationTemplates')
      if (saved) {
        this.templates = new Map(saved)
      }
    } catch (error) {
      this.logger.error('Error loading notification templates:', error)
    }
  }

  private async saveTemplates(): Promise<void> {
    try {
      await this.storageService.set('notificationTemplates', Array.from(this.templates.entries()))
    } catch (error) {
      this.logger.error('Error saving notification templates:', error)
    }
  }

  private async saveNotifications(): Promise<void> {
    try {
      await this.storageService.set('notifications', Array.from(this.notifications.entries()))
    } catch (error) {
      this.logger.error('Error saving notifications:', error)
    }
  }

  // Cleanup and maintenance
  private startCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    if (this.preferences.general.autoCleanup) {
      this.cleanupTimer = setInterval(() => {
        this.cleanupExpiredNotifications()
      }, this.preferences.general.cleanupInterval * 60 * 60 * 1000) // Convert hours to ms
    }
  }

  private cleanupExpiredNotifications(): void {
    const now = Date.now()
    const retentionPeriod = this.preferences.general.retentionPeriod * 24 * 60 * 60 * 1000 // Convert days to ms

    for (const [id, notification] of this.notifications) {
      const age = now - notification.timestamp.getTime()
      
      if (age > retentionPeriod && (notification.read || notification.dismissed)) {
        this.notifications.delete(id)
      }
    }

    this.updateStatusBar()
    this.updateWebview()
    this.saveNotifications()
  }

  private expireNotification(notificationId: string): void {
    const notification = this.notifications.get(notificationId)
    if (notification) {
      notification.dismissed = true
      this.notifications.delete(notificationId)
      this.updateStatusBar()
      this.updateWebview()
      this.saveNotifications()
    }
  }

  private addToHistory(notification: Notification): void {
    this.history.notifications.unshift({ ...notification })
    
    // Limit history size
    if (this.history.notifications.length > 1000) {
      this.history.notifications = this.history.notifications.slice(0, 1000)
    }

    this.updateStats()
    this.saveHistory()
  }

  private updateStats(): void {
    const notifications = this.history.notifications
    
    this.history.stats = {
      total: notifications.length,
      read: notifications.filter(n => n.read).length,
      dismissed: notifications.filter(n => n.dismissed).length,
      actioned: notifications.filter(n => n.metadata.analytics.actionTaken).length,
      expired: 0, // Would track expired notifications
      byCategory: new Map(),
      byType: new Map(),
      bySeverity: new Map(),
      averageResponseTime: 0,
      mostActiveHours: []
    }
  }

  // Testing and utilities
  private async sendTestNotification(): Promise<void> {
    await this.notifyInfo(
      'Test Notification',
      'This is a test notification to verify the system is working correctly.',
      [
        {
          id: 'test_action',
          label: 'Test Action',
          isPrimary: true,
          isDestructive: false,
          requiresConfirmation: false,
          callback: () => {
            vscode.window.showInformationMessage('Test action executed!')
          }
        }
      ]
    )
  }

  private playNotificationSound(notification: Notification): void {
    // Would implement sound playing
    this.logger.debug('Playing notification sound:', notification.type.sound?.type || 'default')
  }

  private async exportNotifications(): Promise<void> {
    const data = {
      notifications: Array.from(this.notifications.values()),
      history: this.history,
      preferences: this.preferences,
      exported: new Date().toISOString()
    }

    const uri = await vscode.window.showSaveDialog({
      defaultUri: vscode.Uri.file(`notifications-${Date.now()}.json`),
      filters: { 'JSON': ['json'] }
    })

    if (uri) {
      await vscode.workspace.fs.writeFile(uri, Buffer.from(JSON.stringify(data, null, 2)))
      vscode.window.showInformationMessage('Notifications exported successfully')
    }
  }

  public getNotifications(): Notification[] {
    return Array.from(this.notifications.values())
  }

  public getUnreadCount(): number {
    return Array.from(this.notifications.values()).filter(n => !n.read).length
  }

  public dispose(): void {
    this.statusBarItem.dispose()
    this.webviewPanel?.dispose()
    this.disposables.forEach(d => d.dispose())
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    // Clear scheduled notifications
    for (const timeout of this.queue.scheduledNotifications.values()) {
      clearTimeout(timeout)
    }
  }
}