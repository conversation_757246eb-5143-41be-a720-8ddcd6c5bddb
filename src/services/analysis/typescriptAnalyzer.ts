import * as ts from 'typescript'
import { FileSystemService } from '../fileSystemService'

export interface TypeScriptSymbol {
  name: string
  kind: ts.SyntaxKind
  type: string
  line: number
  column: number
  endLine: number
  endColumn: number
  scope: string
  signature?: string
  documentation?: string
  isExported: boolean
  modifiers: string[]
  filePath: string
}

export interface TypeScriptImport {
  module: string
  importType: 'import' | 'require' | 'dynamic'
  importClause: string
  defaultImport?: string
  namedImports: string[]
  namespaceImport?: string
  line: number
  column: number
  isTypeOnly: boolean
  isLocal: boolean
}

export interface TypeScriptExport {
  name: string
  type: 'named' | 'default' | 'namespace' | 'assignment'
  line: number
  column: number
  signature?: string
  isReexport: boolean
  originalModule?: string
}

export interface AnalysisResult {
  symbols: TypeScriptSymbol[]
  imports: TypeScriptImport[]
  exports: TypeScriptExport[]
  dependencies: string[]
  diagnostics: ts.Diagnostic[]
  sourceFile: ts.SourceFile
}

export class TypeScriptAnalyzer {
  private fileSystem: FileSystemService
  private compilerOptions: ts.CompilerOptions
  private program?: ts.Program
  private checker?: ts.TypeChecker

  constructor(fileSystem: FileSystemService) {
    this.fileSystem = fileSystem
    this.compilerOptions = {
      target: ts.ScriptTarget.ES2020,
      module: ts.ModuleKind.CommonJS,
      lib: ['es2020', 'dom'],
      allowJs: true,
      allowSyntheticDefaultImports: true,
      esModuleInterop: true,
      skipLibCheck: true,
      strict: false,
      noEmit: true,
      resolveJsonModule: true,
      moduleResolution: ts.ModuleResolutionKind.NodeJs
    }
  }

  async analyzeFile(filePath: string): Promise<AnalysisResult> {
    const content = await this.fileSystem.readFile(filePath)
    const sourceFile = ts.createSourceFile(
      filePath,
      content,
      ts.ScriptTarget.ES2020,
      true
    )

    const symbols: TypeScriptSymbol[] = []
    const imports: TypeScriptImport[] = []
    const exports: TypeScriptExport[] = []
    const dependencies: string[] = []

    this.visitNode(sourceFile, sourceFile, symbols, imports, exports, dependencies)

    const diagnostics = ts.getPreEmitDiagnostics(
      ts.createProgram([filePath], this.compilerOptions)
    )

    return {
      symbols,
      imports,
      exports,
      dependencies: [...new Set(dependencies)],
      diagnostics,
      sourceFile
    }
  }

  async analyzeProject(projectRoot: string, filePaths: string[]): Promise<Map<string, AnalysisResult>> {
    const results = new Map<string, AnalysisResult>()
    
    this.program = ts.createProgram(filePaths, this.compilerOptions)
    this.checker = this.program.getTypeChecker()

    for (const filePath of filePaths) {
      const sourceFile = this.program.getSourceFile(filePath)
      if (sourceFile) {
        const symbols: TypeScriptSymbol[] = []
        const imports: TypeScriptImport[] = []
        const exports: TypeScriptExport[] = []
        const dependencies: string[] = []

        this.visitNode(sourceFile, sourceFile, symbols, imports, exports, dependencies)

        const diagnostics = ts.getPreEmitDiagnostics(this.program, sourceFile)

        results.set(filePath, {
          symbols,
          imports,
          exports,
          dependencies: [...new Set(dependencies)],
          diagnostics,
          sourceFile
        })
      }
    }

    return results
  }

  private visitNode(
    node: ts.Node,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[],
    imports: TypeScriptImport[],
    exports: TypeScriptExport[],
    dependencies: string[]
  ): void {
    switch (node.kind) {
      case ts.SyntaxKind.FunctionDeclaration:
        this.analyzeFunctionDeclaration(node as ts.FunctionDeclaration, sourceFile, symbols)
        break
      case ts.SyntaxKind.ClassDeclaration:
        this.analyzeClassDeclaration(node as ts.ClassDeclaration, sourceFile, symbols)
        break
      case ts.SyntaxKind.InterfaceDeclaration:
        this.analyzeInterfaceDeclaration(node as ts.InterfaceDeclaration, sourceFile, symbols)
        break
      case ts.SyntaxKind.TypeAliasDeclaration:
        this.analyzeTypeAliasDeclaration(node as ts.TypeAliasDeclaration, sourceFile, symbols)
        break
      case ts.SyntaxKind.VariableDeclaration:
        this.analyzeVariableDeclaration(node as ts.VariableDeclaration, sourceFile, symbols)
        break
      case ts.SyntaxKind.ImportDeclaration:
        this.analyzeImportDeclaration(node as ts.ImportDeclaration, sourceFile, imports, dependencies)
        break
      case ts.SyntaxKind.ExportDeclaration:
        this.analyzeExportDeclaration(node as ts.ExportDeclaration, sourceFile, exports)
        break
      case ts.SyntaxKind.ExportAssignment:
        this.analyzeExportAssignment(node as ts.ExportAssignment, sourceFile, exports)
        break
    }

    ts.forEachChild(node, child => {
      this.visitNode(child, sourceFile, symbols, imports, exports, dependencies)
    })
  }

  private analyzeFunctionDeclaration(
    node: ts.FunctionDeclaration,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[]
  ): void {
    if (!node.name) return

    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(node.getEnd())
    const signature = this.getFunctionSignature(node)
    const documentation = this.getDocumentation(node)
    const modifiers = this.getModifiers(node)
    const isExported = this.isExported(node)

    symbols.push({
      name: node.name.text,
      kind: node.kind,
      type: 'function',
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: this.getScope(node),
      signature,
      documentation,
      isExported,
      modifiers,
      filePath: sourceFile.fileName
    })
  }

  private analyzeClassDeclaration(
    node: ts.ClassDeclaration,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[]
  ): void {
    if (!node.name) return

    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(node.getEnd())
    const signature = this.getClassSignature(node)
    const documentation = this.getDocumentation(node)
    const modifiers = this.getModifiers(node)
    const isExported = this.isExported(node)

    symbols.push({
      name: node.name.text,
      kind: node.kind,
      type: 'class',
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: this.getScope(node),
      signature,
      documentation,
      isExported,
      modifiers,
      filePath: sourceFile.fileName
    })

    // Analyze class members
    node.members.forEach(member => {
      if (ts.isMethodDeclaration(member) || ts.isPropertyDeclaration(member)) {
        this.analyzeClassMember(member, sourceFile, symbols, node.name!.text)
      }
    })
  }

  private analyzeInterfaceDeclaration(
    node: ts.InterfaceDeclaration,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[]
  ): void {
    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(node.getEnd())
    const signature = this.getInterfaceSignature(node)
    const documentation = this.getDocumentation(node)
    const modifiers = this.getModifiers(node)
    const isExported = this.isExported(node)

    symbols.push({
      name: node.name.text,
      kind: node.kind,
      type: 'interface',
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: this.getScope(node),
      signature,
      documentation,
      isExported,
      modifiers,
      filePath: sourceFile.fileName
    })
  }

  private analyzeTypeAliasDeclaration(
    node: ts.TypeAliasDeclaration,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[]
  ): void {
    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(node.getEnd())
    const signature = this.getTypeAliasSignature(node)
    const documentation = this.getDocumentation(node)
    const modifiers = this.getModifiers(node)
    const isExported = this.isExported(node)

    symbols.push({
      name: node.name.text,
      kind: node.kind,
      type: 'type',
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: this.getScope(node),
      signature,
      documentation,
      isExported,
      modifiers,
      filePath: sourceFile.fileName
    })
  }

  private analyzeVariableDeclaration(
    node: ts.VariableDeclaration,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[]
  ): void {
    if (!ts.isIdentifier(node.name)) return

    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(node.getEnd())
    const signature = this.getVariableSignature(node)
    const modifiers = this.getModifiers(node.parent as ts.VariableStatement)
    const isExported = this.isExported(node.parent as ts.VariableStatement)

    symbols.push({
      name: node.name.text,
      kind: node.kind,
      type: 'variable',
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: this.getScope(node),
      signature,
      isExported,
      modifiers,
      filePath: sourceFile.fileName
    })
  }

  private analyzeClassMember(
    member: ts.ClassElement,
    sourceFile: ts.SourceFile,
    symbols: TypeScriptSymbol[],
    className: string
  ): void {
    if (!member.name || !ts.isIdentifier(member.name)) return

    const position = sourceFile.getLineAndCharacterOfPosition(member.getStart())
    const endPosition = sourceFile.getLineAndCharacterOfPosition(member.getEnd())
    const signature = ts.isMethodDeclaration(member) ? 
      this.getMethodSignature(member) : 
      this.getPropertySignature(member as ts.PropertyDeclaration)
    const documentation = this.getDocumentation(member)
    const modifiers = this.getModifiers(member)
    const type = ts.isMethodDeclaration(member) ? 'method' : 'property'

    symbols.push({
      name: member.name.text,
      kind: member.kind,
      type,
      line: position.line + 1,
      column: position.character + 1,
      endLine: endPosition.line + 1,
      endColumn: endPosition.character + 1,
      scope: className,
      signature,
      documentation,
      isExported: false,
      modifiers,
      filePath: sourceFile.fileName
    })
  }

  private analyzeImportDeclaration(
    node: ts.ImportDeclaration,
    sourceFile: ts.SourceFile,
    imports: TypeScriptImport[],
    dependencies: string[]
  ): void {
    if (!node.moduleSpecifier || !ts.isStringLiteral(node.moduleSpecifier)) return

    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const module = node.moduleSpecifier.text
    const isTypeOnly = node.importClause?.isTypeOnly || false
    const isLocal = this.isLocalModule(module)
    
    dependencies.push(module)

    let defaultImport: string | undefined
    let namedImports: string[] = []
    let namespaceImport: string | undefined
    let importClause = ''

    if (node.importClause) {
      if (node.importClause.name) {
        defaultImport = node.importClause.name.text
        importClause = defaultImport
      }

      if (node.importClause.namedBindings) {
        if (ts.isNamespaceImport(node.importClause.namedBindings)) {
          namespaceImport = node.importClause.namedBindings.name.text
          importClause = `* as ${namespaceImport}`
        } else if (ts.isNamedImports(node.importClause.namedBindings)) {
          namedImports = node.importClause.namedBindings.elements.map(
            element => element.name.text
          )
          importClause = `{ ${namedImports.join(', ')} }`
        }
      }
    }

    imports.push({
      module,
      importType: 'import',
      importClause,
      defaultImport,
      namedImports,
      namespaceImport,
      line: position.line + 1,
      column: position.character + 1,
      isTypeOnly,
      isLocal
    })
  }

  private analyzeExportDeclaration(
    node: ts.ExportDeclaration,
    sourceFile: ts.SourceFile,
    exports: TypeScriptExport[]
  ): void {
    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const isReexport = !!node.moduleSpecifier

    if (node.exportClause && ts.isNamedExports(node.exportClause)) {
      node.exportClause.elements.forEach(element => {
        exports.push({
          name: element.name.text,
          type: 'named',
          line: position.line + 1,
          column: position.character + 1,
          isReexport,
          originalModule: node.moduleSpecifier && ts.isStringLiteral(node.moduleSpecifier) ? 
            node.moduleSpecifier.text : undefined
        })
      })
    } else if (!node.exportClause) {
      exports.push({
        name: '*',
        type: 'namespace',
        line: position.line + 1,
        column: position.character + 1,
        isReexport,
        originalModule: node.moduleSpecifier && ts.isStringLiteral(node.moduleSpecifier) ? 
          node.moduleSpecifier.text : undefined
      })
    }
  }

  private analyzeExportAssignment(
    node: ts.ExportAssignment,
    sourceFile: ts.SourceFile,
    exports: TypeScriptExport[]
  ): void {
    const position = sourceFile.getLineAndCharacterOfPosition(node.getStart())
    const name = node.isExportEquals ? 'export=' : 'default'

    exports.push({
      name,
      type: node.isExportEquals ? 'assignment' : 'default',
      line: position.line + 1,
      column: position.character + 1,
      isReexport: false
    })
  }

  private getFunctionSignature(node: ts.FunctionDeclaration): string {
    const params = node.parameters.map(param => {
      const name = param.name.getText()
      const type = param.type ? param.type.getText() : 'any'
      const optional = param.questionToken ? '?' : ''
      return `${name}${optional}: ${type}`
    }).join(', ')

    const returnType = node.type ? node.type.getText() : 'any'
    return `(${params}): ${returnType}`
  }

  private getClassSignature(node: ts.ClassDeclaration): string {
    const heritage = node.heritageClauses?.map(clause => {
      const keyword = clause.token === ts.SyntaxKind.ExtendsKeyword ? 'extends' : 'implements'
      const types = clause.types.map(type => type.getText()).join(', ')
      return `${keyword} ${types}`
    }).join(' ') || ''

    return `class ${node.name?.text}${heritage ? ' ' + heritage : ''}`
  }

  private getInterfaceSignature(node: ts.InterfaceDeclaration): string {
    const heritage = node.heritageClauses?.map(clause => {
      const types = clause.types.map(type => type.getText()).join(', ')
      return `extends ${types}`
    }).join(' ') || ''

    return `interface ${node.name.text}${heritage ? ' ' + heritage : ''}`
  }

  private getTypeAliasSignature(node: ts.TypeAliasDeclaration): string {
    return `type ${node.name.text} = ${node.type.getText()}`
  }

  private getVariableSignature(node: ts.VariableDeclaration): string {
    const name = node.name.getText()
    const type = node.type ? `: ${node.type.getText()}` : ''
    const initializer = node.initializer ? ` = ${node.initializer.getText()}` : ''
    return `${name}${type}${initializer}`
  }

  private getMethodSignature(node: ts.MethodDeclaration): string {
    const name = node.name?.getText() || ''
    const params = node.parameters.map(param => {
      const paramName = param.name.getText()
      const type = param.type ? param.type.getText() : 'any'
      const optional = param.questionToken ? '?' : ''
      return `${paramName}${optional}: ${type}`
    }).join(', ')

    const returnType = node.type ? node.type.getText() : 'any'
    return `${name}(${params}): ${returnType}`
  }

  private getPropertySignature(node: ts.PropertyDeclaration): string {
    const name = node.name?.getText() || ''
    const type = node.type ? `: ${node.type.getText()}` : ''
    const initializer = node.initializer ? ` = ${node.initializer.getText()}` : ''
    return `${name}${type}${initializer}`
  }

  private getDocumentation(node: ts.Node): string | undefined {
    const sourceFile = node.getSourceFile()
    const text = sourceFile.text
    const start = node.getFullStart()
    const commentRanges = ts.getLeadingCommentRanges(text, start)
    
    if (commentRanges && commentRanges.length > 0) {
      const lastComment = commentRanges[commentRanges.length - 1]
      const commentText = text.substring(lastComment.pos, lastComment.end)
      return commentText.trim()
    }
    
    return undefined
  }

  private getModifiers(node: ts.Node): string[] {
    const modifiers: string[] = []
    
    if (ts.canHaveModifiers(node)) {
      const nodeModifiers = ts.getModifiers(node)
      if (nodeModifiers) {
        nodeModifiers.forEach(modifier => {
          modifiers.push(ts.SyntaxKind[modifier.kind].toLowerCase())
        })
      }
    }
    
    return modifiers
  }

  private isExported(node: ts.Node): boolean {
    const modifiers = this.getModifiers(node)
    return modifiers.includes('export')
  }

  private getScope(node: ts.Node): string {
    let parent = node.parent
    while (parent) {
      if (ts.isClassDeclaration(parent) && parent.name) {
        return parent.name.text
      }
      if (ts.isInterfaceDeclaration(parent)) {
        return parent.name.text
      }
      if (ts.isNamespaceDeclaration(parent)) {
        return parent.name.text
      }
      parent = parent.parent
    }
    return 'global'
  }

  private isLocalModule(module: string): boolean {
    return module.startsWith('./') || module.startsWith('../') || module.startsWith('/')
  }
}