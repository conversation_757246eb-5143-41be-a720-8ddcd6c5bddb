# Assets for Natural Language Task Transformer

This directory contains visual assets for the VSCode extension marketplace and documentation.

## Files

### Icon
- `icon.png` - Main extension icon (128x128px)
- `icon.svg` - Vector version of the icon

### Screenshots
- `screenshot-1-transform.png` - Main task transformation feature
- `screenshot-2-suggestions.png` - Context-aware suggestions
- `screenshot-3-settings.png` - Configuration panel
- `screenshot-4-output.png` - Generated task output example

### Banners
- `banner.png` - Marketplace banner (1024x512px)
- `banner-small.png` - Small banner (560x280px)

### Promotional
- `demo.gif` - Animated demo of core features
- `logo.png` - Project logo for documentation

## Usage

### Extension Marketplace
- `icon.png` is used as the extension icon
- `banner.png` is used as the marketplace banner
- Screenshots are displayed in the marketplace gallery

### Documentation
- Screenshots are embedded in README and documentation
- `demo.gif` is used for the main README demo
- `logo.png` is used in documentation headers

## Design Guidelines

### Colors
- Primary: #007ACC (VS Code blue)
- Secondary: #4CAF50 (AI green)
- Accent: #FF6B35 (Action orange)
- Background: #1E1E1E (Dark theme)

### Typography
- Headers: "Segoe UI", sans-serif
- Body: "Cascadia Code", monospace
- Logo: "Inter", sans-serif

### Icon Design
- Minimalist design following VS Code guidelines
- High contrast for visibility
- Scalable from 16x16 to 128x128 pixels
- Meaningful symbolism (task/AI transformation)

## Creating Screenshots

### Setup
1. Use VS Code with default dark theme
2. Set window size to 1200x800 pixels
3. Use consistent project structure across screenshots
4. Show realistic code examples

### Screenshot Checklist
- [ ] High resolution (at least 1200px wide)
- [ ] Dark theme for consistency
- [ ] Clear text and UI elements
- [ ] Relevant code examples
- [ ] Extension features clearly visible
- [ ] No sensitive information visible

### Tools
- macOS: Cmd+Shift+4 for area selection
- Windows: Snipping Tool or Print Screen
- Linux: gnome-screenshot or scrot
- Optional: Tools like CleanShot X for enhanced screenshots

## File Specifications

### Icon Requirements
- Format: PNG
- Size: 128x128 pixels
- Background: Transparent
- DPI: 72 (web) or 144 (retina)

### Screenshot Requirements
- Format: PNG or JPG
- Width: 1200-1920 pixels
- Height: 800-1200 pixels
- File size: < 2MB each

### Banner Requirements
- Format: PNG
- Size: 1024x512 pixels (2:1 ratio)
- Include extension name and tagline
- High contrast text

## Maintenance

### Regular Updates
- Update screenshots when UI changes
- Refresh demo content periodically
- Update banners for major releases
- Maintain consistent visual style

### Version Control
- Include all assets in Git repository
- Use descriptive commit messages for asset changes
- Tag asset versions with release versions
- Archive old versions for reference

## Legal

All assets should be:
- Original creations or properly licensed
- Free of copyrighted material
- Compliant with VS Code marketplace guidelines
- Accessible (proper contrast ratios)

## Contributing

When contributing assets:
1. Follow the design guidelines
2. Provide source files when possible
3. Include descriptions of changes
4. Test assets at different sizes
5. Verify accessibility compliance