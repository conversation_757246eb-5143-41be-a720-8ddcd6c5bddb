import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { UserInteraction, PreferenceTracker } from './preferenceTracker'

export interface CodePattern {
  id: string
  name: string
  description: string
  type: PatternType
  confidence: number
  frequency: number
  examples: PatternExample[]
  metadata: PatternMetadata
  relationships: PatternRelationship[]
  lastSeen: Date
  createdAt: Date
}

export interface PatternType {
  category: 'syntax' | 'structure' | 'behavior' | 'naming' | 'usage' | 'architectural'
  subcategory: string
  language?: string
  scope: 'file' | 'function' | 'class' | 'project' | 'workspace'
}

export interface PatternExample {
  id: string
  filePath: string
  startLine: number
  endLine: number
  code: string
  context: string
  timestamp: Date
  confidence: number
}

export interface PatternMetadata {
  size: number
  complexity: number
  maintainability: number
  reusability: number
  documentation: number
  testCoverage: number
  performance: number
}

export interface PatternRelationship {
  relatedPatternId: string
  relationshipType: 'similar' | 'complementary' | 'conflicting' | 'prerequisite' | 'evolution'
  strength: number
  description: string
}

export interface WorkflowPattern {
  id: string
  name: string
  description: string
  sequence: WorkflowStep[]
  frequency: number
  successRate: number
  averageDuration: number
  conditions: WorkflowCondition[]
  outcomes: WorkflowOutcome[]
}

export interface WorkflowStep {
  action: string
  context: any
  timestamp: Date
  duration: number
  success: boolean
}

export interface WorkflowCondition {
  type: 'time' | 'file_type' | 'project_size' | 'user_experience' | 'context'
  value: any
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in_range'
}

export interface WorkflowOutcome {
  type: 'success' | 'failure' | 'partial' | 'abandoned'
  metric: string
  value: number
  impact: 'high' | 'medium' | 'low'
}

export interface AntiPattern {
  id: string
  name: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  frequency: number
  examples: PatternExample[]
  remediation: string
  relatedPatterns: string[]
}

export interface PatternInsight {
  type: 'emerging' | 'declining' | 'stable' | 'anomaly'
  pattern: CodePattern
  description: string
  recommendation: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  timeline: Date[]
}

export interface RecognitionResult {
  patterns: CodePattern[]
  workflows: WorkflowPattern[]
  antiPatterns: AntiPattern[]
  insights: PatternInsight[]
  confidence: number
  processingTime: number
}

export class PatternRecognizer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private preferenceTracker: PreferenceTracker
  private patterns: Map<string, CodePattern>
  private workflows: Map<string, WorkflowPattern>
  private antiPatterns: Map<string, AntiPattern>
  private analysisCache: Map<string, RecognitionResult>

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    preferenceTracker: PreferenceTracker
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.preferenceTracker = preferenceTracker
    this.patterns = new Map()
    this.workflows = new Map()
    this.antiPatterns = new Map()
    this.analysisCache = new Map()
  }

  async analyzeCode(filePath: string): Promise<RecognitionResult> {
    const startTime = Date.now()
    const cacheKey = `${filePath}_${await this.getFileHash(filePath)}`
    
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!
    }

    try {
      const content = await this.fileSystem.readFile(filePath)
      const language = this.detectLanguage(filePath)
      
      const patterns = await this.recognizeCodePatterns(content, language, filePath)
      const workflows = await this.recognizeWorkflowPatterns(filePath)
      const antiPatterns = await this.recognizeAntiPatterns(content, language, filePath)
      const insights = await this.generateInsights(patterns, workflows, antiPatterns)
      
      const result: RecognitionResult = {
        patterns,
        workflows,
        antiPatterns,
        insights,
        confidence: this.calculateOverallConfidence(patterns, workflows, antiPatterns),
        processingTime: Date.now() - startTime
      }
      
      this.analysisCache.set(cacheKey, result)
      return result
    } catch (error) {
      console.error('Error analyzing code patterns:', error)
      return {
        patterns: [],
        workflows: [],
        antiPatterns: [],
        insights: [],
        confidence: 0,
        processingTime: Date.now() - startTime
      }
    }
  }

  private async recognizeCodePatterns(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    // Common patterns to recognize
    const patternRecognizers = [
      this.recognizeSingletonPattern,
      this.recognizeFactoryPattern,
      this.recognizeObserverPattern,
      this.recognizeRepositoryPattern,
      this.recognizeServicePattern,
      this.recognizeFunctionComposition,
      this.recognizeErrorHandlingPattern,
      this.recognizeConfigurationPattern
    ]
    
    for (const recognizer of patternRecognizers) {
      try {
        const recognizedPatterns = await recognizer.call(this, content, language, filePath)
        patterns.push(...recognizedPatterns)
      } catch (error) {
        console.error('Error in pattern recognizer:', error)
      }
    }
    
    return patterns
  }

  private async recognizeSingletonPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    if (language === 'typescript' || language === 'javascript') {
      // Look for singleton patterns
      const singletonRegex = /class\s+(\w+)\s*{[^}]*private\s+static\s+instance[^}]*getInstance\s*\(/gs
      const matches = content.matchAll(singletonRegex)
      
      for (const match of matches) {
        const className = match[1]
        const startLine = content.substring(0, match.index).split('\n').length
        
        patterns.push({
          id: `singleton_${className}_${filePath}`,
          name: 'Singleton Pattern',
          description: `Singleton implementation for ${className}`,
          type: {
            category: 'architectural',
            subcategory: 'creational',
            language,
            scope: 'class'
          },
          confidence: 0.8,
          frequency: 1,
          examples: [{
            id: `example_${Date.now()}`,
            filePath,
            startLine,
            endLine: startLine + 10,
            code: match[0],
            context: 'class_definition',
            timestamp: new Date(),
            confidence: 0.8
          }],
          metadata: {
            size: match[0].length,
            complexity: 0.6,
            maintainability: 0.7,
            reusability: 0.8,
            documentation: 0.5,
            testCoverage: 0.5,
            performance: 0.8
          },
          relationships: [],
          lastSeen: new Date(),
          createdAt: new Date()
        })
      }
    }
    
    return patterns
  }

  private async recognizeFactoryPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    if (language === 'typescript' || language === 'javascript') {
      const factoryRegex = /(?:class|function)\s+(\w*Factory\w*)|create\w+\s*\([^)]*\)\s*:\s*\w+/gs
      const matches = content.matchAll(factoryRegex)
      
      for (const match of matches) {
        const startLine = content.substring(0, match.index).split('\n').length
        
        patterns.push({
          id: `factory_${match[1] || 'create'}_${filePath}`,
          name: 'Factory Pattern',
          description: 'Factory method or class for object creation',
          type: {
            category: 'architectural',
            subcategory: 'creational',
            language,
            scope: 'function'
          },
          confidence: 0.7,
          frequency: 1,
          examples: [{
            id: `example_${Date.now()}`,
            filePath,
            startLine,
            endLine: startLine + 5,
            code: match[0],
            context: 'factory_method',
            timestamp: new Date(),
            confidence: 0.7
          }],
          metadata: {
            size: match[0].length,
            complexity: 0.5,
            maintainability: 0.8,
            reusability: 0.9,
            documentation: 0.6,
            testCoverage: 0.6,
            performance: 0.7
          },
          relationships: [],
          lastSeen: new Date(),
          createdAt: new Date()
        })
      }
    }
    
    return patterns
  }

  private async recognizeObserverPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const observerRegex = /(?:addEventListener|on\w+|subscribe|emit|notify)/gi
    const matches = content.matchAll(observerRegex)
    
    if (Array.from(matches).length > 2) {
      patterns.push({
        id: `observer_${filePath}`,
        name: 'Observer Pattern',
        description: 'Event-driven observer pattern implementation',
        type: {
          category: 'behavioral',
          subcategory: 'observer',
          language,
          scope: 'file'
        },
        confidence: 0.6,
        frequency: 1,
        examples: [],
        metadata: {
          size: content.length,
          complexity: 0.7,
          maintainability: 0.6,
          reusability: 0.8,
          documentation: 0.5,
          testCoverage: 0.4,
          performance: 0.7
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeRepositoryPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const repoRegex = /(?:class|interface)\s+(\w*Repository\w*)|(?:find|create|update|delete|save)\w*\s*\([^)]*\)/gs
    const matches = content.matchAll(repoRegex)
    
    const matchArray = Array.from(matches)
    if (matchArray.length > 3) {
      patterns.push({
        id: `repository_${filePath}`,
        name: 'Repository Pattern',
        description: 'Data access layer implementation',
        type: {
          category: 'architectural',
          subcategory: 'data_access',
          language,
          scope: 'class'
        },
        confidence: 0.8,
        frequency: 1,
        examples: [],
        metadata: {
          size: content.length,
          complexity: 0.6,
          maintainability: 0.8,
          reusability: 0.9,
          documentation: 0.7,
          testCoverage: 0.8,
          performance: 0.7
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeServicePattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const serviceRegex = /(?:class|interface)\s+(\w*Service\w*)/gs
    const matches = content.matchAll(serviceRegex)
    
    for (const match of matches) {
      const serviceName = match[1]
      patterns.push({
        id: `service_${serviceName}_${filePath}`,
        name: 'Service Pattern',
        description: `Service layer implementation: ${serviceName}`,
        type: {
          category: 'architectural',
          subcategory: 'service_layer',
          language,
          scope: 'class'
        },
        confidence: 0.7,
        frequency: 1,
        examples: [],
        metadata: {
          size: match[0].length,
          complexity: 0.5,
          maintainability: 0.8,
          reusability: 0.8,
          documentation: 0.6,
          testCoverage: 0.7,
          performance: 0.7
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeFunctionComposition(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const compositionRegex = /(?:compose|pipe|chain)\s*\(/gs
    const matches = content.matchAll(compositionRegex)
    
    if (Array.from(matches).length > 0) {
      patterns.push({
        id: `composition_${filePath}`,
        name: 'Function Composition',
        description: 'Functional composition patterns',
        type: {
          category: 'behavior',
          subcategory: 'functional',
          language,
          scope: 'function'
        },
        confidence: 0.6,
        frequency: 1,
        examples: [],
        metadata: {
          size: content.length,
          complexity: 0.8,
          maintainability: 0.9,
          reusability: 0.9,
          documentation: 0.5,
          testCoverage: 0.6,
          performance: 0.8
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeErrorHandlingPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const errorRegex = /try\s*{[^}]*catch\s*\([^)]*\)|\.catch\s*\(|Promise\.reject|throw\s+new\s+Error/gs
    const matches = content.matchAll(errorRegex)
    
    const matchCount = Array.from(matches).length
    if (matchCount > 2) {
      patterns.push({
        id: `error_handling_${filePath}`,
        name: 'Error Handling Pattern',
        description: 'Comprehensive error handling implementation',
        type: {
          category: 'behavior',
          subcategory: 'error_handling',
          language,
          scope: 'file'
        },
        confidence: 0.7,
        frequency: 1,
        examples: [],
        metadata: {
          size: content.length,
          complexity: 0.6,
          maintainability: 0.8,
          reusability: 0.7,
          documentation: 0.6,
          testCoverage: 0.8,
          performance: 0.7
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeConfigurationPattern(content: string, language: string, filePath: string): Promise<CodePattern[]> {
    const patterns: CodePattern[] = []
    
    const configRegex = /(?:config|settings|options|env)\s*[=:]/gi
    const matches = content.matchAll(configRegex)
    
    if (Array.from(matches).length > 3) {
      patterns.push({
        id: `configuration_${filePath}`,
        name: 'Configuration Pattern',
        description: 'Configuration management pattern',
        type: {
          category: 'structural',
          subcategory: 'configuration',
          language,
          scope: 'file'
        },
        confidence: 0.6,
        frequency: 1,
        examples: [],
        metadata: {
          size: content.length,
          complexity: 0.4,
          maintainability: 0.9,
          reusability: 0.8,
          documentation: 0.7,
          testCoverage: 0.5,
          performance: 0.8
        },
        relationships: [],
        lastSeen: new Date(),
        createdAt: new Date()
      })
    }
    
    return patterns
  }

  private async recognizeWorkflowPatterns(filePath: string): Promise<WorkflowPattern[]> {
    const interactions = this.preferenceTracker.getInteractionHistory()
    const fileInteractions = interactions.filter(i => i.context.filePath === filePath)
    
    if (fileInteractions.length < 5) {
      return []
    }
    
    const patterns: WorkflowPattern[] = []
    
    // Analyze sequence patterns
    const sequences = this.extractSequences(fileInteractions)
    
    for (const sequence of sequences) {
      if (sequence.length >= 3) {
        patterns.push({
          id: `workflow_${filePath}_${Date.now()}`,
          name: 'File Workflow Pattern',
          description: 'Common workflow sequence for this file',
          sequence: sequence.map(interaction => ({
            action: interaction.type.action,
            context: interaction.context,
            timestamp: interaction.timestamp,
            duration: 0, // Would need to calculate
            success: interaction.outcome.success
          })),
          frequency: 1,
          successRate: sequence.filter(i => i.outcome.success).length / sequence.length,
          averageDuration: 0,
          conditions: [],
          outcomes: []
        })
      }
    }
    
    return patterns
  }

  private extractSequences(interactions: UserInteraction[]): UserInteraction[][] {
    const sequences: UserInteraction[][] = []
    const sessionGapMs = 10 * 60 * 1000 // 10 minutes
    
    let currentSequence: UserInteraction[] = []
    let lastTimestamp = 0
    
    for (const interaction of interactions) {
      const timestamp = interaction.timestamp.getTime()
      
      if (timestamp - lastTimestamp > sessionGapMs) {
        if (currentSequence.length > 0) {
          sequences.push(currentSequence)
        }
        currentSequence = [interaction]
      } else {
        currentSequence.push(interaction)
      }
      
      lastTimestamp = timestamp
    }
    
    if (currentSequence.length > 0) {
      sequences.push(currentSequence)
    }
    
    return sequences
  }

  private async recognizeAntiPatterns(content: string, language: string, filePath: string): Promise<AntiPattern[]> {
    const antiPatterns: AntiPattern[] = []
    
    // God class/function anti-pattern
    const lines = content.split('\n')
    if (lines.length > 500) {
      antiPatterns.push({
        id: `god_class_${filePath}`,
        name: 'God Class/File',
        description: 'File is too large and likely has too many responsibilities',
        severity: 'medium',
        frequency: 1,
        examples: [],
        remediation: 'Consider breaking this file into smaller, more focused modules',
        relatedPatterns: []
      })
    }
    
    // Deep nesting anti-pattern
    const deepNestingRegex = /\s{20,}/g
    const deepNestingMatches = content.match(deepNestingRegex)
    if (deepNestingMatches && deepNestingMatches.length > 5) {
      antiPatterns.push({
        id: `deep_nesting_${filePath}`,
        name: 'Deep Nesting',
        description: 'Code has excessive nesting levels',
        severity: 'medium',
        frequency: 1,
        examples: [],
        remediation: 'Consider using early returns or extracting functions to reduce nesting',
        relatedPatterns: []
      })
    }
    
    // Hardcoded values anti-pattern
    const hardcodedRegex = /(?:["'](?:http|https|ftp|localhost|127\.0\.0\.1)[^"']*["']|[0-9]{4,})/g
    const hardcodedMatches = content.match(hardcodedRegex)
    if (hardcodedMatches && hardcodedMatches.length > 3) {
      antiPatterns.push({
        id: `hardcoded_values_${filePath}`,
        name: 'Hardcoded Values',
        description: 'Contains hardcoded URLs, ports, or magic numbers',
        severity: 'low',
        frequency: 1,
        examples: [],
        remediation: 'Move hardcoded values to configuration files or constants',
        relatedPatterns: []
      })
    }
    
    return antiPatterns
  }

  private async generateInsights(
    patterns: CodePattern[],
    workflows: WorkflowPattern[],
    antiPatterns: AntiPattern[]
  ): Promise<PatternInsight[]> {
    const insights: PatternInsight[] = []
    
    // Analyze pattern trends
    for (const pattern of patterns) {
      if (pattern.confidence > 0.8) {
        insights.push({
          type: 'stable',
          pattern,
          description: `Strong ${pattern.name} implementation detected`,
          recommendation: `Consider documenting this pattern for team reference`,
          confidence: pattern.confidence,
          impact: 'medium',
          timeline: [pattern.createdAt]
        })
      }
    }
    
    // Highlight emerging patterns
    const newPatterns = patterns.filter(p => p.frequency === 1 && p.confidence > 0.6)
    for (const pattern of newPatterns) {
      insights.push({
        type: 'emerging',
        pattern,
        description: `New ${pattern.name} pattern emerging`,
        recommendation: `Monitor usage and consider standardizing if it proves beneficial`,
        confidence: pattern.confidence,
        impact: 'low',
        timeline: [pattern.createdAt]
      })
    }
    
    return insights
  }

  private calculateOverallConfidence(
    patterns: CodePattern[],
    workflows: WorkflowPattern[],
    antiPatterns: AntiPattern[]
  ): number {
    if (patterns.length === 0) return 0
    
    const avgPatternConfidence = patterns.reduce((sum, p) => sum + p.confidence, 0) / patterns.length
    const workflowBonus = workflows.length > 0 ? 0.1 : 0
    const antiPatternPenalty = antiPatterns.length * 0.05
    
    return Math.max(0, Math.min(1, avgPatternConfidence + workflowBonus - antiPatternPenalty))
  }

  private async getFileHash(filePath: string): Promise<string> {
    try {
      const stats = await this.fileSystem.stat(filePath)
      return `${stats.size}_${stats.mtime.getTime()}`
    } catch (error) {
      return `${Date.now()}`
    }
  }

  private detectLanguage(filePath: string): string {
    const ext = this.fileSystem.extname(filePath).toLowerCase()
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.rb': 'ruby',
      '.php': 'php'
    }
    return languageMap[ext] || 'unknown'
  }

  getPatterns(): CodePattern[] {
    return Array.from(this.patterns.values())
  }

  getWorkflows(): WorkflowPattern[] {
    return Array.from(this.workflows.values())
  }

  getAntiPatterns(): AntiPattern[] {
    return Array.from(this.antiPatterns.values())
  }

  clearCache(): void {
    this.analysisCache.clear()
  }
}