import * as vscode from 'vscode'
import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { TypeScriptAnalyzer } from '../analysis/typescriptAnalyzer'
import { PythonAnalyzer } from '../analysis/pythonAnalyzer'
import { <PERSON><PERSON><PERSON><PERSON>ognizer } from '../memory/patternRecognizer'
import { ContextA<PERSON>yzer } from '../context/contextAnalyzer'

export interface SelectionAnalysis {
  selection: SelectionInfo
  content: ContentAnalysis
  structure: StructuralAnalysis
  semantics: SemanticAnalysis
  intent: IntentAnalysis
  suggestions: SelectionSuggestion[]
  metrics: SelectionMetrics
  relationships: RelationshipAnalysis
  quality: QualityAnalysis
  opportunities: OpportunityAnalysis
}

export interface SelectionInfo {
  range: vscode.Range
  text: string
  language: string
  filePath: string
  size: SelectionSize
  boundaries: SelectionBoundaries
  completeness: CompletenessAnalysis
  context: SelectionContext
}

export interface SelectionSize {
  characters: number
  lines: number
  tokens: number
  bytes: number
  complexity: number
  depth: number
}

export interface SelectionBoundaries {
  type: 'complete' | 'partial' | 'cross_boundary' | 'invalid'
  startContext: BoundaryContext
  endContext: BoundaryContext
  suggestedExpansion?: vscode.Range
  confidence: number
}

export interface BoundaryContext {
  tokenType: string
  syntaxElement: string
  indentLevel: number
  isLineStart: boolean
  isLineEnd: boolean
  isBlockBoundary: boolean
}

export interface CompletenessAnalysis {
  isSyntacticallyComplete: boolean
  isSemanticallyComplete: boolean
  isLogicallyComplete: boolean
  missingElements: string[]
  confidence: number
}

export interface SelectionContext {
  parentElement?: CodeElement
  siblingElements: CodeElement[]
  childElements: CodeElement[]
  scopeChain: ScopeInfo[]
  nearbyCode: string
}

export interface CodeElement {
  type: 'function' | 'class' | 'method' | 'variable' | 'statement' | 'expression' | 'block' | 'comment'
  name?: string
  range: vscode.Range
  signature?: string
  visibility?: 'public' | 'private' | 'protected'
  isExported?: boolean
  complexity: number
}

export interface ScopeInfo {
  type: 'global' | 'module' | 'class' | 'function' | 'block' | 'loop' | 'conditional'
  name?: string
  range: vscode.Range
  variables: VariableInfo[]
  level: number
}

export interface VariableInfo {
  name: string
  type?: string
  range: vscode.Range
  isParameter: boolean
  isConstant: boolean
  usageCount: number
  lastUsage: vscode.Position
}

export interface ContentAnalysis {
  type: ContentType
  categories: ContentCategory[]
  keywords: KeywordAnalysis
  literals: LiteralAnalysis
  identifiers: IdentifierAnalysis
  operators: OperatorAnalysis
  comments: CommentAnalysis
}

export interface ContentType {
  primary: 'code' | 'comment' | 'string' | 'documentation' | 'data' | 'mixed'
  secondary: string[]
  confidence: number
  reasoning: string[]
}

export interface ContentCategory {
  category: string
  percentage: number
  elements: string[]
  confidence: number
}

export interface KeywordAnalysis {
  keywords: string[]
  languageKeywords: string[]
  customKeywords: string[]
  frequency: Record<string, number>
  context: Record<string, string[]>
}

export interface LiteralAnalysis {
  strings: StringLiteral[]
  numbers: NumberLiteral[]
  booleans: BooleanLiteral[]
  nulls: NullLiteral[]
  patterns: LiteralPattern[]
}

export interface StringLiteral {
  value: string
  type: 'single' | 'double' | 'template' | 'regex'
  range: vscode.Range
  isMultiline: boolean
  containsInterpolation: boolean
}

export interface NumberLiteral {
  value: number | string
  type: 'integer' | 'float' | 'scientific' | 'hex' | 'binary' | 'octal'
  range: vscode.Range
  isConstant: boolean
}

export interface BooleanLiteral {
  value: boolean
  range: vscode.Range
  context: string
}

export interface NullLiteral {
  type: 'null' | 'undefined' | 'None' | 'nil'
  range: vscode.Range
  context: string
}

export interface LiteralPattern {
  pattern: string
  occurrences: number
  type: 'magic_number' | 'hardcoded_string' | 'repeated_value'
  severity: 'low' | 'medium' | 'high'
}

export interface IdentifierAnalysis {
  variables: IdentifierInfo[]
  functions: IdentifierInfo[]
  classes: IdentifierInfo[]
  modules: IdentifierInfo[]
  namingConventions: NamingConventionAnalysis
  undefinedReferences: string[]
}

export interface IdentifierInfo {
  name: string
  type: string
  range: vscode.Range
  definition?: vscode.Location
  references: vscode.Location[]
  scope: string
  isDefinition: boolean
  isUsage: boolean
}

export interface NamingConventionAnalysis {
  camelCase: number
  snakeCase: number
  pascalCase: number
  kebabCase: number
  constantCase: number
  consistency: number
  violations: NamingViolation[]
}

export interface NamingViolation {
  identifier: string
  expected: string
  actual: string
  severity: 'low' | 'medium' | 'high'
  suggestion: string
}

export interface OperatorAnalysis {
  arithmetic: OperatorInfo[]
  logical: OperatorInfo[]
  comparison: OperatorInfo[]
  assignment: OperatorInfo[]
  bitwise: OperatorInfo[]
  complexity: number
  patterns: OperatorPattern[]
}

export interface OperatorInfo {
  operator: string
  count: number
  positions: vscode.Range[]
  context: string[]
}

export interface OperatorPattern {
  pattern: string
  count: number
  complexity: number
  suggestion?: string
}

export interface CommentAnalysis {
  totalComments: number
  types: CommentType[]
  coverage: number
  quality: number
  todos: TodoItem[]
  documentation: DocumentationInfo[]
}

export interface CommentType {
  type: 'single_line' | 'multi_line' | 'documentation' | 'todo' | 'fixme' | 'note'
  count: number
  ranges: vscode.Range[]
}

export interface TodoItem {
  type: 'TODO' | 'FIXME' | 'HACK' | 'NOTE' | 'BUG'
  text: string
  range: vscode.Range
  priority: 'low' | 'medium' | 'high'
  assignee?: string
}

export interface DocumentationInfo {
  type: 'jsdoc' | 'pydoc' | 'inline' | 'markdown'
  content: string
  range: vscode.Range
  completeness: number
  quality: number
}

export interface StructuralAnalysis {
  syntaxTree: SyntaxNode
  hierarchy: HierarchyInfo
  dependencies: DependencyInfo[]
  complexity: ComplexityMetrics
  patterns: StructuralPattern[]
  cohesion: CohesionAnalysis
}

export interface SyntaxNode {
  type: string
  range: vscode.Range
  children: SyntaxNode[]
  parent?: SyntaxNode
  depth: number
  properties: Record<string, any>
}

export interface HierarchyInfo {
  level: number
  path: string[]
  isTopLevel: boolean
  parentType?: string
  childrenCount: number
}

export interface DependencyInfo {
  type: 'import' | 'call' | 'reference' | 'inheritance' | 'composition'
  target: string
  range: vscode.Range
  isExternal: boolean
  strength: number
}

export interface ComplexityMetrics {
  cyclomatic: number
  cognitive: number
  halstead: HalsteadMetrics
  maintainability: number
  testability: number
}

export interface HalsteadMetrics {
  vocabulary: number
  length: number
  volume: number
  difficulty: number
  effort: number
  time: number
  bugs: number
}

export interface StructuralPattern {
  name: string
  type: 'design_pattern' | 'code_smell' | 'anti_pattern' | 'best_practice'
  confidence: number
  description: string
  impact: 'positive' | 'negative' | 'neutral'
}

export interface CohesionAnalysis {
  score: number
  type: 'functional' | 'sequential' | 'communicational' | 'procedural' | 'temporal' | 'logical' | 'coincidental'
  strength: 'high' | 'medium' | 'low'
  recommendations: string[]
}

export interface SemanticAnalysis {
  purpose: PurposeAnalysis
  functionality: FunctionalityAnalysis
  dataFlow: DataFlowAnalysis
  controlFlow: ControlFlowAnalysis
  sideEffects: SideEffectAnalysis
  performance: PerformanceAnalysis
}

export interface PurposeAnalysis {
  primaryPurpose: string
  secondaryPurposes: string[]
  confidence: number
  reasoning: string[]
  domain: string
}

export interface FunctionalityAnalysis {
  functions: FunctionInfo[]
  transformations: DataTransformation[]
  computations: ComputationInfo[]
  ioOperations: IOOperation[]
}

export interface FunctionInfo {
  name: string
  signature: string
  purpose: string
  sideEffects: string[]
  complexity: number
  purity: number
}

export interface DataTransformation {
  input: string
  output: string
  operation: string
  confidence: number
}

export interface ComputationInfo {
  type: 'arithmetic' | 'logical' | 'string' | 'collection' | 'comparison'
  complexity: number
  operations: string[]
}

export interface IOOperation {
  type: 'read' | 'write' | 'network' | 'file' | 'database' | 'ui'
  target: string
  isBlocking: boolean
  hasErrorHandling: boolean
}

export interface DataFlowAnalysis {
  inputs: DataFlowNode[]
  outputs: DataFlowNode[]
  transformations: DataFlowTransformation[]
  cycles: DataFlowCycle[]
  bottlenecks: DataFlowBottleneck[]
}

export interface DataFlowNode {
  variable: string
  type?: string
  range: vscode.Range
  isSource: boolean
  isSink: boolean
  isIntermediate: boolean
}

export interface DataFlowTransformation {
  from: string
  to: string
  operation: string
  range: vscode.Range
  complexity: number
}

export interface DataFlowCycle {
  variables: string[]
  length: number
  type: 'direct' | 'indirect'
  severity: number
}

export interface DataFlowBottleneck {
  variable: string
  usageCount: number
  complexity: number
  suggestion: string
}

export interface ControlFlowAnalysis {
  branches: BranchInfo[]
  loops: LoopInfo[]
  jumps: JumpInfo[]
  complexity: number
  reachability: ReachabilityInfo
}

export interface BranchInfo {
  type: 'if' | 'switch' | 'try' | 'conditional'
  condition: string
  range: vscode.Range
  pathCount: number
  coverage: number
}

export interface LoopInfo {
  type: 'for' | 'while' | 'do_while' | 'foreach'
  range: vscode.Range
  iterations: IterationInfo
  complexity: number
  hasBreak: boolean
  hasContinue: boolean
}

export interface IterationInfo {
  type: 'fixed' | 'variable' | 'infinite' | 'unknown'
  estimatedCount?: number
  variable?: string
  condition?: string
}

export interface JumpInfo {
  type: 'return' | 'break' | 'continue' | 'goto' | 'throw'
  range: vscode.Range
  target?: string
  isEarly: boolean
}

export interface ReachabilityInfo {
  reachableLines: number[]
  unreachableLines: number[]
  deadCode: vscode.Range[]
  coverage: number
}

export interface SideEffectAnalysis {
  hasSideEffects: boolean
  types: SideEffectType[]
  severity: 'none' | 'low' | 'medium' | 'high' | 'critical'
  globalVariables: string[]
  externalCalls: string[]
  fileOperations: string[]
  networkOperations: string[]
}

export interface SideEffectType {
  type: 'global_state' | 'file_system' | 'network' | 'console' | 'ui' | 'database' | 'external_service'
  operations: string[]
  impact: number
  reversible: boolean
}

export interface PerformanceAnalysis {
  timeComplexity: ComplexityEstimate
  spaceComplexity: ComplexityEstimate
  bottlenecks: PerformanceBottleneck[]
  optimizations: OptimizationSuggestion[]
  profileEstimate: ProfileEstimate
}

export interface ComplexityEstimate {
  bigO: string
  confidence: number
  factors: string[]
  explanation: string
}

export interface PerformanceBottleneck {
  type: 'loop' | 'recursion' | 'io' | 'memory' | 'computation'
  location: vscode.Range
  impact: number
  description: string
  suggestion: string
}

export interface OptimizationSuggestion {
  type: 'algorithmic' | 'data_structure' | 'caching' | 'parallelization' | 'lazy_loading'
  description: string
  estimatedGain: number
  difficulty: 'easy' | 'medium' | 'hard'
  tradeoffs: string[]
}

export interface ProfileEstimate {
  estimatedRuntime: number
  memoryUsage: number
  hotSpots: vscode.Range[]
  callFrequency: Record<string, number>
}

export interface IntentAnalysis {
  primaryIntent: UserIntent
  alternativeIntents: UserIntent[]
  confidence: number
  reasoning: string[]
  history: IntentHistory[]
  predictions: IntentPrediction[]
}

export interface UserIntent {
  action: 'copy' | 'move' | 'delete' | 'refactor' | 'analyze' | 'debug' | 'optimize' | 'document' | 'test' | 'extract'
  target: 'function' | 'class' | 'variable' | 'statement' | 'block' | 'expression' | 'comment' | 'string'
  purpose: string
  scope: 'local' | 'file' | 'project' | 'workspace'
  urgency: 'low' | 'medium' | 'high' | 'critical'
  complexity: number
}

export interface IntentHistory {
  intent: UserIntent
  timestamp: Date
  success: boolean
  duration: number
  context: string
}

export interface IntentPrediction {
  intent: UserIntent
  probability: number
  triggers: string[]
  requiredContext: string[]
}

export interface SelectionSuggestion {
  type: 'expansion' | 'contraction' | 'alternative' | 'related' | 'action'
  title: string
  description: string
  range?: vscode.Range
  action?: string
  priority: number
  confidence: number
  reasoning: string[]
  impact: 'low' | 'medium' | 'high'
}

export interface SelectionMetrics {
  efficiency: EfficiencyMetrics
  accuracy: AccuracyMetrics
  usability: UsabilityMetrics
  learning: LearningMetrics
}

export interface EfficiencyMetrics {
  selectionTime: number
  precisionRatio: number
  completenessRatio: number
  editDistance: number
  keystrokeEfficiency: number
}

export interface AccuracyMetrics {
  syntacticAccuracy: number
  semanticAccuracy: number
  intentAlignment: number
  boundaryAccuracy: number
  overallAccuracy: number
}

export interface UsabilityMetrics {
  easeOfSelection: number
  visualClarity: number
  feedbackQuality: number
  learnability: number
  accessibility: number
}

export interface LearningMetrics {
  patternRecognition: number
  adaptationRate: number
  improvementTrend: number
  userSatisfaction: number
  systemAccuracy: number
}

export interface RelationshipAnalysis {
  internalRelationships: Relationship[]
  externalRelationships: Relationship[]
  crossFileRelationships: Relationship[]
  dependencyDepth: number
  couplingStrength: number
  cohesionLevel: number
}

export interface Relationship {
  type: 'calls' | 'inherits' | 'implements' | 'uses' | 'creates' | 'modifies' | 'depends_on'
  target: string
  strength: number
  direction: 'incoming' | 'outgoing' | 'bidirectional'
  range: vscode.Range
  isExplicit: boolean
}

export interface QualityAnalysis {
  overallQuality: number
  maintainability: number
  readability: number
  testability: number
  reusability: number
  reliability: number
  security: SecurityAnalysis
  codeSmells: CodeSmell[]
}

export interface SecurityAnalysis {
  vulnerabilities: SecurityVulnerability[]
  risks: SecurityRisk[]
  recommendations: SecurityRecommendation[]
  overallRisk: 'low' | 'medium' | 'high' | 'critical'
}

export interface SecurityVulnerability {
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  range: vscode.Range
  cwe?: string
  fix?: string
}

export interface SecurityRisk {
  category: string
  probability: number
  impact: number
  mitigation: string[]
}

export interface SecurityRecommendation {
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  effort: 'trivial' | 'minor' | 'major' | 'significant'
}

export interface CodeSmell {
  name: string
  type: 'structural' | 'behavioral' | 'naming' | 'complexity' | 'duplication'
  severity: 'low' | 'medium' | 'high'
  description: string
  range: vscode.Range
  suggestion: string
  refactoringDifficulty: 'easy' | 'medium' | 'hard'
}

export interface OpportunityAnalysis {
  refactoringOpportunities: RefactoringOpportunity[]
  optimizationOpportunities: OptimizationOpportunity[]
  testingOpportunities: TestingOpportunity[]
  documentationOpportunities: DocumentationOpportunity[]
  learningOpportunities: LearningOpportunity[]
}

export interface RefactoringOpportunity {
  type: 'extract_method' | 'extract_class' | 'inline' | 'move' | 'rename' | 'simplify'
  description: string
  benefits: string[]
  effort: 'low' | 'medium' | 'high'
  risk: 'low' | 'medium' | 'high'
  automatable: boolean
}

export interface OptimizationOpportunity {
  type: 'performance' | 'memory' | 'readability' | 'maintainability'
  description: string
  estimatedGain: number
  implementation: string
  tradeoffs: string[]
}

export interface TestingOpportunity {
  type: 'unit_test' | 'integration_test' | 'e2e_test' | 'property_test'
  description: string
  coverage: number
  complexity: number
  priority: 'low' | 'medium' | 'high'
}

export interface DocumentationOpportunity {
  type: 'inline_comment' | 'function_doc' | 'class_doc' | 'api_doc' | 'example'
  description: string
  importance: number
  template?: string
}

export interface LearningOpportunity {
  concept: string
  resources: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  relevance: number
}

export class SelectionAnalyzer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private tsAnalyzer: TypeScriptAnalyzer
  private pythonAnalyzer: PythonAnalyzer
  private patternRecognizer: PatternRecognizer
  private contextAnalyzer: ContextAnalyzer
  private analysisCache: Map<string, SelectionAnalysis>

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    tsAnalyzer: TypeScriptAnalyzer,
    pythonAnalyzer: PythonAnalyzer,
    patternRecognizer: PatternRecognizer,
    contextAnalyzer: ContextAnalyzer
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.tsAnalyzer = tsAnalyzer
    this.pythonAnalyzer = pythonAnalyzer
    this.patternRecognizer = patternRecognizer
    this.contextAnalyzer = contextAnalyzer
    this.analysisCache = new Map()
  }

  async analyzeSelection(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<SelectionAnalysis> {
    const startTime = performance.now()
    const cacheKey = this.getCacheKey(document, selection)
    
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!
    }

    try {
      const selectionInfo = await this.analyzeSelectionInfo(document, selection)
      const content = await this.analyzeContent(document, selection)
      const structure = await this.analyzeStructure(document, selection)
      const semantics = await this.analyzeSemantics(document, selection, structure)
      const intent = await this.analyzeIntent(document, selection, content, structure)
      const relationships = await this.analyzeRelationships(document, selection)
      const quality = await this.analyzeQuality(document, selection, structure, semantics)
      const opportunities = await this.analyzeOpportunities(document, selection, quality)
      const suggestions = await this.generateSuggestions(document, selection, intent, quality)
      
      const metrics = this.calculateMetrics(
        selectionInfo, content, structure, semantics, intent,
        performance.now() - startTime
      )

      const analysis: SelectionAnalysis = {
        selection: selectionInfo,
        content,
        structure,
        semantics,
        intent,
        suggestions,
        metrics,
        relationships,
        quality,
        opportunities
      }

      this.analysisCache.set(cacheKey, analysis)
      return analysis
    } catch (error) {
      console.error('Error analyzing selection:', error)
      return this.createEmptyAnalysis(document, selection)
    }
  }

  private async analyzeSelectionInfo(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<SelectionInfo> {
    const text = document.getText(selection)
    const language = document.languageId
    const filePath = document.uri.fsPath
    
    const size = this.calculateSelectionSize(text, selection)
    const boundaries = await this.analyzeBoundaries(document, selection)
    const completeness = await this.analyzeCompleteness(document, selection, text, language)
    const context = await this.analyzeSelectionContext(document, selection)

    return {
      range: selection,
      text,
      language,
      filePath,
      size,
      boundaries,
      completeness,
      context
    }
  }

  private calculateSelectionSize(text: string, selection: vscode.Selection): SelectionSize {
    const lines = text.split('\n').length
    const tokens = this.countTokens(text)
    const bytes = Buffer.byteLength(text, 'utf8')
    const complexity = this.estimateComplexity(text)
    const depth = this.calculateNestingDepth(text)

    return {
      characters: text.length,
      lines,
      tokens,
      bytes,
      complexity,
      depth
    }
  }

  private countTokens(text: string): number {
    // Simple token counting - could be enhanced with language-specific tokenizers
    return text.split(/\s+|[{}();,.]/).filter(token => token.length > 0).length
  }

  private estimateComplexity(text: string): number {
    const conditions = (text.match(/if\s*\(|else|while\s*\(|for\s*\(/g) || []).length
    const functions = (text.match(/function\s+\w+|def\s+\w+|=>\s*/g) || []).length
    const classes = (text.match(/class\s+\w+/g) || []).length
    
    return (conditions * 1) + (functions * 2) + (classes * 3)
  }

  private calculateNestingDepth(text: string): number {
    let maxDepth = 0
    let currentDepth = 0
    
    for (const char of text) {
      if (char === '{' || char === '(' || char === '[') {
        currentDepth++
        maxDepth = Math.max(maxDepth, currentDepth)
      } else if (char === '}' || char === ')' || char === ']') {
        currentDepth--
      }
    }
    
    return maxDepth
  }

  private async analyzeBoundaries(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<SelectionBoundaries> {
    const startContext = this.analyzeBoundaryContext(document, selection.start)
    const endContext = this.analyzeBoundaryContext(document, selection.end)
    
    const type = this.determineBoundaryType(document, selection, startContext, endContext)
    const suggestedExpansion = await this.suggestBoundaryExpansion(document, selection, type)
    const confidence = this.calculateBoundaryConfidence(type, startContext, endContext)

    return {
      type,
      startContext,
      endContext,
      suggestedExpansion,
      confidence
    }
  }

  private analyzeBoundaryContext(document: vscode.TextDocument, position: vscode.Position): BoundaryContext {
    const line = document.lineAt(position.line)
    const char = position.character < line.text.length ? line.text[position.character] : ''
    
    return {
      tokenType: this.classifyCharacter(char),
      syntaxElement: this.identifySyntaxElement(document, position),
      indentLevel: this.getIndentLevel(line.text),
      isLineStart: position.character === 0,
      isLineEnd: position.character >= line.text.length,
      isBlockBoundary: /[{}]/.test(char)
    }
  }

  private classifyCharacter(char: string): string {
    if (/\w/.test(char)) return 'word'
    if (/\s/.test(char)) return 'whitespace'
    if (/[(){}[\]]/.test(char)) return 'bracket'
    if (/[.,;:]/.test(char)) return 'punctuation'
    if (/[+\-*/%=<>!&|]/.test(char)) return 'operator'
    return 'other'
  }

  private identifySyntaxElement(document: vscode.TextDocument, position: vscode.Position): string {
    // Would integrate with language services for precise syntax identification
    return 'unknown'
  }

  private getIndentLevel(lineText: string): number {
    const match = lineText.match(/^(\s*)/)
    return match ? match[1].length : 0
  }

  private determineBoundaryType(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    startContext: BoundaryContext,
    endContext: BoundaryContext
  ): SelectionBoundaries['type'] {
    if (this.isCompleteElement(document, selection)) return 'complete'
    if (this.crossesBoundaries(startContext, endContext)) return 'cross_boundary'
    if (this.isPartialElement(document, selection)) return 'partial'
    return 'invalid'
  }

  private isCompleteElement(document: vscode.TextDocument, selection: vscode.Selection): boolean {
    // Would implement AST-based complete element detection
    return true
  }

  private crossesBoundaries(startContext: BoundaryContext, endContext: BoundaryContext): boolean {
    return startContext.isBlockBoundary || endContext.isBlockBoundary
  }

  private isPartialElement(document: vscode.TextDocument, selection: vscode.Selection): boolean {
    // Would implement partial element detection
    return false
  }

  private async suggestBoundaryExpansion(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    type: SelectionBoundaries['type']
  ): Promise<vscode.Range | undefined> {
    if (type === 'complete') return undefined
    
    // Would implement smart boundary expansion
    return undefined
  }

  private calculateBoundaryConfidence(
    type: SelectionBoundaries['type'],
    startContext: BoundaryContext,
    endContext: BoundaryContext
  ): number {
    if (type === 'complete') return 1.0
    if (type === 'partial') return 0.7
    if (type === 'cross_boundary') return 0.4
    return 0.1
  }

  private async analyzeCompleteness(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    text: string,
    language: string
  ): Promise<CompletenessAnalysis> {
    const isSyntacticallyComplete = await this.checkSyntacticCompleteness(text, language)
    const isSemanticallyComplete = await this.checkSemanticCompleteness(document, selection)
    const isLogicallyComplete = await this.checkLogicalCompleteness(document, selection)
    const missingElements = await this.findMissingElements(document, selection, text)
    
    const completenessScore = (
      (isSyntacticallyComplete ? 1 : 0) +
      (isSemanticallyComplete ? 1 : 0) +
      (isLogicallyComplete ? 1 : 0)
    ) / 3

    return {
      isSyntacticallyComplete,
      isSemanticallyComplete,
      isLogicallyComplete,
      missingElements,
      confidence: completenessScore
    }
  }

  private async checkSyntacticCompleteness(text: string, language: string): Promise<boolean> {
    try {
      if (language === 'typescript' || language === 'javascript') {
        return await this.tsAnalyzer.validateSyntax(text)
      } else if (language === 'python') {
        return this.pythonAnalyzer.validateSyntax(text)
      }
      return true
    } catch (error) {
      return false
    }
  }

  private async checkSemanticCompleteness(document: vscode.TextDocument, selection: vscode.Selection): Promise<boolean> {
    // Would implement semantic completeness checking
    return true
  }

  private async checkLogicalCompleteness(document: vscode.TextDocument, selection: vscode.Selection): Promise<boolean> {
    // Would implement logical completeness checking
    return true
  }

  private async findMissingElements(document: vscode.TextDocument, selection: vscode.Selection, text: string): Promise<string[]> {
    const missingElements: string[] = []
    
    // Check for unmatched brackets
    const openBrackets = (text.match(/[({[]/g) || []).length
    const closeBrackets = (text.match(/[)}\]]/g) || []).length
    if (openBrackets !== closeBrackets) {
      missingElements.push('unmatched_brackets')
    }
    
    // Check for incomplete statements
    if (text.trim().endsWith(',') || text.trim().endsWith('+') || text.trim().endsWith('-')) {
      missingElements.push('incomplete_expression')
    }
    
    return missingElements
  }

  private async analyzeSelectionContext(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<SelectionContext> {
    const contextInfo = await this.contextAnalyzer.analyzeContext(document, selection.active, selection)
    
    return {
      parentElement: undefined, // Would extract from contextInfo
      siblingElements: [],
      childElements: [],
      scopeChain: [],
      nearbyCode: contextInfo.cursorContext.lineText
    }
  }

  private async analyzeContent(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<ContentAnalysis> {
    const text = document.getText(selection)
    const language = document.languageId
    
    const type = this.analyzeContentType(text, language)
    const categories = this.categorizeContent(text, language)
    const keywords = this.analyzeKeywords(text, language)
    const literals = this.analyzeLiterals(text, language)
    const identifiers = await this.analyzeIdentifiers(document, selection, text)
    const operators = this.analyzeOperators(text, language)
    const comments = this.analyzeComments(text, language)

    return {
      type,
      categories,
      keywords,
      literals,
      identifiers,
      operators,
      comments
    }
  }

  private analyzeContentType(text: string, language: string): ContentType {
    const codePercentage = this.calculateCodePercentage(text)
    const commentPercentage = this.calculateCommentPercentage(text)
    const stringPercentage = this.calculateStringPercentage(text)
    
    let primary: ContentType['primary'] = 'mixed'
    const reasoning: string[] = []
    
    if (codePercentage > 0.7) {
      primary = 'code'
      reasoning.push('Majority is executable code')
    } else if (commentPercentage > 0.5) {
      primary = 'comment'
      reasoning.push('Majority is comments or documentation')
    } else if (stringPercentage > 0.5) {
      primary = 'string'
      reasoning.push('Majority is string literals')
    }
    
    return {
      primary,
      secondary: ['code', 'comment', 'string'].filter(t => t !== primary),
      confidence: Math.max(codePercentage, commentPercentage, stringPercentage),
      reasoning
    }
  }

  private calculateCodePercentage(text: string): number {
    const codeLines = text.split('\n').filter(line => {
      const trimmed = line.trim()
      return trimmed.length > 0 && !trimmed.startsWith('//') && !trimmed.startsWith('#')
    }).length
    const totalLines = text.split('\n').length
    return totalLines > 0 ? codeLines / totalLines : 0
  }

  private calculateCommentPercentage(text: string): number {
    const commentLines = text.split('\n').filter(line => {
      const trimmed = line.trim()
      return trimmed.startsWith('//') || trimmed.startsWith('#') || trimmed.startsWith('/*')
    }).length
    const totalLines = text.split('\n').length
    return totalLines > 0 ? commentLines / totalLines : 0
  }

  private calculateStringPercentage(text: string): number {
    const stringMatches = text.match(/["'`][^"'`]*["'`]/g) || []
    const stringChars = stringMatches.join('').length
    return text.length > 0 ? stringChars / text.length : 0
  }

  private categorizeContent(text: string, language: string): ContentCategory[] {
    const categories: ContentCategory[] = []
    
    // Function definitions
    const functions = text.match(/function\s+\w+|def\s+\w+/g) || []
    if (functions.length > 0) {
      categories.push({
        category: 'function_definition',
        percentage: functions.length / text.split('\n').length,
        elements: functions,
        confidence: 0.9
      })
    }
    
    // Variable declarations
    const variables = text.match(/(const|let|var|=)\s+\w+/g) || []
    if (variables.length > 0) {
      categories.push({
        category: 'variable_declaration',
        percentage: variables.length / text.split('\n').length,
        elements: variables,
        confidence: 0.8
      })
    }
    
    return categories
  }

  private analyzeKeywords(text: string, language: string): KeywordAnalysis {
    const languageKeywords = this.getLanguageKeywords(language)
    const keywords: string[] = []
    const frequency: Record<string, number> = {}
    const context: Record<string, string[]> = {}
    
    for (const keyword of languageKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g')
      const matches = text.match(regex) || []
      if (matches.length > 0) {
        keywords.push(keyword)
        frequency[keyword] = matches.length
        context[keyword] = this.getKeywordContext(text, keyword)
      }
    }
    
    return {
      keywords,
      languageKeywords: keywords.filter(k => languageKeywords.includes(k)),
      customKeywords: [], // Would identify custom keywords
      frequency,
      context
    }
  }

  private getLanguageKeywords(language: string): string[] {
    const keywordMap: Record<string, string[]> = {
      'typescript': ['function', 'class', 'interface', 'type', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return'],
      'javascript': ['function', 'class', 'const', 'let', 'var', 'if', 'else', 'for', 'while', 'return'],
      'python': ['def', 'class', 'if', 'else', 'elif', 'for', 'while', 'return', 'import', 'from'],
      'java': ['class', 'public', 'private', 'static', 'void', 'if', 'else', 'for', 'while', 'return']
    }
    
    return keywordMap[language] || []
  }

  private getKeywordContext(text: string, keyword: string): string[] {
    const contexts: string[] = []
    const lines = text.split('\n')
    
    for (const line of lines) {
      if (line.includes(keyword)) {
        contexts.push(line.trim())
      }
    }
    
    return contexts
  }

  private analyzeLiterals(text: string, language: string): LiteralAnalysis {
    const strings = this.extractStringLiterals(text)
    const numbers = this.extractNumberLiterals(text)
    const booleans = this.extractBooleanLiterals(text)
    const nulls = this.extractNullLiterals(text, language)
    const patterns = this.detectLiteralPatterns(strings, numbers)
    
    return {
      strings,
      numbers,
      booleans,
      nulls,
      patterns
    }
  }

  private extractStringLiterals(text: string): StringLiteral[] {
    const literals: StringLiteral[] = []
    const stringRegex = /(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g
    let match
    
    while ((match = stringRegex.exec(text)) !== null) {
      const quote = match[1]
      const value = match[2]
      
      literals.push({
        value,
        type: quote === '`' ? 'template' : quote === '"' ? 'double' : 'single',
        range: new vscode.Range(0, match.index, 0, match.index + match[0].length),
        isMultiline: value.includes('\n'),
        containsInterpolation: quote === '`' && value.includes('${')
      })
    }
    
    return literals
  }

  private extractNumberLiterals(text: string): NumberLiteral[] {
    const literals: NumberLiteral[] = []
    const numberRegex = /\b(\d+(?:\.\d+)?(?:[eE][+-]?\d+)?|0x[0-9a-fA-F]+|0b[01]+|0o[0-7]+)\b/g
    let match
    
    while ((match = numberRegex.exec(text)) !== null) {
      const value = match[1]
      let type: NumberLiteral['type'] = 'integer'
      
      if (value.includes('.') || value.includes('e') || value.includes('E')) {
        type = 'float'
      } else if (value.startsWith('0x')) {
        type = 'hex'
      } else if (value.startsWith('0b')) {
        type = 'binary'
      } else if (value.startsWith('0o')) {
        type = 'octal'
      }
      
      literals.push({
        value: value.startsWith('0x') || value.startsWith('0b') || value.startsWith('0o') ? value : parseFloat(value),
        type,
        range: new vscode.Range(0, match.index, 0, match.index + match[0].length),
        isConstant: /[A-Z_]+/.test(value) // Simple heuristic
      })
    }
    
    return literals
  }

  private extractBooleanLiterals(text: string): BooleanLiteral[] {
    const literals: BooleanLiteral[] = []
    const booleanRegex = /\b(true|false|True|False)\b/g
    let match
    
    while ((match = booleanRegex.exec(text)) !== null) {
      literals.push({
        value: match[1].toLowerCase() === 'true',
        range: new vscode.Range(0, match.index, 0, match.index + match[0].length),
        context: 'boolean_literal'
      })
    }
    
    return literals
  }

  private extractNullLiterals(text: string, language: string): NullLiteral[] {
    const literals: NullLiteral[] = []
    const nullKeywords = language === 'python' ? ['None'] : ['null', 'undefined']
    
    for (const keyword of nullKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g')
      let match
      
      while ((match = regex.exec(text)) !== null) {
        literals.push({
          type: keyword as NullLiteral['type'],
          range: new vscode.Range(0, match.index, 0, match.index + match[0].length),
          context: 'null_literal'
        })
      }
    }
    
    return literals
  }

  private detectLiteralPatterns(strings: StringLiteral[], numbers: NumberLiteral[]): LiteralPattern[] {
    const patterns: LiteralPattern[] = []
    
    // Detect magic numbers
    const numberValues = numbers.map(n => n.value.toString())
    const magicNumbers = numberValues.filter((value, index, array) => 
      array.indexOf(value) !== index && !['0', '1', '-1'].includes(value)
    )
    
    if (magicNumbers.length > 0) {
      patterns.push({
        pattern: 'magic_numbers',
        occurrences: magicNumbers.length,
        type: 'magic_number',
        severity: 'medium'
      })
    }
    
    // Detect hardcoded strings
    const stringValues = strings.map(s => s.value)
    const hardcodedStrings = stringValues.filter((value, index, array) => 
      array.indexOf(value) !== index && value.length > 3
    )
    
    if (hardcodedStrings.length > 0) {
      patterns.push({
        pattern: 'hardcoded_strings',
        occurrences: hardcodedStrings.length,
        type: 'hardcoded_string',
        severity: 'low'
      })
    }
    
    return patterns
  }

  private async analyzeIdentifiers(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    text: string
  ): Promise<IdentifierAnalysis> {
    const identifierRegex = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g
    const identifiers: string[] = []
    let match
    
    while ((match = identifierRegex.exec(text)) !== null) {
      identifiers.push(match[0])
    }
    
    const variables: IdentifierInfo[] = []
    const functions: IdentifierInfo[] = []
    const classes: IdentifierInfo[] = []
    const modules: IdentifierInfo[] = []
    
    // Would implement detailed identifier analysis using language services
    
    const namingConventions = this.analyzeNamingConventions(identifiers)
    const undefinedReferences: string[] = [] // Would check for undefined references
    
    return {
      variables,
      functions,
      classes,
      modules,
      namingConventions,
      undefinedReferences
    }
  }

  private analyzeNamingConventions(identifiers: string[]): NamingConventionAnalysis {
    let camelCase = 0
    let snakeCase = 0
    let pascalCase = 0
    let kebabCase = 0
    let constantCase = 0
    
    for (const identifier of identifiers) {
      if (/^[a-z][a-zA-Z0-9]*$/.test(identifier)) camelCase++
      else if (/^[a-z][a-z0-9_]*$/.test(identifier)) snakeCase++
      else if (/^[A-Z][a-zA-Z0-9]*$/.test(identifier)) pascalCase++
      else if (/^[a-z][a-z0-9-]*$/.test(identifier)) kebabCase++
      else if (/^[A-Z][A-Z0-9_]*$/.test(identifier)) constantCase++
    }
    
    const total = identifiers.length
    const consistency = total > 0 ? Math.max(camelCase, snakeCase, pascalCase, kebabCase, constantCase) / total : 1
    
    return {
      camelCase: camelCase / total,
      snakeCase: snakeCase / total,
      pascalCase: pascalCase / total,
      kebabCase: kebabCase / total,
      constantCase: constantCase / total,
      consistency,
      violations: [] // Would implement violation detection
    }
  }

  private analyzeOperators(text: string, language: string): OperatorAnalysis {
    const operators = {
      arithmetic: ['+', '-', '*', '/', '%', '**'],
      logical: ['&&', '||', '!', 'and', 'or', 'not'],
      comparison: ['==', '!=', '<', '>', '<=', '>=', '===', '!=='],
      assignment: ['=', '+=', '-=', '*=', '/=', '%='],
      bitwise: ['&', '|', '^', '~', '<<', '>>', '>>>']
    }
    
    const analysis: OperatorAnalysis = {
      arithmetic: [],
      logical: [],
      comparison: [],
      assignment: [],
      bitwise: [],
      complexity: 0,
      patterns: []
    }
    
    let totalOperators = 0
    
    for (const [category, ops] of Object.entries(operators)) {
      for (const op of ops) {
        const regex = new RegExp(`\\${op.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g')
        const matches = text.match(regex) || []
        if (matches.length > 0) {
          (analysis as any)[category].push({
            operator: op,
            count: matches.length,
            positions: [], // Would calculate actual positions
            context: []
          })
          totalOperators += matches.length
        }
      }
    }
    
    analysis.complexity = totalOperators
    return analysis
  }

  private analyzeComments(text: string, language: string): CommentAnalysis {
    const totalComments = this.countComments(text, language)
    const types = this.categorizeComments(text, language)
    const coverage = this.calculateCommentCoverage(text)
    const quality = this.assessCommentQuality(text)
    const todos = this.extractTodos(text)
    const documentation = this.extractDocumentation(text, language)
    
    return {
      totalComments,
      types,
      coverage,
      quality,
      todos,
      documentation
    }
  }

  private countComments(text: string, language: string): number {
    const singleLineRegex = language === 'python' ? /#.*$/gm : /\/\/.*$/gm
    const multiLineRegex = /\/\*[\s\S]*?\*\//g
    
    const singleLineComments = text.match(singleLineRegex) || []
    const multiLineComments = text.match(multiLineRegex) || []
    
    return singleLineComments.length + multiLineComments.length
  }

  private categorizeComments(text: string, language: string): CommentType[] {
    // Would implement detailed comment categorization
    return []
  }

  private calculateCommentCoverage(text: string): number {
    const totalLines = text.split('\n').length
    const commentLines = text.split('\n').filter(line => 
      line.trim().startsWith('//') || line.trim().startsWith('#')
    ).length
    
    return totalLines > 0 ? commentLines / totalLines : 0
  }

  private assessCommentQuality(text: string): number {
    // Would implement comment quality assessment
    return 0.5
  }

  private extractTodos(text: string): TodoItem[] {
    const todos: TodoItem[] = []
    const todoRegex = /(TODO|FIXME|HACK|NOTE|BUG)[:]\s*(.+)/gi
    let match
    
    while ((match = todoRegex.exec(text)) !== null) {
      todos.push({
        type: match[1].toUpperCase() as TodoItem['type'],
        text: match[2],
        range: new vscode.Range(0, match.index, 0, match.index + match[0].length),
        priority: this.classifyTodoPriority(match[1]),
        assignee: this.extractAssignee(match[2])
      })
    }
    
    return todos
  }

  private classifyTodoPriority(type: string): TodoItem['priority'] {
    switch (type.toUpperCase()) {
      case 'BUG':
      case 'FIXME':
        return 'high'
      case 'TODO':
        return 'medium'
      case 'HACK':
        return 'medium'
      case 'NOTE':
        return 'low'
      default:
        return 'low'
    }
  }

  private extractAssignee(text: string): string | undefined {
    const assigneeMatch = text.match(/@(\w+)/)
    return assigneeMatch ? assigneeMatch[1] : undefined
  }

  private extractDocumentation(text: string, language: string): DocumentationInfo[] {
    // Would implement documentation extraction
    return []
  }

  private async analyzeStructure(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<StructuralAnalysis> {
    const text = document.getText(selection)
    const language = document.languageId
    
    const syntaxTree = await this.buildSyntaxTree(text, language)
    const hierarchy = this.analyzeHierarchy(syntaxTree)
    const dependencies = await this.analyzeDependencies(document, selection)
    const complexity = this.calculateComplexityMetrics(text, syntaxTree)
    const patterns = await this.detectStructuralPatterns(document, selection)
    const cohesion = this.analyzeCohesion(syntaxTree, text)
    
    return {
      syntaxTree,
      hierarchy,
      dependencies,
      complexity,
      patterns,
      cohesion
    }
  }

  private async buildSyntaxTree(text: string, language: string): Promise<SyntaxNode> {
    // Would integrate with language-specific parsers
    return {
      type: 'root',
      range: new vscode.Range(0, 0, 0, text.length),
      children: [],
      depth: 0,
      properties: {}
    }
  }

  private analyzeHierarchy(syntaxTree: SyntaxNode): HierarchyInfo {
    return {
      level: syntaxTree.depth,
      path: ['root'],
      isTopLevel: syntaxTree.depth === 0,
      childrenCount: syntaxTree.children.length
    }
  }

  private async analyzeDependencies(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<DependencyInfo[]> {
    // Would implement dependency analysis
    return []
  }

  private calculateComplexityMetrics(text: string, syntaxTree: SyntaxNode): ComplexityMetrics {
    const cyclomatic = this.calculateCyclomaticComplexity(text)
    const cognitive = this.calculateCognitiveComplexity(text)
    const halstead = this.calculateHalsteadMetrics(text)
    const maintainability = this.calculateMaintainabilityIndex(cyclomatic, halstead)
    const testability = this.calculateTestability(cyclomatic, cognitive)
    
    return {
      cyclomatic,
      cognitive,
      halstead,
      maintainability,
      testability
    }
  }

  private calculateCyclomaticComplexity(text: string): number {
    const conditions = (text.match(/if\s*\(|else\s*if|while\s*\(|for\s*\(|catch\s*\(|case\s+/g) || []).length
    return conditions + 1
  }

  private calculateCognitiveComplexity(text: string): number {
    // Simplified cognitive complexity calculation
    const nestedConditions = (text.match(/\s+if\s*\(|\s+while\s*\(|\s+for\s*\(/g) || []).length
    const logicalOperators = (text.match(/&&|\|\||and\s|or\s/g) || []).length
    return nestedConditions * 2 + logicalOperators
  }

  private calculateHalsteadMetrics(text: string): HalsteadMetrics {
    const operators = (text.match(/[+\-*/=<>!&|]+/g) || []).length
    const operands = (text.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b|\b\d+\b/g) || []).length
    
    const vocabulary = operators + operands
    const length = vocabulary
    const volume = length * Math.log2(vocabulary || 1)
    const difficulty = (operators / 2) * (operands / (operands || 1))
    const effort = difficulty * volume
    const time = effort / 18 // seconds
    const bugs = volume / 3000
    
    return {
      vocabulary,
      length,
      volume,
      difficulty,
      effort,
      time,
      bugs
    }
  }

  private calculateMaintainabilityIndex(cyclomatic: number, halstead: HalsteadMetrics): number {
    // Simplified maintainability index
    return Math.max(0, 171 - 5.2 * Math.log(halstead.volume) - 0.23 * cyclomatic - 16.2 * Math.log(10))
  }

  private calculateTestability(cyclomatic: number, cognitive: number): number {
    // Simple testability score (inverse of complexity)
    return Math.max(0, 100 - (cyclomatic * 2 + cognitive))
  }

  private async detectStructuralPatterns(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<StructuralPattern[]> {
    // Would integrate with pattern recognizer
    return []
  }

  private analyzeCohesion(syntaxTree: SyntaxNode, text: string): CohesionAnalysis {
    // Would implement cohesion analysis
    return {
      score: 0.7,
      type: 'functional',
      strength: 'medium',
      recommendations: []
    }
  }

  private async analyzeSemantics(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    structure: StructuralAnalysis
  ): Promise<SemanticAnalysis> {
    const text = document.getText(selection)
    
    const purpose = await this.analyzePurpose(text, structure)
    const functionality = await this.analyzeFunctionality(text, structure)
    const dataFlow = await this.analyzeDataFlow(text, structure)
    const controlFlow = await this.analyzeControlFlow(text, structure)
    const sideEffects = await this.analyzeSideEffects(text, structure)
    const performance = await this.analyzePerformance(text, structure)
    
    return {
      purpose,
      functionality,
      dataFlow,
      controlFlow,
      sideEffects,
      performance
    }
  }

  private async analyzePurpose(text: string, structure: StructuralAnalysis): Promise<PurposeAnalysis> {
    // Would implement purpose analysis using NLP and code analysis
    return {
      primaryPurpose: 'data_processing',
      secondaryPurposes: ['validation', 'transformation'],
      confidence: 0.7,
      reasoning: ['Contains data transformation patterns'],
      domain: 'general'
    }
  }

  private async analyzeFunctionality(text: string, structure: StructuralAnalysis): Promise<FunctionalityAnalysis> {
    // Would implement functionality analysis
    return {
      functions: [],
      transformations: [],
      computations: [],
      ioOperations: []
    }
  }

  private async analyzeDataFlow(text: string, structure: StructuralAnalysis): Promise<DataFlowAnalysis> {
    // Would implement data flow analysis
    return {
      inputs: [],
      outputs: [],
      transformations: [],
      cycles: [],
      bottlenecks: []
    }
  }

  private async analyzeControlFlow(text: string, structure: StructuralAnalysis): Promise<ControlFlowAnalysis> {
    // Would implement control flow analysis
    return {
      branches: [],
      loops: [],
      jumps: [],
      complexity: structure.complexity.cyclomatic,
      reachability: {
        reachableLines: [],
        unreachableLines: [],
        deadCode: [],
        coverage: 1.0
      }
    }
  }

  private async analyzeSideEffects(text: string, structure: StructuralAnalysis): Promise<SideEffectAnalysis> {
    const hasSideEffects = this.detectSideEffects(text)
    
    return {
      hasSideEffects,
      types: [],
      severity: hasSideEffects ? 'medium' : 'none',
      globalVariables: [],
      externalCalls: [],
      fileOperations: [],
      networkOperations: []
    }
  }

  private detectSideEffects(text: string): boolean {
    const sideEffectPatterns = [
      /console\./,
      /document\./,
      /window\./,
      /localStorage/,
      /sessionStorage/,
      /fetch\(/,
      /XMLHttpRequest/,
      /\.push\(/,
      /\.pop\(/,
      /delete\s+/
    ]
    
    return sideEffectPatterns.some(pattern => pattern.test(text))
  }

  private async analyzePerformance(text: string, structure: StructuralAnalysis): Promise<PerformanceAnalysis> {
    const timeComplexity = this.estimateTimeComplexity(text)
    const spaceComplexity = this.estimateSpaceComplexity(text)
    const bottlenecks = this.identifyBottlenecks(text)
    const optimizations = this.suggestOptimizations(text, bottlenecks)
    const profileEstimate = this.estimateProfile(text)
    
    return {
      timeComplexity,
      spaceComplexity,
      bottlenecks,
      optimizations,
      profileEstimate
    }
  }

  private estimateTimeComplexity(text: string): ComplexityEstimate {
    const nestedLoops = (text.match(/for\s*\([^)]*\)\s*{[^}]*for\s*\(/g) || []).length
    const singleLoops = (text.match(/for\s*\(|while\s*\(/g) || []).length - nestedLoops * 2
    
    let bigO = 'O(1)'
    const factors: string[] = []
    
    if (nestedLoops > 0) {
      bigO = 'O(n²)'
      factors.push('nested loops')
    } else if (singleLoops > 0) {
      bigO = 'O(n)'
      factors.push('linear loops')
    }
    
    return {
      bigO,
      confidence: factors.length > 0 ? 0.8 : 0.5,
      factors,
      explanation: `Estimated based on ${factors.join(', ') || 'constant time operations'}`
    }
  }

  private estimateSpaceComplexity(text: string): ComplexityEstimate {
    const arrays = (text.match(/new\s+Array\(|new\s+\w+\[|\[\]/g) || []).length
    const objects = (text.match(/new\s+\w+\(|\{\}/g) || []).length
    
    let bigO = 'O(1)'
    const factors: string[] = []
    
    if (arrays > 0 || objects > 0) {
      bigO = 'O(n)'
      factors.push('data structures')
    }
    
    return {
      bigO,
      confidence: 0.6,
      factors,
      explanation: `Estimated based on ${factors.join(', ') || 'no significant data structures'}`
    }
  }

  private identifyBottlenecks(text: string): PerformanceBottleneck[] {
    const bottlenecks: PerformanceBottleneck[] = []
    
    // Nested loops
    const nestedLoopRegex = /for\s*\([^)]*\)\s*{[^}]*for\s*\(/g
    let match
    while ((match = nestedLoopRegex.exec(text)) !== null) {
      bottlenecks.push({
        type: 'loop',
        location: new vscode.Range(0, match.index, 0, match.index + match[0].length),
        impact: 0.8,
        description: 'Nested loop detected',
        suggestion: 'Consider optimizing the nested loop structure'
      })
    }
    
    return bottlenecks
  }

  private suggestOptimizations(text: string, bottlenecks: PerformanceBottleneck[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = []
    
    for (const bottleneck of bottlenecks) {
      if (bottleneck.type === 'loop') {
        suggestions.push({
          type: 'algorithmic',
          description: 'Optimize nested loop using hash map lookup',
          estimatedGain: 0.5,
          difficulty: 'medium',
          tradeoffs: ['Increased memory usage', 'Better time complexity']
        })
      }
    }
    
    return suggestions
  }

  private estimateProfile(text: string): ProfileEstimate {
    const lines = text.split('\n').length
    const complexity = this.calculateCyclomaticComplexity(text)
    
    return {
      estimatedRuntime: lines * 0.1, // Very rough estimate
      memoryUsage: lines * 10, // bytes
      hotSpots: [],
      callFrequency: {}
    }
  }

  private async analyzeIntent(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): Promise<IntentAnalysis> {
    const primaryIntent = await this.detectPrimaryIntent(document, selection, content, structure)
    const alternativeIntents = await this.detectAlternativeIntents(primaryIntent, content, structure)
    const confidence = this.calculateIntentConfidence(primaryIntent, content, structure)
    const reasoning = this.generateIntentReasoning(primaryIntent, content, structure)
    const history: IntentHistory[] = [] // Would track intent history
    const predictions = await this.predictNextIntents(primaryIntent, content, structure)
    
    return {
      primaryIntent,
      alternativeIntents,
      confidence,
      reasoning,
      history,
      predictions
    }
  }

  private async detectPrimaryIntent(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): Promise<UserIntent> {
    // Analyze selection characteristics to determine intent
    const hasCompleteFunction = structure.syntaxTree.children.some(child => child.type === 'function')
    const hasComment = content.comments.totalComments > 0
    const hasError = content.identifiers.undefinedReferences.length > 0
    
    let action: UserIntent['action'] = 'analyze'
    let target: UserIntent['target'] = 'statement'
    let purpose = 'Understanding selected code'
    
    if (hasCompleteFunction) {
      action = 'refactor'
      target = 'function'
      purpose = 'Refactoring function'
    } else if (hasComment) {
      action = 'document'
      target = 'comment'
      purpose = 'Working with documentation'
    } else if (hasError) {
      action = 'debug'
      target = 'statement'
      purpose = 'Debugging code issues'
    }
    
    return {
      action,
      target,
      purpose,
      scope: 'local',
      urgency: 'medium',
      complexity: structure.complexity.cyclomatic
    }
  }

  private async detectAlternativeIntents(
    primaryIntent: UserIntent,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): Promise<UserIntent[]> {
    const alternatives: UserIntent[] = []
    
    // Generate alternative intents based on context
    if (primaryIntent.action !== 'copy') {
      alternatives.push({
        action: 'copy',
        target: primaryIntent.target,
        purpose: 'Copy for reuse elsewhere',
        scope: 'project',
        urgency: 'low',
        complexity: 1
      })
    }
    
    if (primaryIntent.action !== 'extract') {
      alternatives.push({
        action: 'extract',
        target: 'function',
        purpose: 'Extract into separate function',
        scope: 'file',
        urgency: 'medium',
        complexity: 3
      })
    }
    
    return alternatives
  }

  private calculateIntentConfidence(
    intent: UserIntent,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): number {
    let confidence = 0.5
    
    // Increase confidence based on clear indicators
    if (intent.action === 'refactor' && structure.hierarchy.isTopLevel) {
      confidence += 0.3
    }
    
    if (intent.action === 'debug' && content.identifiers.undefinedReferences.length > 0) {
      confidence += 0.4
    }
    
    return Math.min(confidence, 1.0)
  }

  private generateIntentReasoning(
    intent: UserIntent,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): string[] {
    const reasoning: string[] = []
    
    reasoning.push(`Detected ${intent.action} intent for ${intent.target}`)
    
    if (structure.complexity.cyclomatic > 5) {
      reasoning.push('High complexity suggests refactoring opportunity')
    }
    
    if (content.comments.coverage < 0.1) {
      reasoning.push('Low comment coverage suggests documentation need')
    }
    
    return reasoning
  }

  private async predictNextIntents(
    currentIntent: UserIntent,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): Promise<IntentPrediction[]> {
    const predictions: IntentPrediction[] = []
    
    // Predict likely next actions based on current intent
    if (currentIntent.action === 'analyze') {
      predictions.push({
        intent: {
          action: 'refactor',
          target: currentIntent.target,
          purpose: 'Refactor after analysis',
          scope: 'file',
          urgency: 'medium',
          complexity: 3
        },
        probability: 0.7,
        triggers: ['complexity_high', 'code_smell_detected'],
        requiredContext: ['full_function_selected']
      })
    }
    
    return predictions
  }

  private async analyzeRelationships(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<RelationshipAnalysis> {
    // Would implement relationship analysis
    return {
      internalRelationships: [],
      externalRelationships: [],
      crossFileRelationships: [],
      dependencyDepth: 0,
      couplingStrength: 0,
      cohesionLevel: 0
    }
  }

  private async analyzeQuality(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    structure: StructuralAnalysis,
    semantics: SemanticAnalysis
  ): Promise<QualityAnalysis> {
    const maintainability = structure.complexity.maintainability / 100
    const readability = this.assessReadability(document.getText(selection))
    const testability = structure.complexity.testability / 100
    const reusability = this.assessReusability(structure, semantics)
    const reliability = this.assessReliability(semantics)
    const security = await this.analyzeSecurityIssues(document.getText(selection))
    const codeSmells = await this.detectCodeSmells(document, selection, structure)
    
    const overallQuality = (maintainability + readability + testability + reusability + reliability) / 5
    
    return {
      overallQuality,
      maintainability,
      readability,
      testability,
      reusability,
      reliability,
      security,
      codeSmells
    }
  }

  private assessReadability(text: string): number {
    const avgLineLength = text.split('\n').reduce((sum, line) => sum + line.length, 0) / text.split('\n').length
    const complexWords = (text.match(/\b\w{8,}\b/g) || []).length
    const totalWords = (text.match(/\b\w+\b/g) || []).length
    
    let score = 1.0
    
    // Penalize very long lines
    if (avgLineLength > 120) score -= 0.2
    
    // Penalize too many complex words
    if (totalWords > 0 && complexWords / totalWords > 0.3) score -= 0.2
    
    return Math.max(0, score)
  }

  private assessReusability(structure: StructuralAnalysis, semantics: SemanticAnalysis): number {
    let score = 0.7
    
    // Higher reusability for pure functions
    if (!semantics.sideEffects.hasSideEffects) score += 0.2
    
    // Lower reusability for high coupling
    if (structure.dependencies.length > 5) score -= 0.2
    
    return Math.max(0, Math.min(1, score))
  }

  private assessReliability(semantics: SemanticAnalysis): number {
    let score = 0.8
    
    // Lower reliability for side effects
    if (semantics.sideEffects.severity === 'high') score -= 0.3
    else if (semantics.sideEffects.severity === 'medium') score -= 0.1
    
    // Lower reliability for complex control flow
    if (semantics.controlFlow.complexity > 10) score -= 0.2
    
    return Math.max(0, score)
  }

  private async analyzeSecurityIssues(text: string): Promise<SecurityAnalysis> {
    const vulnerabilities: SecurityVulnerability[] = []
    const risks: SecurityRisk[] = []
    const recommendations: SecurityRecommendation[] = []
    
    // Check for common security issues
    if (text.includes('eval(')) {
      vulnerabilities.push({
        type: 'Code Injection',
        severity: 'high',
        description: 'Use of eval() can lead to code injection',
        range: new vscode.Range(0, 0, 0, 0),
        cwe: 'CWE-94',
        fix: 'Avoid using eval(), use safer alternatives'
      })
    }
    
    if (text.includes('innerHTML')) {
      vulnerabilities.push({
        type: 'XSS',
        severity: 'medium',
        description: 'innerHTML usage can lead to XSS',
        range: new vscode.Range(0, 0, 0, 0),
        fix: 'Use textContent or proper sanitization'
      })
    }
    
    const overallRisk = vulnerabilities.length > 0 ? 
      vulnerabilities.some(v => v.severity === 'high') ? 'high' : 'medium' : 'low'
    
    return {
      vulnerabilities,
      risks,
      recommendations,
      overallRisk
    }
  }

  private async detectCodeSmells(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    structure: StructuralAnalysis
  ): Promise<CodeSmell[]> {
    const smells: CodeSmell[] = []
    const text = document.getText(selection)
    
    // Long method
    if (text.split('\n').length > 50) {
      smells.push({
        name: 'Long Method',
        type: 'structural',
        severity: 'medium',
        description: 'Method is too long and should be broken down',
        range: selection,
        suggestion: 'Extract smaller methods',
        refactoringDifficulty: 'medium'
      })
    }
    
    // High complexity
    if (structure.complexity.cyclomatic > 10) {
      smells.push({
        name: 'Complex Method',
        type: 'complexity',
        severity: 'high',
        description: 'Method has high cyclomatic complexity',
        range: selection,
        suggestion: 'Simplify control flow',
        refactoringDifficulty: 'hard'
      })
    }
    
    return smells
  }

  private async analyzeOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    quality: QualityAnalysis
  ): Promise<OpportunityAnalysis> {
    const refactoringOpportunities = await this.identifyRefactoringOpportunities(document, selection, quality)
    const optimizationOpportunities = await this.identifyOptimizationOpportunities(document, selection)
    const testingOpportunities = await this.identifyTestingOpportunities(document, selection)
    const documentationOpportunities = await this.identifyDocumentationOpportunities(document, selection)
    const learningOpportunities = await this.identifyLearningOpportunities(document, selection)
    
    return {
      refactoringOpportunities,
      optimizationOpportunities,
      testingOpportunities,
      documentationOpportunities,
      learningOpportunities
    }
  }

  private async identifyRefactoringOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    quality: QualityAnalysis
  ): Promise<RefactoringOpportunity[]> {
    const opportunities: RefactoringOpportunity[] = []
    
    if (quality.codeSmells.some(smell => smell.name === 'Long Method')) {
      opportunities.push({
        type: 'extract_method',
        description: 'Extract parts of this long method into smaller methods',
        benefits: ['Improved readability', 'Better testability', 'Increased reusability'],
        effort: 'medium',
        risk: 'low',
        automatable: true
      })
    }
    
    return opportunities
  }

  private async identifyOptimizationOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<OptimizationOpportunity[]> {
    // Would implement optimization opportunity detection
    return []
  }

  private async identifyTestingOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<TestingOpportunity[]> {
    // Would implement testing opportunity detection
    return []
  }

  private async identifyDocumentationOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<DocumentationOpportunity[]> {
    // Would implement documentation opportunity detection
    return []
  }

  private async identifyLearningOpportunities(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): Promise<LearningOpportunity[]> {
    // Would implement learning opportunity detection
    return []
  }

  private async generateSuggestions(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    intent: IntentAnalysis,
    quality: QualityAnalysis
  ): Promise<SelectionSuggestion[]> {
    const suggestions: SelectionSuggestion[] = []
    
    // Intent-based suggestions
    if (intent.primaryIntent.action === 'refactor') {
      suggestions.push({
        type: 'action',
        title: 'Extract Method',
        description: 'Extract selected code into a new method',
        action: 'extract_method',
        priority: 8,
        confidence: 0.8,
        reasoning: ['High complexity detected', 'Code appears to be a logical unit'],
        impact: 'medium'
      })
    }
    
    // Quality-based suggestions
    if (quality.codeSmells.length > 0) {
      suggestions.push({
        type: 'action',
        title: 'Address Code Smells',
        description: `Fix ${quality.codeSmells.length} code smell(s)`,
        action: 'fix_code_smells',
        priority: 6,
        confidence: 0.7,
        reasoning: ['Code smells detected', 'Quality improvement needed'],
        impact: 'high'
      })
    }
    
    // Boundary suggestions
    if (selection.isEmpty) {
      suggestions.push({
        type: 'expansion',
        title: 'Select Word',
        description: 'Expand selection to current word',
        priority: 9,
        confidence: 0.9,
        reasoning: ['No text selected', 'Cursor on word'],
        impact: 'low'
      })
    }
    
    return suggestions.sort((a, b) => b.priority - a.priority)
  }

  private calculateMetrics(
    selectionInfo: SelectionInfo,
    content: ContentAnalysis,
    structure: StructuralAnalysis,
    semantics: SemanticAnalysis,
    intent: IntentAnalysis,
    processingTime: number
  ): SelectionMetrics {
    const efficiency: EfficiencyMetrics = {
      selectionTime: processingTime,
      precisionRatio: selectionInfo.boundaries.confidence,
      completenessRatio: selectionInfo.completeness.confidence,
      editDistance: 0, // Would calculate edit distance
      keystrokeEfficiency: selectionInfo.size.characters / Math.max(1, processingTime / 1000)
    }
    
    const accuracy: AccuracyMetrics = {
      syntacticAccuracy: selectionInfo.completeness.isSyntacticallyComplete ? 1.0 : 0.5,
      semanticAccuracy: selectionInfo.completeness.isSemanticallyComplete ? 1.0 : 0.5,
      intentAlignment: intent.confidence,
      boundaryAccuracy: selectionInfo.boundaries.confidence,
      overallAccuracy: 0.8 // Would calculate actual accuracy
    }
    
    const usability: UsabilityMetrics = {
      easeOfSelection: 0.8,
      visualClarity: 0.9,
      feedbackQuality: 0.7,
      learnability: 0.8,
      accessibility: 0.9
    }
    
    const learning: LearningMetrics = {
      patternRecognition: 0.7,
      adaptationRate: 0.6,
      improvementTrend: 0.8,
      userSatisfaction: 0.8,
      systemAccuracy: accuracy.overallAccuracy
    }
    
    return {
      efficiency,
      accuracy,
      usability,
      learning
    }
  }

  private createEmptyAnalysis(
    document: vscode.TextDocument,
    selection: vscode.Selection
  ): SelectionAnalysis {
    // Create a minimal analysis for error cases
    return {
      selection: {
        range: selection,
        text: document.getText(selection),
        language: document.languageId,
        filePath: document.uri.fsPath,
        size: {
          characters: 0,
          lines: 0,
          tokens: 0,
          bytes: 0,
          complexity: 0,
          depth: 0
        },
        boundaries: {
          type: 'invalid',
          startContext: {
            tokenType: 'unknown',
            syntaxElement: 'unknown',
            indentLevel: 0,
            isLineStart: false,
            isLineEnd: false,
            isBlockBoundary: false
          },
          endContext: {
            tokenType: 'unknown',
            syntaxElement: 'unknown',
            indentLevel: 0,
            isLineStart: false,
            isLineEnd: false,
            isBlockBoundary: false
          },
          confidence: 0
        },
        completeness: {
          isSyntacticallyComplete: false,
          isSemanticallyComplete: false,
          isLogicallyComplete: false,
          missingElements: [],
          confidence: 0
        },
        context: {
          siblingElements: [],
          childElements: [],
          scopeChain: [],
          nearbyCode: ''
        }
      },
      content: {
        type: {
          primary: 'mixed',
          secondary: [],
          confidence: 0,
          reasoning: []
        },
        categories: [],
        keywords: {
          keywords: [],
          languageKeywords: [],
          customKeywords: [],
          frequency: {},
          context: {}
        },
        literals: {
          strings: [],
          numbers: [],
          booleans: [],
          nulls: [],
          patterns: []
        },
        identifiers: {
          variables: [],
          functions: [],
          classes: [],
          modules: [],
          namingConventions: {
            camelCase: 0,
            snakeCase: 0,
            pascalCase: 0,
            kebabCase: 0,
            constantCase: 0,
            consistency: 0,
            violations: []
          },
          undefinedReferences: []
        },
        operators: {
          arithmetic: [],
          logical: [],
          comparison: [],
          assignment: [],
          bitwise: [],
          complexity: 0,
          patterns: []
        },
        comments: {
          totalComments: 0,
          types: [],
          coverage: 0,
          quality: 0,
          todos: [],
          documentation: []
        }
      },
      structure: {
        syntaxTree: {
          type: 'root',
          range: selection,
          children: [],
          depth: 0,
          properties: {}
        },
        hierarchy: {
          level: 0,
          path: [],
          isTopLevel: true,
          childrenCount: 0
        },
        dependencies: [],
        complexity: {
          cyclomatic: 1,
          cognitive: 1,
          halstead: {
            vocabulary: 0,
            length: 0,
            volume: 0,
            difficulty: 0,
            effort: 0,
            time: 0,
            bugs: 0
          },
          maintainability: 100,
          testability: 100
        },
        patterns: [],
        cohesion: {
          score: 0,
          type: 'functional',
          strength: 'low',
          recommendations: []
        }
      },
      semantics: {
        purpose: {
          primaryPurpose: 'unknown',
          secondaryPurposes: [],
          confidence: 0,
          reasoning: [],
          domain: 'unknown'
        },
        functionality: {
          functions: [],
          transformations: [],
          computations: [],
          ioOperations: []
        },
        dataFlow: {
          inputs: [],
          outputs: [],
          transformations: [],
          cycles: [],
          bottlenecks: []
        },
        controlFlow: {
          branches: [],
          loops: [],
          jumps: [],
          complexity: 1,
          reachability: {
            reachableLines: [],
            unreachableLines: [],
            deadCode: [],
            coverage: 1
          }
        },
        sideEffects: {
          hasSideEffects: false,
          types: [],
          severity: 'none',
          globalVariables: [],
          externalCalls: [],
          fileOperations: [],
          networkOperations: []
        },
        performance: {
          timeComplexity: {
            bigO: 'O(1)',
            confidence: 0.5,
            factors: [],
            explanation: 'Unknown'
          },
          spaceComplexity: {
            bigO: 'O(1)',
            confidence: 0.5,
            factors: [],
            explanation: 'Unknown'
          },
          bottlenecks: [],
          optimizations: [],
          profileEstimate: {
            estimatedRuntime: 0,
            memoryUsage: 0,
            hotSpots: [],
            callFrequency: {}
          }
        }
      },
      intent: {
        primaryIntent: {
          action: 'analyze',
          target: 'statement',
          purpose: 'Unknown',
          scope: 'local',
          urgency: 'low',
          complexity: 1
        },
        alternativeIntents: [],
        confidence: 0,
        reasoning: [],
        history: [],
        predictions: []
      },
      suggestions: [],
      metrics: {
        efficiency: {
          selectionTime: 0,
          precisionRatio: 0,
          completenessRatio: 0,
          editDistance: 0,
          keystrokeEfficiency: 0
        },
        accuracy: {
          syntacticAccuracy: 0,
          semanticAccuracy: 0,
          intentAlignment: 0,
          boundaryAccuracy: 0,
          overallAccuracy: 0
        },
        usability: {
          easeOfSelection: 0,
          visualClarity: 0,
          feedbackQuality: 0,
          learnability: 0,
          accessibility: 0
        },
        learning: {
          patternRecognition: 0,
          adaptationRate: 0,
          improvementTrend: 0,
          userSatisfaction: 0,
          systemAccuracy: 0
        }
      },
      relationships: {
        internalRelationships: [],
        externalRelationships: [],
        crossFileRelationships: [],
        dependencyDepth: 0,
        couplingStrength: 0,
        cohesionLevel: 0
      },
      quality: {
        overallQuality: 0,
        maintainability: 0,
        readability: 0,
        testability: 0,
        reusability: 0,
        reliability: 0,
        security: {
          vulnerabilities: [],
          risks: [],
          recommendations: [],
          overallRisk: 'low'
        },
        codeSmells: []
      },
      opportunities: {
        refactoringOpportunities: [],
        optimizationOpportunities: [],
        testingOpportunities: [],
        documentationOpportunities: [],
        learningOpportunities: []
      }
    }
  }

  private getCacheKey(document: vscode.TextDocument, selection: vscode.Selection): string {
    return `${document.uri.fsPath}_${selection.start.line}_${selection.start.character}_${selection.end.line}_${selection.end.character}`
  }

  clearCache(): void {
    this.analysisCache.clear()
  }

  getCacheSize(): number {
    return this.analysisCache.size
  }
}