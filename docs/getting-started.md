# Getting Started with Natural Language Task Transformer

This guide will help you set up and start using the Natural Language Task Transformer extension in VSCode.

## Prerequisites

- Visual Studio Code 1.82.0 or later
- An API key from either:
  - OpenAI (GPT-4 recommended)
  - Anthropic (<PERSON>)

## Installation

### From VSCode Marketplace

1. Open VSCode
2. Press `Ctrl+Shift+X` (Windows/Linux) or `Cmd+Shift+X` (Mac) to open Extensions
3. Search for "Natural Language to Structured Task Transformation"
4. Click "Install"

### From VSIX Package

1. Download the `.vsix` file from the releases page
2. Open Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
3. Run `Extensions: Install from VSIX...`
4. Select the downloaded `.vsix` file

## Initial Setup

### 1. Configure AI Provider

After installation, you need to configure an AI provider:

1. Open Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Run `Task Transformer: Configure Task Transformer`
3. Choose your AI provider:
   - **OpenAI**: Recommended for general use
   - **Anthropic**: Good for complex reasoning tasks

### 2. Set API Key

The extension will prompt you to enter your API key. This is stored securely in VSCode's SecretStorage.

**For OpenAI:**
1. Visit [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Create a new API key
3. Copy the key (starts with `sk-`)
4. Enter it when prompted

**For Anthropic:**
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Create a new API key
3. Copy the key (starts with `sk-ant-`)
4. Enter it when prompted

### 3. Configure Settings (Optional)

You can customize the extension behavior through VSCode settings:

1. Open Settings (`Ctrl+,` or `Cmd+,`)
2. Search for "Task Transformer"
3. Adjust settings as needed

## Basic Usage

### Transform Natural Language to Tasks

1. **Select text** in your editor containing natural language description
2. **Right-click** and select "Transform Natural Language to Tasks"
3. **Or use Command Palette**: `Task Transformer: Transform Natural Language to Tasks`
4. **Enter your description** in the input box
5. **Review the generated tasks** in the output

#### Example:

**Input:**
```
"Create a user authentication system with JWT tokens and password reset functionality"
```

**Generated Output:**
```markdown
## User Authentication System

### Primary Tasks:
1. **Set up authentication middleware**
   - Install JWT library
   - Create token generation utility
   - Add middleware for protected routes

2. **Implement user registration**
   - Create user model/schema
   - Add password hashing
   - Validate email format

3. **Build login functionality**
   - Create login endpoint
   - Verify credentials
   - Generate JWT tokens

4. **Add password reset**
   - Create reset token system
   - Send reset emails
   - Validate reset tokens

### Files to Create/Modify:
- `src/middleware/auth.js`
- `src/models/User.js`
- `src/routes/auth.js`
- `src/utils/jwt.js`
- `src/utils/email.js`
```

### Get Context-Aware Suggestions

1. **Select code** in your editor
2. **Right-click** and select "Get Context-Aware Suggestions"
3. **Review suggestions** based on your codebase context

#### Example:

**Selected Code:**
```javascript
function calculateTotal(items) {
    return items.reduce((sum, item) => sum + item.price, 0);
}
```

**Generated Suggestions:**
```markdown
## Suggestions for calculateTotal function

### Improvements:
- Add input validation for items array
- Handle edge cases (empty array, null values)
- Add TypeScript types for better type safety
- Consider adding tax calculation
- Add unit tests

### Security Considerations:
- Validate that price values are numbers
- Check for negative prices
- Prevent overflow for large calculations
```

## Advanced Features

### Template Customization

The extension uses Handlebars templates that you can customize:

1. Create a `.vscode/task-templates/` directory in your project
2. Add custom templates (e.g., `feature.hbs`, `bugfix.hbs`)
3. The extension will automatically use project-specific templates

### Learning and Adaptation

The extension learns from your usage patterns:

- **Preferences**: Remembers your preferred task structures
- **Patterns**: Recognizes common patterns in your codebase
- **Context**: Improves suggestions based on your project type

### Codebase Indexing

The extension automatically indexes your codebase for better context:

- **File Analysis**: Understands your project structure
- **Symbol Extraction**: Identifies functions, classes, and variables
- **Dependency Mapping**: Tracks relationships between files

## Troubleshooting

### Common Issues

#### "API Key Invalid" Error
1. Check that your API key is correctly entered
2. Verify the API key is active in your provider's dashboard
3. Ensure you have sufficient credits/usage quota

#### "No Context Found" Warning
1. Make sure your project has files in supported languages
2. Check that indexing is enabled in settings
3. Wait for initial indexing to complete

#### Slow Performance
1. Reduce `maxIndexFiles` in settings for large projects
2. Add patterns to `excludePatterns` for faster indexing
3. Consider using a more powerful AI model

### Getting Help

If you encounter issues:

1. Check the [troubleshooting guide](troubleshooting.md)
2. Review the [FAQ](faq.md)
3. Report issues on [GitHub](https://github.com/yourusername/natural-language-task-transformer/issues)

## Next Steps

- Read the [User Guide](user-guide.md) for detailed usage instructions
- Explore [Advanced Features](advanced-features.md)
- Check out [Examples](examples.md) for more use cases
- Join the [Community](https://github.com/yourusername/natural-language-task-transformer/discussions)

## Tips for Best Results

1. **Be specific** in your natural language descriptions
2. **Include context** about your project type and goals
3. **Use proper grammar** for better AI understanding
4. **Review and customize** generated tasks as needed
5. **Provide feedback** to improve the learning system