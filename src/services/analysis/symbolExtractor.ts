import { TypeScriptAnalyzer, TypeScriptSymbol, AnalysisResult } from './typescriptAnalyzer'
import { ConnectionManager } from '../database/connectionManager'
import { FileSystemService } from '../fileSystemService'

export interface SymbolInfo {
  id?: number
  fileId: number
  name: string
  type: string
  kind: string
  line: number
  column: number
  endLine: number
  endColumn: number
  scope: string
  signature?: string
  documentation?: string
  isExported: boolean
  modifiers: string[]
  complexity: number
  references: number
  usages: SymbolUsage[]
}

export interface SymbolUsage {
  filePath: string
  line: number
  column: number
  usageType: 'declaration' | 'reference' | 'call' | 'import' | 'export'
  context?: string
}

export interface SymbolIndex {
  symbols: Map<string, SymbolInfo[]>
  globalSymbols: Map<string, SymbolInfo>
  exportedSymbols: Map<string, SymbolInfo[]>
  symbolsByFile: Map<string, SymbolInfo[]>
  symbolsByType: Map<string, SymbolInfo[]>
}

export interface SymbolSearchResult {
  symbol: SymbolInfo
  filePath: string
  score: number
  context: string
}

export class SymbolExtractor {
  private db: ConnectionManager
  private fileSystem: FileSystemService
  private tsAnalyzer: TypeScriptAnalyzer
  private symbolIndex: SymbolIndex

  constructor(
    db: ConnectionManager,
    fileSystem: FileSystemService,
    tsAnalyzer: TypeScriptAnalyzer
  ) {
    this.db = db
    this.fileSystem = fileSystem
    this.tsAnalyzer = tsAnalyzer
    this.symbolIndex = {
      symbols: new Map(),
      globalSymbols: new Map(),
      exportedSymbols: new Map(),
      symbolsByFile: new Map(),
      symbolsByType: new Map()
    }
  }

  async extractSymbolsFromFile(filePath: string): Promise<SymbolInfo[]> {
    const analysisResult = await this.tsAnalyzer.analyzeFile(filePath)
    const fileRecord = await this.getFileRecord(filePath)
    
    if (!fileRecord) {
      throw new Error(`File not found in database: ${filePath}`)
    }

    const symbols: SymbolInfo[] = []
    
    for (const tsSymbol of analysisResult.symbols) {
      const symbolInfo = await this.convertTsSymbolToSymbolInfo(tsSymbol, fileRecord.id)
      symbols.push(symbolInfo)
    }

    await this.saveSymbolsToDatabase(symbols)
    return symbols
  }

  async extractSymbolsFromProject(projectRoot: string, filePaths: string[]): Promise<SymbolIndex> {
    const analysisResults = await this.tsAnalyzer.analyzeProject(projectRoot, filePaths)
    const allSymbols: SymbolInfo[] = []

    for (const [filePath, analysis] of analysisResults) {
      const fileRecord = await this.getFileRecord(filePath)
      if (!fileRecord) continue

      const symbols = await this.processFileSymbols(analysis, fileRecord.id)
      allSymbols.push(...symbols)
    }

    await this.saveSymbolsToDatabase(allSymbols)
    this.buildSymbolIndex(allSymbols)
    
    return this.symbolIndex
  }

  private async convertTsSymbolToSymbolInfo(
    tsSymbol: TypeScriptSymbol,
    fileId: number
  ): Promise<SymbolInfo> {
    const complexity = this.calculateSymbolComplexity(tsSymbol)
    const references = await this.countSymbolReferences(tsSymbol.name, fileId)
    const usages = await this.findSymbolUsages(tsSymbol.name, fileId)

    return {
      fileId,
      name: tsSymbol.name,
      type: tsSymbol.type,
      kind: tsSymbol.kind.toString(),
      line: tsSymbol.line,
      column: tsSymbol.column,
      endLine: tsSymbol.endLine,
      endColumn: tsSymbol.endColumn,
      scope: tsSymbol.scope,
      signature: tsSymbol.signature,
      documentation: tsSymbol.documentation,
      isExported: tsSymbol.isExported,
      modifiers: tsSymbol.modifiers,
      complexity,
      references,
      usages
    }
  }

  private async processFileSymbols(
    analysis: AnalysisResult,
    fileId: number
  ): Promise<SymbolInfo[]> {
    const symbols: SymbolInfo[] = []
    
    for (const tsSymbol of analysis.symbols) {
      const symbolInfo = await this.convertTsSymbolToSymbolInfo(tsSymbol, fileId)
      symbols.push(symbolInfo)
    }

    return symbols
  }

  private async saveSymbolsToDatabase(symbols: SymbolInfo[]): Promise<void> {
    await this.db.transaction(async (tx) => {
      for (const symbol of symbols) {
        await tx.execute(
          tx.insert()
            .into('symbols')
            .columns([
              'file_id', 'name', 'type', 'line_start', 'line_end',
              'column_start', 'column_end', 'scope', 'signature',
              'documentation', 'is_exported'
            ])
            .values([
              symbol.fileId,
              symbol.name,
              symbol.type,
              symbol.line,
              symbol.endLine,
              symbol.column,
              symbol.endColumn,
              symbol.scope,
              symbol.signature,
              symbol.documentation,
              symbol.isExported ? 1 : 0
            ])
        )
      }
    })
  }

  private buildSymbolIndex(symbols: SymbolInfo[]): void {
    this.symbolIndex.symbols.clear()
    this.symbolIndex.globalSymbols.clear()
    this.symbolIndex.exportedSymbols.clear()
    this.symbolIndex.symbolsByFile.clear()
    this.symbolIndex.symbolsByType.clear()

    for (const symbol of symbols) {
      // Index by name
      if (!this.symbolIndex.symbols.has(symbol.name)) {
        this.symbolIndex.symbols.set(symbol.name, [])
      }
      this.symbolIndex.symbols.get(symbol.name)!.push(symbol)

      // Index global symbols
      if (symbol.scope === 'global') {
        this.symbolIndex.globalSymbols.set(symbol.name, symbol)
      }

      // Index exported symbols
      if (symbol.isExported) {
        if (!this.symbolIndex.exportedSymbols.has(symbol.name)) {
          this.symbolIndex.exportedSymbols.set(symbol.name, [])
        }
        this.symbolIndex.exportedSymbols.get(symbol.name)!.push(symbol)
      }

      // Index by file
      const fileKey = symbol.fileId.toString()
      if (!this.symbolIndex.symbolsByFile.has(fileKey)) {
        this.symbolIndex.symbolsByFile.set(fileKey, [])
      }
      this.symbolIndex.symbolsByFile.get(fileKey)!.push(symbol)

      // Index by type
      if (!this.symbolIndex.symbolsByType.has(symbol.type)) {
        this.symbolIndex.symbolsByType.set(symbol.type, [])
      }
      this.symbolIndex.symbolsByType.get(symbol.type)!.push(symbol)
    }
  }

  async searchSymbols(query: string, options: {
    type?: string
    scope?: string
    exported?: boolean
    limit?: number
  } = {}): Promise<SymbolSearchResult[]> {
    const results: SymbolSearchResult[] = []
    const searchQuery = query.toLowerCase()
    
    const queryBuilder = this.db.query()
      .select('s.*, f.path as file_path')
      .from('symbols s')
      .innerJoin('files f', 'f.id = s.file_id')
      .where('s.name', 'LIKE', `%${query}%`)

    if (options.type) {
      queryBuilder.where('s.type', '=', options.type)
    }

    if (options.scope) {
      queryBuilder.where('s.scope', '=', options.scope)
    }

    if (options.exported !== undefined) {
      queryBuilder.where('s.is_exported', '=', options.exported ? 1 : 0)
    }

    if (options.limit) {
      queryBuilder.limit(options.limit)
    }

    const symbols = await this.db.findMany(queryBuilder)

    for (const symbol of symbols) {
      const score = this.calculateSearchScore(symbol.name, searchQuery)
      const context = await this.getSymbolContext(symbol.file_id, symbol.line_start)
      
      results.push({
        symbol: {
          id: symbol.id,
          fileId: symbol.file_id,
          name: symbol.name,
          type: symbol.type,
          kind: symbol.kind || '',
          line: symbol.line_start,
          column: symbol.column_start,
          endLine: symbol.line_end,
          endColumn: symbol.column_end,
          scope: symbol.scope,
          signature: symbol.signature,
          documentation: symbol.documentation,
          isExported: symbol.is_exported === 1,
          modifiers: [],
          complexity: 0,
          references: 0,
          usages: []
        },
        filePath: symbol.file_path,
        score,
        context
      })
    }

    return results.sort((a, b) => b.score - a.score)
  }

  async getSymbolDefinition(symbolName: string, filePath: string): Promise<SymbolInfo | null> {
    const fileRecord = await this.getFileRecord(filePath)
    if (!fileRecord) return null

    const symbol = await this.db.findOne(
      this.db.query()
        .select('*')
        .from('symbols')
        .where('name', '=', symbolName)
        .where('file_id', '=', fileRecord.id)
    )

    if (!symbol) return null

    return {
      id: symbol.id,
      fileId: symbol.file_id,
      name: symbol.name,
      type: symbol.type,
      kind: symbol.kind || '',
      line: symbol.line_start,
      column: symbol.column_start,
      endLine: symbol.line_end,
      endColumn: symbol.column_end,
      scope: symbol.scope,
      signature: symbol.signature,
      documentation: symbol.documentation,
      isExported: symbol.is_exported === 1,
      modifiers: [],
      complexity: 0,
      references: 0,
      usages: []
    }
  }

  async getSymbolReferences(symbolName: string, projectRoot: string): Promise<SymbolUsage[]> {
    const usages: SymbolUsage[] = []
    
    // Find all files that might reference this symbol
    const files = await this.db.findMany(
      this.db.query()
        .select('f.path, fc.content')
        .from('files f')
        .innerJoin('file_contents fc', 'fc.file_id = f.id')
        .where('fc.content', 'LIKE', `%${symbolName}%`)
    )

    for (const file of files) {
      const content = file.content
      const lines = content.split('\n')
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        const index = line.indexOf(symbolName)
        
        if (index !== -1) {
          const usageType = this.determineUsageType(line, symbolName, index)
          usages.push({
            filePath: file.path,
            line: i + 1,
            column: index + 1,
            usageType,
            context: line.trim()
          })
        }
      }
    }

    return usages
  }

  async getSymbolsByFile(filePath: string): Promise<SymbolInfo[]> {
    const fileRecord = await this.getFileRecord(filePath)
    if (!fileRecord) return []

    const symbols = await this.db.findMany(
      this.db.query()
        .select('*')
        .from('symbols')
        .where('file_id', '=', fileRecord.id)
        .orderBy('line_start', 'ASC')
    )

    return symbols.map(symbol => ({
      id: symbol.id,
      fileId: symbol.file_id,
      name: symbol.name,
      type: symbol.type,
      kind: symbol.kind || '',
      line: symbol.line_start,
      column: symbol.column_start,
      endLine: symbol.line_end,
      endColumn: symbol.column_end,
      scope: symbol.scope,
      signature: symbol.signature,
      documentation: symbol.documentation,
      isExported: symbol.is_exported === 1,
      modifiers: [],
      complexity: 0,
      references: 0,
      usages: []
    }))
  }

  private async getFileRecord(filePath: string): Promise<{ id: number } | null> {
    return await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', filePath)
    )
  }

  private calculateSymbolComplexity(symbol: TypeScriptSymbol): number {
    let complexity = 1

    if (symbol.type === 'function') {
      complexity += 2
      if (symbol.signature) {
        const paramCount = (symbol.signature.match(/,/g) || []).length + 1
        complexity += paramCount * 0.5
      }
    } else if (symbol.type === 'class') {
      complexity += 3
    } else if (symbol.type === 'interface') {
      complexity += 2
    }

    if (symbol.isExported) {
      complexity += 1
    }

    return Math.round(complexity)
  }

  private async countSymbolReferences(symbolName: string, fileId: number): Promise<number> {
    const result = await this.db.findOne(
      this.db.query()
        .select('COUNT(*) as count')
        .from('file_contents fc')
        .where('fc.file_id', '=', fileId)
        .where('fc.content', 'LIKE', `%${symbolName}%`)
    )

    return result?.count || 0
  }

  private async findSymbolUsages(symbolName: string, fileId: number): Promise<SymbolUsage[]> {
    // This is a simplified implementation
    // In a real implementation, you would use AST parsing to find actual usages
    return []
  }

  private calculateSearchScore(symbolName: string, query: string): number {
    const name = symbolName.toLowerCase()
    const q = query.toLowerCase()
    
    if (name === q) return 100
    if (name.startsWith(q)) return 90
    if (name.endsWith(q)) return 80
    if (name.includes(q)) return 70
    
    // Fuzzy matching
    let score = 0
    let j = 0
    
    for (let i = 0; i < name.length && j < q.length; i++) {
      if (name[i] === q[j]) {
        score += 10
        j++
      }
    }
    
    return score
  }

  private async getSymbolContext(fileId: number, lineNumber: number): Promise<string> {
    const content = await this.db.findOne(
      this.db.query()
        .select('content')
        .from('file_contents')
        .where('file_id', '=', fileId)
    )

    if (!content) return ''

    const lines = content.content.split('\n')
    if (lineNumber > 0 && lineNumber <= lines.length) {
      return lines[lineNumber - 1].trim()
    }

    return ''
  }

  private determineUsageType(line: string, symbolName: string, index: number): SymbolUsage['usageType'] {
    const trimmed = line.trim()
    
    if (trimmed.startsWith('import') && trimmed.includes(symbolName)) {
      return 'import'
    }
    
    if (trimmed.startsWith('export') && trimmed.includes(symbolName)) {
      return 'export'
    }
    
    if (trimmed.includes(`${symbolName}(`)) {
      return 'call'
    }
    
    if (trimmed.includes(`function ${symbolName}`) || 
        trimmed.includes(`class ${symbolName}`) ||
        trimmed.includes(`interface ${symbolName}`) ||
        trimmed.includes(`type ${symbolName}`)) {
      return 'declaration'
    }
    
    return 'reference'
  }

  getSymbolIndex(): SymbolIndex {
    return this.symbolIndex
  }
}