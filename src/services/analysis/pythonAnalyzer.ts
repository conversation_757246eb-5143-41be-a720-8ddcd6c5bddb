import * as Parser from 'dt-python-parser'
import { FileSystemService } from '../fileSystemService'

export interface PythonSymbol {
  name: string
  type: 'function' | 'class' | 'variable' | 'method' | 'property' | 'import' | 'constant'
  line: number
  column: number
  endLine: number
  endColumn: number
  scope: string
  signature?: string
  documentation?: string
  decorators: string[]
  isAsync: boolean
  isPrivate: boolean
  isStatic: boolean
  isClassMethod: boolean
  isProperty: boolean
  parentClass?: string
  arguments?: PythonArgument[]
  returnType?: string
  filePath: string
}

export interface PythonArgument {
  name: string
  type?: string
  defaultValue?: string
  isKeyword: boolean
  isVarArgs: boolean
  isKwArgs: boolean
}

export interface PythonImport {
  module: string
  names: string[]
  alias?: string
  isFromImport: boolean
  line: number
  column: number
  isLocal: boolean
  level: number // For relative imports
}

export interface PythonClass {
  name: string
  line: number
  column: number
  endLine: number
  endColumn: number
  baseClasses: string[]
  methods: PythonSymbol[]
  properties: PythonSymbol[]
  variables: PythonSymbol[]
  decorators: string[]
  documentation?: string
  isAbstract: boolean
}

export interface PythonFunction {
  name: string
  line: number
  column: number
  endLine: number
  endColumn: number
  arguments: PythonArgument[]
  returnType?: string
  decorators: string[]
  documentation?: string
  isAsync: boolean
  isGenerator: boolean
  isLambda: boolean
  scope: string
}

export interface PythonAnalysisResult {
  symbols: PythonSymbol[]
  imports: PythonImport[]
  classes: PythonClass[]
  functions: PythonFunction[]
  variables: PythonSymbol[]
  constants: PythonSymbol[]
  dependencies: string[]
  ast: any
  errors: string[]
}

export class PythonAnalyzer {
  private fileSystem: FileSystemService
  private parser: any

  constructor(fileSystem: FileSystemService) {
    this.fileSystem = fileSystem
    this.parser = new Parser.Parser()
  }

  async analyzeFile(filePath: string): Promise<PythonAnalysisResult> {
    const content = await this.fileSystem.readFile(filePath)
    return this.analyzeContent(content, filePath)
  }

  analyzeContent(content: string, filePath: string): PythonAnalysisResult {
    const result: PythonAnalysisResult = {
      symbols: [],
      imports: [],
      classes: [],
      functions: [],
      variables: [],
      constants: [],
      dependencies: [],
      ast: null,
      errors: []
    }

    try {
      const ast = this.parser.parse(content)
      result.ast = ast

      this.visitNode(ast, result, filePath, 'global')
      result.dependencies = [...new Set(result.imports.map(imp => imp.module))]
    } catch (error) {
      result.errors.push(`Parse error: ${error}`)
    }

    return result
  }

  private visitNode(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    if (!node || typeof node !== 'object') {
      return
    }

    switch (node.type) {
      case 'Module':
        this.visitChildren(node, result, filePath, scope)
        break
      case 'FunctionDef':
      case 'AsyncFunctionDef':
        this.analyzeFunctionDef(node, result, filePath, scope)
        break
      case 'ClassDef':
        this.analyzeClassDef(node, result, filePath, scope)
        break
      case 'Import':
        this.analyzeImport(node, result)
        break
      case 'ImportFrom':
        this.analyzeImportFrom(node, result)
        break
      case 'Assign':
        this.analyzeAssignment(node, result, filePath, scope)
        break
      case 'AnnAssign':
        this.analyzeAnnotatedAssignment(node, result, filePath, scope)
        break
      case 'AugAssign':
        this.analyzeAugmentedAssignment(node, result, filePath, scope)
        break
      default:
        this.visitChildren(node, result, filePath, scope)
        break
    }
  }

  private visitChildren(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    if (node.body) {
      for (const child of node.body) {
        this.visitNode(child, result, filePath, scope)
      }
    }

    if (node.orelse) {
      for (const child of node.orelse) {
        this.visitNode(child, result, filePath, scope)
      }
    }

    if (node.handlers) {
      for (const handler of node.handlers) {
        this.visitNode(handler, result, filePath, scope)
      }
    }

    if (node.finalbody) {
      for (const child of node.finalbody) {
        this.visitNode(child, result, filePath, scope)
      }
    }
  }

  private analyzeFunctionDef(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    const name = node.name
    const line = node.lineno || 0
    const column = node.col_offset || 0
    const endLine = node.end_lineno || line
    const endColumn = node.end_col_offset || column
    const isAsync = node.type === 'AsyncFunctionDef'
    const decorators = this.extractDecorators(node.decorator_list)
    const documentation = this.extractDocstring(node)
    const arguments = this.extractArguments(node.args)
    const returnType = this.extractReturnType(node)

    const pythonFunction: PythonFunction = {
      name,
      line,
      column,
      endLine,
      endColumn,
      arguments,
      returnType,
      decorators,
      documentation,
      isAsync,
      isGenerator: this.containsYield(node),
      isLambda: false,
      scope
    }

    result.functions.push(pythonFunction)

    const symbol: PythonSymbol = {
      name,
      type: 'function',
      line,
      column,
      endLine,
      endColumn,
      scope,
      signature: this.buildFunctionSignature(pythonFunction),
      documentation,
      decorators,
      isAsync,
      isPrivate: name.startsWith('_'),
      isStatic: decorators.includes('staticmethod'),
      isClassMethod: decorators.includes('classmethod'),
      isProperty: decorators.includes('property'),
      arguments,
      returnType,
      filePath
    }

    result.symbols.push(symbol)

    // Visit function body with new scope
    const functionScope = scope === 'global' ? name : `${scope}.${name}`
    this.visitChildren(node, result, filePath, functionScope)
  }

  private analyzeClassDef(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    const name = node.name
    const line = node.lineno || 0
    const column = node.col_offset || 0
    const endLine = node.end_lineno || line
    const endColumn = node.end_col_offset || column
    const decorators = this.extractDecorators(node.decorator_list)
    const documentation = this.extractDocstring(node)
    const baseClasses = this.extractBaseClasses(node.bases)

    const pythonClass: PythonClass = {
      name,
      line,
      column,
      endLine,
      endColumn,
      baseClasses,
      methods: [],
      properties: [],
      variables: [],
      decorators,
      documentation,
      isAbstract: this.isAbstractClass(decorators, node)
    }

    result.classes.push(pythonClass)

    const symbol: PythonSymbol = {
      name,
      type: 'class',
      line,
      column,
      endLine,
      endColumn,
      scope,
      signature: this.buildClassSignature(pythonClass),
      documentation,
      decorators,
      isAsync: false,
      isPrivate: name.startsWith('_'),
      isStatic: false,
      isClassMethod: false,
      isProperty: false,
      filePath
    }

    result.symbols.push(symbol)

    // Visit class body with new scope
    const classScope = scope === 'global' ? name : `${scope}.${name}`
    this.visitChildren(node, result, filePath, classScope)
  }

  private analyzeImport(node: any, result: PythonAnalysisResult): void {
    const line = node.lineno || 0
    const column = node.col_offset || 0

    for (const alias of node.names) {
      const module = alias.name
      const asname = alias.asname
      
      const pythonImport: PythonImport = {
        module,
        names: [asname || module],
        alias: asname,
        isFromImport: false,
        line,
        column,
        isLocal: this.isLocalModule(module),
        level: 0
      }

      result.imports.push(pythonImport)
    }
  }

  private analyzeImportFrom(node: any, result: PythonAnalysisResult): void {
    const module = node.module || ''
    const line = node.lineno || 0
    const column = node.col_offset || 0
    const level = node.level || 0

    const names: string[] = []
    let alias: string | undefined

    if (node.names) {
      for (const nameAlias of node.names) {
        const name = nameAlias.name
        const asname = nameAlias.asname
        names.push(asname || name)
        
        if (asname) {
          alias = asname
        }
      }
    }

    const pythonImport: PythonImport = {
      module,
      names,
      alias,
      isFromImport: true,
      line,
      column,
      isLocal: this.isLocalModule(module) || level > 0,
      level
    }

    result.imports.push(pythonImport)
  }

  private analyzeAssignment(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    const line = node.lineno || 0
    const column = node.col_offset || 0

    for (const target of node.targets) {
      if (target.type === 'Name') {
        const name = target.id
        const isConstant = this.isConstantName(name)
        
        const symbol: PythonSymbol = {
          name,
          type: isConstant ? 'constant' : 'variable',
          line,
          column,
          endLine: line,
          endColumn: column,
          scope,
          isAsync: false,
          isPrivate: name.startsWith('_'),
          isStatic: false,
          isClassMethod: false,
          isProperty: false,
          decorators: [],
          filePath
        }

        result.symbols.push(symbol)
        
        if (isConstant) {
          result.constants.push(symbol)
        } else {
          result.variables.push(symbol)
        }
      }
    }
  }

  private analyzeAnnotatedAssignment(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    if (node.target && node.target.type === 'Name') {
      const name = node.target.id
      const line = node.lineno || 0
      const column = node.col_offset || 0
      const isConstant = this.isConstantName(name)
      const type = this.extractTypeAnnotation(node.annotation)

      const symbol: PythonSymbol = {
        name,
        type: isConstant ? 'constant' : 'variable',
        line,
        column,
        endLine: line,
        endColumn: column,
        scope,
        signature: type ? `${name}: ${type}` : name,
        isAsync: false,
        isPrivate: name.startsWith('_'),
        isStatic: false,
        isClassMethod: false,
        isProperty: false,
        decorators: [],
        filePath
      }

      result.symbols.push(symbol)
      
      if (isConstant) {
        result.constants.push(symbol)
      } else {
        result.variables.push(symbol)
      }
    }
  }

  private analyzeAugmentedAssignment(node: any, result: PythonAnalysisResult, filePath: string, scope: string): void {
    if (node.target && node.target.type === 'Name') {
      const name = node.target.id
      const line = node.lineno || 0
      const column = node.col_offset || 0

      const symbol: PythonSymbol = {
        name,
        type: 'variable',
        line,
        column,
        endLine: line,
        endColumn: column,
        scope,
        isAsync: false,
        isPrivate: name.startsWith('_'),
        isStatic: false,
        isClassMethod: false,
        isProperty: false,
        decorators: [],
        filePath
      }

      result.symbols.push(symbol)
      result.variables.push(symbol)
    }
  }

  private extractDecorators(decoratorList: any[]): string[] {
    if (!decoratorList) return []
    
    return decoratorList.map(decorator => {
      if (decorator.type === 'Name') {
        return decorator.id
      } else if (decorator.type === 'Attribute') {
        return this.extractAttributeName(decorator)
      } else if (decorator.type === 'Call') {
        return this.extractCallName(decorator)
      }
      return 'unknown'
    })
  }

  private extractDocstring(node: any): string | undefined {
    if (node.body && node.body.length > 0) {
      const firstStmt = node.body[0]
      if (firstStmt.type === 'Expr' && firstStmt.value && firstStmt.value.type === 'Str') {
        return firstStmt.value.s
      }
    }
    return undefined
  }

  private extractArguments(args: any): PythonArgument[] {
    if (!args) return []

    const arguments: PythonArgument[] = []
    
    // Regular arguments
    if (args.args) {
      for (const arg of args.args) {
        arguments.push({
          name: arg.arg,
          type: this.extractTypeAnnotation(arg.annotation),
          isKeyword: false,
          isVarArgs: false,
          isKwArgs: false
        })
      }
    }

    // *args
    if (args.vararg) {
      arguments.push({
        name: args.vararg.arg,
        type: this.extractTypeAnnotation(args.vararg.annotation),
        isKeyword: false,
        isVarArgs: true,
        isKwArgs: false
      })
    }

    // **kwargs
    if (args.kwarg) {
      arguments.push({
        name: args.kwarg.arg,
        type: this.extractTypeAnnotation(args.kwarg.annotation),
        isKeyword: false,
        isVarArgs: false,
        isKwArgs: true
      })
    }

    // Keyword-only arguments
    if (args.kwonlyargs) {
      for (const arg of args.kwonlyargs) {
        arguments.push({
          name: arg.arg,
          type: this.extractTypeAnnotation(arg.annotation),
          isKeyword: true,
          isVarArgs: false,
          isKwArgs: false
        })
      }
    }

    return arguments
  }

  private extractReturnType(node: any): string | undefined {
    if (node.returns) {
      return this.extractTypeAnnotation(node.returns)
    }
    return undefined
  }

  private extractTypeAnnotation(annotation: any): string | undefined {
    if (!annotation) return undefined

    switch (annotation.type) {
      case 'Name':
        return annotation.id
      case 'Attribute':
        return this.extractAttributeName(annotation)
      case 'Subscript':
        return this.extractSubscriptName(annotation)
      case 'Str':
        return annotation.s
      default:
        return 'Any'
    }
  }

  private extractAttributeName(node: any): string {
    if (node.value && node.value.type === 'Name') {
      return `${node.value.id}.${node.attr}`
    }
    return node.attr
  }

  private extractCallName(node: any): string {
    if (node.func) {
      if (node.func.type === 'Name') {
        return node.func.id
      } else if (node.func.type === 'Attribute') {
        return this.extractAttributeName(node.func)
      }
    }
    return 'unknown'
  }

  private extractSubscriptName(node: any): string {
    const value = node.value?.id || 'unknown'
    const slice = this.extractSliceValue(node.slice)
    return `${value}[${slice}]`
  }

  private extractSliceValue(slice: any): string {
    if (!slice) return ''
    
    if (slice.type === 'Name') {
      return slice.id
    } else if (slice.type === 'Tuple') {
      return slice.elts.map((elt: any) => this.extractSliceValue(elt)).join(', ')
    }
    
    return 'unknown'
  }

  private extractBaseClasses(bases: any[]): string[] {
    if (!bases) return []
    
    return bases.map(base => {
      if (base.type === 'Name') {
        return base.id
      } else if (base.type === 'Attribute') {
        return this.extractAttributeName(base)
      }
      return 'unknown'
    })
  }

  private buildFunctionSignature(func: PythonFunction): string {
    const args = func.arguments.map(arg => {
      let argStr = arg.name
      if (arg.type) {
        argStr += `: ${arg.type}`
      }
      if (arg.defaultValue) {
        argStr += ` = ${arg.defaultValue}`
      }
      if (arg.isVarArgs) {
        argStr = `*${argStr}`
      } else if (arg.isKwArgs) {
        argStr = `**${argStr}`
      }
      return argStr
    }).join(', ')

    let signature = `def ${func.name}(${args})`
    if (func.returnType) {
      signature += ` -> ${func.returnType}`
    }
    
    return signature
  }

  private buildClassSignature(cls: PythonClass): string {
    let signature = `class ${cls.name}`
    if (cls.baseClasses.length > 0) {
      signature += `(${cls.baseClasses.join(', ')})`
    }
    return signature
  }

  private containsYield(node: any): boolean {
    // Simplified yield detection - would need more sophisticated AST traversal
    return false
  }

  private isAbstractClass(decorators: string[], node: any): boolean {
    return decorators.includes('abstractmethod') || decorators.includes('ABC')
  }

  private isConstantName(name: string): boolean {
    return name === name.toUpperCase() && name.includes('_')
  }

  private isLocalModule(module: string): boolean {
    return module.startsWith('.') || !module.includes('.')
  }
}