import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'
import { PreferenceTracker } from '../services/memory/preferenceTracker'

export interface SettingsConfiguration {
  general: GeneralSettings
  ai: AISettings
  templates: TemplateSettings
  suggestions: SuggestionSettings
  ui: UISettings
  performance: PerformanceSettings
  security: SecuritySettings
  advanced: AdvancedSettings
}

export interface GeneralSettings {
  autoSave: boolean
  autoUpdate: boolean
  enableTelemetry: boolean
  enableNotifications: boolean
  defaultLanguage: string
  defaultFramework: string
  workspaceIntegration: boolean
  gitIntegration: boolean
  keyboardShortcuts: KeyboardShortcuts
  accessibility: AccessibilitySettings
}

export interface KeyboardShortcuts {
  transformText: string
  suggestImprovements: string
  selectTemplate: string
  browseTemplates: string
  createTemplate: string
  quickTransform: string
  showHistory: string
  toggleSidebar: string
}

export interface AccessibilitySettings {
  highContrast: boolean
  largeText: boolean
  screenReader: boolean
  reducedMotion: boolean
  keyboardNavigation: boolean
  announcements: boolean
}

export interface AISettings {
  provider: 'openai' | 'claude' | 'custom'
  model: string
  apiKey: string
  apiUrl: string
  temperature: number
  maxTokens: number
  timeoutSeconds: number
  retryAttempts: number
  enableCaching: boolean
  enableStreaming: boolean
  customHeaders: Record<string, string>
  rateLimiting: RateLimitSettings
}

export interface RateLimitSettings {
  enabled: boolean
  requestsPerMinute: number
  tokensPerMinute: number
  burstLimit: number
  backoffStrategy: 'exponential' | 'linear' | 'fixed'
}

export interface TemplateSettings {
  autoComplete: boolean
  showPreviews: boolean
  enableSharing: boolean
  syncSettings: boolean
  defaultCategory: string
  sortBy: 'name' | 'date' | 'usage' | 'rating'
  viewMode: 'grid' | 'list' | 'detailed'
  maxRecentTemplates: number
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'never'
  compressionLevel: number
  validation: TemplateValidationSettings
}

export interface TemplateValidationSettings {
  enableValidation: boolean
  strictMode: boolean
  customRules: ValidationRule[]
  autoFix: boolean
  showWarnings: boolean
}

export interface ValidationRule {
  name: string
  pattern: string
  message: string
  severity: 'error' | 'warning' | 'info'
  enabled: boolean
}

export interface SuggestionSettings {
  enableAI: boolean
  enableLearning: boolean
  enableSemanticSearch: boolean
  maxSuggestions: number
  confidenceThreshold: number
  includeExplanations: boolean
  includeExamples: boolean
  autoApply: boolean
  categories: SuggestionCategory[]
  filters: SuggestionFilter[]
  prioritization: PrioritizationSettings
}

export interface SuggestionCategory {
  name: string
  enabled: boolean
  priority: number
  color: string
  icon: string
}

export interface SuggestionFilter {
  type: 'category' | 'complexity' | 'language' | 'framework' | 'confidence'
  value: string | number
  operator: 'eq' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in'
  enabled: boolean
}

export interface PrioritizationSettings {
  primaryFactor: 'relevance' | 'confidence' | 'impact' | 'effort'
  secondaryFactor: 'relevance' | 'confidence' | 'impact' | 'effort'
  userHistoryWeight: number
  contextWeight: number
  popularityWeight: number
}

export interface UISettings {
  theme: 'system' | 'light' | 'dark' | 'high-contrast'
  fontSize: number
  fontFamily: string
  lineHeight: number
  colorScheme: string
  animations: boolean
  transitions: boolean
  layout: LayoutSettings
  panels: PanelSettings
  statusBar: StatusBarSettings
}

export interface LayoutSettings {
  sidebarPosition: 'left' | 'right'
  panelPosition: 'bottom' | 'right'
  activityBarPosition: 'left' | 'right'
  minimap: boolean
  breadcrumbs: boolean
  tabs: boolean
  indentGuides: boolean
}

export interface PanelSettings {
  showWelcome: boolean
  showTips: boolean
  showMetrics: boolean
  showHistory: boolean
  defaultPanel: string
  collapsible: boolean
  resizable: boolean
}

export interface StatusBarSettings {
  showStatus: boolean
  showProgress: boolean
  showMetrics: boolean
  showErrors: boolean
  position: 'left' | 'right'
  priority: number
}

export interface PerformanceSettings {
  enableCaching: boolean
  cacheSize: number
  cacheExpiration: number
  enableIndexing: boolean
  indexingInterval: number
  maxWorkers: number
  memoryLimit: number
  enableProfiling: boolean
  optimizationLevel: 'low' | 'medium' | 'high' | 'aggressive'
  backgroundProcessing: BackgroundProcessingSettings
}

export interface BackgroundProcessingSettings {
  enabled: boolean
  maxConcurrentTasks: number
  priorityQueue: boolean
  throttling: boolean
  batchSize: number
  debounceTime: number
}

export interface SecuritySettings {
  enableSecurityScans: boolean
  allowExternalConnections: boolean
  enableSSL: boolean
  certificateValidation: boolean
  apiKeyEncryption: boolean
  auditLogging: boolean
  dataRetention: number
  privacyMode: boolean
  trustedDomains: string[]
  blockedDomains: string[]
}

export interface AdvancedSettings {
  debugMode: boolean
  verboseLogging: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'trace'
  logRotation: boolean
  maxLogSize: number
  enableExperimentalFeatures: boolean
  featureFlags: Record<string, boolean>
  customExtensions: string[]
  developmentMode: boolean
  hotReload: boolean
}

export interface SettingsCategory {
  id: string
  name: string
  description: string
  icon: string
  sections: SettingsSection[]
  order: number
}

export interface SettingsSection {
  id: string
  name: string
  description: string
  fields: SettingsField[]
  collapsible: boolean
  collapsed: boolean
}

export interface SettingsField {
  id: string
  name: string
  description: string
  type: FieldType
  value: any
  defaultValue: any
  options?: FieldOption[]
  validation?: FieldValidation
  dependencies?: FieldDependency[]
  conditional?: FieldConditional
  readonly?: boolean
  hidden?: boolean
}

export interface FieldType {
  type: 'string' | 'number' | 'boolean' | 'enum' | 'array' | 'object' | 'color' | 'file' | 'directory' | 'keyBinding'
  format?: 'email' | 'url' | 'password' | 'json' | 'yaml' | 'regex'
  multiline?: boolean
  placeholder?: string
  min?: number
  max?: number
  step?: number
}

export interface FieldOption {
  label: string
  value: any
  description?: string
  icon?: string
  disabled?: boolean
}

export interface FieldValidation {
  required?: boolean
  pattern?: string
  min?: number
  max?: number
  custom?: string
  errorMessage?: string
}

export interface FieldDependency {
  field: string
  value: any
  operator: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in'
}

export interface FieldConditional {
  field: string
  value: any
  operator: 'eq' | 'neq' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in'
  action: 'show' | 'hide' | 'enable' | 'disable'
}

export interface SettingsValidationResult {
  valid: boolean
  errors: SettingsError[]
  warnings: SettingsWarning[]
}

export interface SettingsError {
  field: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

export interface SettingsWarning {
  field: string
  message: string
  suggestion: string
}

export interface SettingsImportExport {
  format: 'json' | 'yaml' | 'ini'
  includeSecrets: boolean
  includeUserData: boolean
  compression: boolean
  encryption: boolean
}

export class SettingsPanel {
  private panel: vscode.WebviewPanel | undefined
  private disposables: vscode.Disposable[] = []
  private currentSettings: SettingsConfiguration
  private settingsCategories: SettingsCategory[]

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService,
    private readonly preferenceTracker: PreferenceTracker
  ) {
    this.currentSettings = this.getDefaultSettings()
    this.settingsCategories = this.defineSettingsCategories()
  }

  public async show(): Promise<void> {
    if (this.panel) {
      this.panel.reveal()
      return
    }

    this.panel = vscode.window.createWebviewPanel(
      'settingsPanel',
      'Task Transformation Settings',
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.joinPath(this.context.extensionUri, 'media'),
          vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews')
        ]
      }
    )

    this.panel.webview.html = await this.getWebviewContent()
    this.panel.onDidDispose(() => this.panel = undefined, null, this.disposables)

    this.panel.webview.onDidReceiveMessage(
      async (message) => {
        switch (message.command) {
          case 'getSettings':
            await this.sendSettings()
            break
          case 'updateSetting':
            await this.updateSetting(message.field, message.value)
            break
          case 'validateSettings':
            await this.validateSettings(message.settings)
            break
          case 'resetSettings':
            await this.resetSettings(message.category)
            break
          case 'importSettings':
            await this.importSettings(message.data)
            break
          case 'exportSettings':
            await this.exportSettings(message.options)
            break
          case 'testConnection':
            await this.testConnection(message.provider, message.settings)
            break
          case 'refreshSettings':
            await this.refreshSettings()
            break
          case 'saveSettings':
            await this.saveSettings(message.settings)
            break
          case 'showTooltip':
            await this.showTooltip(message.field, message.content)
            break
          case 'openExternal':
            await vscode.env.openExternal(vscode.Uri.parse(message.url))
            break
        }
      },
      null,
      this.disposables
    )

    await this.loadSettings()
    await this.sendSettings()
  }

  private async getWebviewContent(): Promise<string> {
    const cssPath = this.panel!.webview.asWebviewUri(
      vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews', 'settings.css')
    )
    const scriptPath = this.panel!.webview.asWebviewUri(
      vscode.Uri.joinPath(this.context.extensionUri, 'src', 'ui', 'webviews', 'settings.js')
    )

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Transformation Settings</title>
    <link rel="stylesheet" href="${cssPath}">
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }

        .settings-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--vscode-widget-border);
        }

        .settings-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .settings-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid var(--vscode-button-border);
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .btn-primary {
            background-color: var(--vscode-button-primaryBackground);
            color: var(--vscode-button-primaryForeground);
        }

        .btn-primary:hover {
            background-color: var(--vscode-button-primaryHoverBackground);
        }

        .settings-sidebar {
            width: 250px;
            background-color: var(--vscode-sideBar-background);
            border: 1px solid var(--vscode-widget-border);
            border-radius: 6px;
            padding: 20px;
            position: fixed;
            height: calc(100vh - 120px);
            overflow-y: auto;
        }

        .settings-content {
            margin-left: 290px;
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-widget-border);
            border-radius: 6px;
            padding: 30px;
            min-height: calc(100vh - 120px);
        }

        .sidebar-category {
            margin-bottom: 15px;
            cursor: pointer;
            padding: 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .sidebar-category:hover {
            background-color: var(--vscode-list-hoverBackground);
        }

        .sidebar-category.active {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
        }

        .category-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .category-name {
            font-weight: 500;
        }

        .settings-section {
            margin-bottom: 40px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--vscode-widget-border);
            cursor: pointer;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .section-description {
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
            margin-top: 5px;
        }

        .section-toggle {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .section-content {
            display: grid;
            gap: 20px;
        }

        .section-content.collapsed {
            display: none;
        }

        .field-group {
            display: grid;
            gap: 10px;
        }

        .field-label {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .field-required {
            color: var(--vscode-errorForeground);
        }

        .field-description {
            color: var(--vscode-descriptionForeground);
            font-size: 13px;
            margin-bottom: 10px;
        }

        .field-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.2s;
        }

        .field-input:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }

        .field-input.error {
            border-color: var(--vscode-errorForeground);
        }

        .field-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .field-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border-radius: 4px;
            font-size: 13px;
        }

        .field-array {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .array-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border: 1px solid var(--vscode-widget-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
        }

        .array-item-input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            font-size: 13px;
        }

        .array-item-remove {
            padding: 4px 8px;
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: 1px solid var(--vscode-button-border);
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .array-item-remove:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .array-add {
            padding: 6px 12px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: 1px solid var(--vscode-button-border);
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            align-self: flex-start;
        }

        .array-add:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .field-error {
            color: var(--vscode-errorForeground);
            font-size: 12px;
            margin-top: 5px;
        }

        .field-warning {
            color: var(--vscode-warningForeground);
            font-size: 12px;
            margin-top: 5px;
        }

        .field-info {
            color: var(--vscode-descriptionForeground);
            font-size: 12px;
            margin-top: 5px;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip-icon {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }

        .tooltip-content {
            visibility: hidden;
            width: 300px;
            background-color: var(--vscode-editorHoverWidget-background);
            color: var(--vscode-editorHoverWidget-foreground);
            border: 1px solid var(--vscode-editorHoverWidget-border);
            text-align: left;
            border-radius: 4px;
            padding: 10px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip:hover .tooltip-content {
            visibility: visible;
            opacity: 1;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background-color: var(--vscode-charts-green);
        }

        .status-error {
            background-color: var(--vscode-errorForeground);
        }

        .status-warning {
            background-color: var(--vscode-warningForeground);
        }

        .status-info {
            background-color: var(--vscode-charts-blue);
        }

        .search-box {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-size: 13px;
            margin-bottom: 15px;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100px;
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
        }

        .spinner {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .settings-sidebar {
                position: static;
                width: 100%;
                height: auto;
                margin-bottom: 20px;
            }

            .settings-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <div class="settings-header">
            <h1 class="settings-title">Task Transformation Settings</h1>
            <div class="settings-actions">
                <button class="btn" id="resetBtn">Reset to Defaults</button>
                <button class="btn" id="importBtn">Import</button>
                <button class="btn" id="exportBtn">Export</button>
                <button class="btn btn-primary" id="saveBtn">Save Settings</button>
            </div>
        </div>

        <div class="settings-sidebar">
            <input type="text" class="search-box" placeholder="Search settings..." id="searchBox">
            <div id="categoriesList"></div>
        </div>

        <div class="settings-content">
            <div id="settingsContent">
                <div class="loading">
                    <div class="spinner">⟳</div>
                    Loading settings...
                </div>
            </div>
        </div>
    </div>

    <script src="${scriptPath}"></script>
    <script>
        const vscode = acquireVsCodeApi();
        let currentSettings = {};
        let settingsCategories = [];
        let activeCategory = 'general';
        let searchQuery = '';

        // Initialize
        window.addEventListener('load', () => {
            vscode.postMessage({ command: 'getSettings' });
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchBox').addEventListener('input', (e) => {
                searchQuery = e.target.value.toLowerCase();
                filterSettings();
            });

            document.getElementById('saveBtn').addEventListener('click', saveSettings);
            document.getElementById('resetBtn').addEventListener('click', resetSettings);
            document.getElementById('importBtn').addEventListener('click', importSettings);
            document.getElementById('exportBtn').addEventListener('click', exportSettings);
        }

        window.addEventListener('message', (event) => {
            const message = event.data;
            
            switch (message.command) {
                case 'settingsLoaded':
                    currentSettings = message.settings;
                    settingsCategories = message.categories;
                    renderSettings();
                    break;
                case 'settingUpdated':
                    updateSettingValue(message.field, message.value);
                    break;
                case 'validationResult':
                    handleValidationResult(message.result);
                    break;
                case 'connectionTestResult':
                    handleConnectionTestResult(message.result);
                    break;
            }
        });

        function renderSettings() {
            renderCategories();
            renderActiveCategory();
        }

        function renderCategories() {
            const categoriesList = document.getElementById('categoriesList');
            categoriesList.innerHTML = '';

            settingsCategories.forEach(category => {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'sidebar-category' + (category.id === activeCategory ? ' active' : '');
                categoryElement.innerHTML = \`
                    <span class="category-icon">\${category.icon}</span>
                    <span class="category-name">\${category.name}</span>
                \`;
                categoryElement.addEventListener('click', () => {
                    activeCategory = category.id;
                    renderSettings();
                });
                categoriesList.appendChild(categoryElement);
            });
        }

        function renderActiveCategory() {
            const settingsContent = document.getElementById('settingsContent');
            const category = settingsCategories.find(c => c.id === activeCategory);
            
            if (!category) return;

            settingsContent.innerHTML = \`
                <h2>\${category.name}</h2>
                <p class="section-description">\${category.description}</p>
                \${category.sections.map(section => renderSection(section)).join('')}
            \`;
        }

        function renderSection(section) {
            const filteredFields = section.fields.filter(field => 
                !searchQuery || 
                field.name.toLowerCase().includes(searchQuery) ||
                field.description.toLowerCase().includes(searchQuery)
            );

            if (filteredFields.length === 0 && searchQuery) {
                return '';
            }

            return \`
                <div class="settings-section">
                    <div class="section-header" onclick="toggleSection('\${section.id}')">
                        <div>
                            <h3 class="section-title">\${section.name}</h3>
                            <p class="section-description">\${section.description}</p>
                        </div>
                        <span class="section-toggle" id="toggle-\${section.id}">
                            \${section.collapsed ? '▶' : '▼'}
                        </span>
                    </div>
                    <div class="section-content\${section.collapsed ? ' collapsed' : ''}" id="content-\${section.id}">
                        \${filteredFields.map(field => renderField(field)).join('')}
                    </div>
                </div>
            \`;
        }

        function renderField(field) {
            const value = getFieldValue(field.id);
            const isDisabled = field.readonly || field.hidden;
            const hasError = field.validation && !validateField(field, value);

            return \`
                <div class="field-group\${field.hidden ? ' hidden' : ''}">
                    <div class="field-label">
                        \${field.name}
                        \${field.validation?.required ? '<span class="field-required">*</span>' : ''}
                        \${field.description ? \`
                            <div class="tooltip">
                                <span class="tooltip-icon">ℹ</span>
                                <div class="tooltip-content">\${field.description}</div>
                            </div>
                        \` : ''}
                    </div>
                    \${renderFieldInput(field, value, isDisabled, hasError)}
                    \${hasError ? \`<div class="field-error">Invalid value</div>\` : ''}
                </div>
            \`;
        }

        function renderFieldInput(field, value, isDisabled, hasError) {
            const commonProps = \`
                id="\${field.id}"
                \${isDisabled ? 'disabled' : ''}
                class="field-input\${hasError ? ' error' : ''}"
            \`;

            switch (field.type.type) {
                case 'string':
                    return field.type.multiline ? 
                        \`<textarea \${commonProps} rows="4">\${value || ''}</textarea>\` :
                        \`<input type="text" \${commonProps} value="\${value || ''}" placeholder="\${field.type.placeholder || ''}">`;
                case 'number':
                    return \`<input type="number" \${commonProps} value="\${value || ''}" 
                           min="\${field.type.min || ''}" max="\${field.type.max || ''}" 
                           step="\${field.type.step || 1}">\`;
                case 'boolean':
                    return \`
                        <div class="field-checkbox">
                            <input type="checkbox" \${commonProps} \${value ? 'checked' : ''}>
                            <label for="\${field.id}">\${field.name}</label>
                        </div>
                    \`;
                case 'enum':
                    return \`
                        <select \${commonProps.replace('field-input', 'field-select')}>
                            \${field.options?.map(option => \`
                                <option value="\${option.value}" \${option.value === value ? 'selected' : ''}>
                                    \${option.label}
                                </option>
                            \`).join('') || ''}
                        </select>
                    \`;
                case 'array':
                    return renderArrayField(field, value || []);
                case 'color':
                    return \`<input type="color" \${commonProps} value="\${value || '#000000'}">\`;
                case 'file':
                    return \`<input type="file" \${commonProps}>\`;
                default:
                    return \`<input type="text" \${commonProps} value="\${value || ''}">\`;
            }
        }

        function renderArrayField(field, values) {
            return \`
                <div class="field-array">
                    \${values.map((value, index) => \`
                        <div class="array-item">
                            <input type="text" class="array-item-input" 
                                   value="\${value}" 
                                   onchange="updateArrayItem('\${field.id}', \${index}, this.value)">
                            <button class="array-item-remove" 
                                    onclick="removeArrayItem('\${field.id}', \${index})">Remove</button>
                        </div>
                    \`).join('')}
                    <button class="array-add" onclick="addArrayItem('\${field.id}')">Add Item</button>
                </div>
            \`;
        }

        function getFieldValue(fieldId) {
            const parts = fieldId.split('.');
            let value = currentSettings;
            for (const part of parts) {
                value = value?.[part];
            }
            return value;
        }

        function updateSettingValue(field, value) {
            const parts = field.split('.');
            let current = currentSettings;
            for (let i = 0; i < parts.length - 1; i++) {
                if (!current[parts[i]]) {
                    current[parts[i]] = {};
                }
                current = current[parts[i]];
            }
            current[parts[parts.length - 1]] = value;
            
            vscode.postMessage({ command: 'updateSetting', field, value });
        }

        function validateField(field, value) {
            if (field.validation?.required && (!value || value === '')) {
                return false;
            }
            if (field.validation?.pattern && !new RegExp(field.validation.pattern).test(value)) {
                return false;
            }
            if (field.validation?.min !== undefined && value < field.validation.min) {
                return false;
            }
            if (field.validation?.max !== undefined && value > field.validation.max) {
                return false;
            }
            return true;
        }

        function toggleSection(sectionId) {
            const content = document.getElementById(\`content-\${sectionId}\`);
            const toggle = document.getElementById(\`toggle-\${sectionId}\`);
            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                toggle.textContent = '▼';
            } else {
                content.classList.add('collapsed');
                toggle.textContent = '▶';
            }
        }

        function filterSettings() {
            renderActiveCategory();
        }

        function saveSettings() {
            vscode.postMessage({ command: 'saveSettings', settings: currentSettings });
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                vscode.postMessage({ command: 'resetSettings', category: activeCategory });
            }
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json,.yaml,.yml';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            vscode.postMessage({ command: 'importSettings', data });
                        } catch (error) {
                            alert('Invalid settings file');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportSettings() {
            vscode.postMessage({ command: 'exportSettings', options: { format: 'json' } });
        }

        function updateArrayItem(fieldId, index, value) {
            const currentValue = getFieldValue(fieldId) || [];
            currentValue[index] = value;
            updateSettingValue(fieldId, currentValue);
        }

        function removeArrayItem(fieldId, index) {
            const currentValue = getFieldValue(fieldId) || [];
            currentValue.splice(index, 1);
            updateSettingValue(fieldId, currentValue);
            renderActiveCategory();
        }

        function addArrayItem(fieldId) {
            const currentValue = getFieldValue(fieldId) || [];
            currentValue.push('');
            updateSettingValue(fieldId, currentValue);
            renderActiveCategory();
        }

        function handleValidationResult(result) {
            // Handle validation results
            console.log('Validation result:', result);
        }

        function handleConnectionTestResult(result) {
            // Handle connection test results
            console.log('Connection test result:', result);
        }
    </script>
</body>
</html>
    `
  }

  private getDefaultSettings(): SettingsConfiguration {
    return {
      general: {
        autoSave: true,
        autoUpdate: true,
        enableTelemetry: true,
        enableNotifications: true,
        defaultLanguage: 'typescript',
        defaultFramework: 'unknown',
        workspaceIntegration: true,
        gitIntegration: true,
        keyboardShortcuts: {
          transformText: 'Ctrl+Shift+T',
          suggestImprovements: 'Ctrl+Shift+S',
          selectTemplate: 'Ctrl+Shift+P',
          browseTemplates: 'Ctrl+Shift+B',
          createTemplate: 'Ctrl+Shift+C',
          quickTransform: 'Ctrl+Shift+Q',
          showHistory: 'Ctrl+Shift+H',
          toggleSidebar: 'Ctrl+Shift+E'
        },
        accessibility: {
          highContrast: false,
          largeText: false,
          screenReader: false,
          reducedMotion: false,
          keyboardNavigation: true,
          announcements: true
        }
      },
      ai: {
        provider: 'openai',
        model: 'gpt-4',
        apiKey: '',
        apiUrl: '',
        temperature: 0.7,
        maxTokens: 2000,
        timeoutSeconds: 30,
        retryAttempts: 3,
        enableCaching: true,
        enableStreaming: false,
        customHeaders: {},
        rateLimiting: {
          enabled: true,
          requestsPerMinute: 60,
          tokensPerMinute: 10000,
          burstLimit: 10,
          backoffStrategy: 'exponential'
        }
      },
      templates: {
        autoComplete: true,
        showPreviews: true,
        enableSharing: false,
        syncSettings: true,
        defaultCategory: 'general',
        sortBy: 'name',
        viewMode: 'grid',
        maxRecentTemplates: 10,
        backupFrequency: 'weekly',
        compressionLevel: 6,
        validation: {
          enableValidation: true,
          strictMode: false,
          customRules: [],
          autoFix: false,
          showWarnings: true
        }
      },
      suggestions: {
        enableAI: true,
        enableLearning: true,
        enableSemanticSearch: true,
        maxSuggestions: 10,
        confidenceThreshold: 0.7,
        includeExplanations: true,
        includeExamples: true,
        autoApply: false,
        categories: [
          { name: 'Refactoring', enabled: true, priority: 8, color: '#007ACC', icon: '🔄' },
          { name: 'Testing', enabled: true, priority: 7, color: '#28A745', icon: '🧪' },
          { name: 'Documentation', enabled: true, priority: 6, color: '#6F42C1', icon: '📚' },
          { name: 'Performance', enabled: true, priority: 9, color: '#FD7E14', icon: '⚡' },
          { name: 'Security', enabled: true, priority: 10, color: '#DC3545', icon: '🔒' }
        ],
        filters: [],
        prioritization: {
          primaryFactor: 'relevance',
          secondaryFactor: 'confidence',
          userHistoryWeight: 0.3,
          contextWeight: 0.4,
          popularityWeight: 0.3
        }
      },
      ui: {
        theme: 'system',
        fontSize: 13,
        fontFamily: 'var(--vscode-font-family)',
        lineHeight: 1.6,
        colorScheme: 'default',
        animations: true,
        transitions: true,
        layout: {
          sidebarPosition: 'left',
          panelPosition: 'bottom',
          activityBarPosition: 'left',
          minimap: true,
          breadcrumbs: true,
          tabs: true,
          indentGuides: true
        },
        panels: {
          showWelcome: true,
          showTips: true,
          showMetrics: true,
          showHistory: true,
          defaultPanel: 'welcome',
          collapsible: true,
          resizable: true
        },
        statusBar: {
          showStatus: true,
          showProgress: true,
          showMetrics: true,
          showErrors: true,
          position: 'left',
          priority: 100
        }
      },
      performance: {
        enableCaching: true,
        cacheSize: 100,
        cacheExpiration: 3600,
        enableIndexing: true,
        indexingInterval: 30,
        maxWorkers: 4,
        memoryLimit: 512,
        enableProfiling: false,
        optimizationLevel: 'medium',
        backgroundProcessing: {
          enabled: true,
          maxConcurrentTasks: 3,
          priorityQueue: true,
          throttling: true,
          batchSize: 10,
          debounceTime: 300
        }
      },
      security: {
        enableSecurityScans: true,
        allowExternalConnections: true,
        enableSSL: true,
        certificateValidation: true,
        apiKeyEncryption: true,
        auditLogging: true,
        dataRetention: 30,
        privacyMode: false,
        trustedDomains: [],
        blockedDomains: []
      },
      advanced: {
        debugMode: false,
        verboseLogging: false,
        logLevel: 'info',
        logRotation: true,
        maxLogSize: 10,
        enableExperimentalFeatures: false,
        featureFlags: {},
        customExtensions: [],
        developmentMode: false,
        hotReload: false
      }
    }
  }

  private defineSettingsCategories(): SettingsCategory[] {
    return [
      {
        id: 'general',
        name: 'General',
        description: 'General extension settings and preferences',
        icon: '⚙️',
        order: 1,
        sections: [
          {
            id: 'basic',
            name: 'Basic Settings',
            description: 'Fundamental extension configuration',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'general.autoSave',
                name: 'Auto Save',
                description: 'Automatically save settings when changed',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'general.autoUpdate',
                name: 'Auto Update',
                description: 'Automatically update templates and models',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'general.defaultLanguage',
                name: 'Default Language',
                description: 'Default programming language for new documents',
                type: { type: 'enum' },
                value: 'typescript',
                defaultValue: 'typescript',
                options: [
                  { label: 'TypeScript', value: 'typescript' },
                  { label: 'JavaScript', value: 'javascript' },
                  { label: 'Python', value: 'python' },
                  { label: 'Java', value: 'java' },
                  { label: 'C#', value: 'csharp' },
                  { label: 'Go', value: 'go' },
                  { label: 'Rust', value: 'rust' }
                ]
              }
            ]
          },
          {
            id: 'shortcuts',
            name: 'Keyboard Shortcuts',
            description: 'Customize keyboard shortcuts',
            collapsible: true,
            collapsed: false,
            fields: [
              {
                id: 'general.keyboardShortcuts.transformText',
                name: 'Transform Text',
                description: 'Shortcut to transform selected text',
                type: { type: 'keyBinding' },
                value: 'Ctrl+Shift+T',
                defaultValue: 'Ctrl+Shift+T'
              },
              {
                id: 'general.keyboardShortcuts.suggestImprovements',
                name: 'Suggest Improvements',
                description: 'Shortcut to suggest code improvements',
                type: { type: 'keyBinding' },
                value: 'Ctrl+Shift+S',
                defaultValue: 'Ctrl+Shift+S'
              }
            ]
          }
        ]
      },
      {
        id: 'ai',
        name: 'AI Configuration',
        description: 'AI provider and model settings',
        icon: '🤖',
        order: 2,
        sections: [
          {
            id: 'provider',
            name: 'AI Provider',
            description: 'Configure AI service provider',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'ai.provider',
                name: 'Provider',
                description: 'AI service provider',
                type: { type: 'enum' },
                value: 'openai',
                defaultValue: 'openai',
                options: [
                  { label: 'OpenAI', value: 'openai' },
                  { label: 'Claude', value: 'claude' },
                  { label: 'Custom', value: 'custom' }
                ]
              },
              {
                id: 'ai.model',
                name: 'Model',
                description: 'AI model to use',
                type: { type: 'string' },
                value: 'gpt-4',
                defaultValue: 'gpt-4'
              },
              {
                id: 'ai.apiKey',
                name: 'API Key',
                description: 'API key for the AI provider',
                type: { type: 'string', format: 'password' },
                value: '',
                defaultValue: '',
                validation: { required: true }
              }
            ]
          }
        ]
      },
      {
        id: 'templates',
        name: 'Templates',
        description: 'Template management and configuration',
        icon: '📝',
        order: 3,
        sections: [
          {
            id: 'behavior',
            name: 'Template Behavior',
            description: 'How templates work and display',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'templates.autoComplete',
                name: 'Auto Complete',
                description: 'Enable template auto-completion',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'templates.showPreviews',
                name: 'Show Previews',
                description: 'Show template previews in browser',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'templates.viewMode',
                name: 'View Mode',
                description: 'How to display templates',
                type: { type: 'enum' },
                value: 'grid',
                defaultValue: 'grid',
                options: [
                  { label: 'Grid', value: 'grid' },
                  { label: 'List', value: 'list' },
                  { label: 'Detailed', value: 'detailed' }
                ]
              }
            ]
          }
        ]
      },
      {
        id: 'suggestions',
        name: 'Suggestions',
        description: 'Code suggestion and improvement settings',
        icon: '💡',
        order: 4,
        sections: [
          {
            id: 'behavior',
            name: 'Suggestion Behavior',
            description: 'How suggestions are generated and displayed',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'suggestions.enableAI',
                name: 'Enable AI Suggestions',
                description: 'Use AI to generate code suggestions',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'suggestions.maxSuggestions',
                name: 'Max Suggestions',
                description: 'Maximum number of suggestions to show',
                type: { type: 'number', min: 1, max: 20 },
                value: 10,
                defaultValue: 10
              },
              {
                id: 'suggestions.confidenceThreshold',
                name: 'Confidence Threshold',
                description: 'Minimum confidence level for suggestions',
                type: { type: 'number', min: 0, max: 1, step: 0.1 },
                value: 0.7,
                defaultValue: 0.7
              }
            ]
          }
        ]
      },
      {
        id: 'ui',
        name: 'User Interface',
        description: 'UI appearance and behavior settings',
        icon: '🎨',
        order: 5,
        sections: [
          {
            id: 'appearance',
            name: 'Appearance',
            description: 'Visual appearance settings',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'ui.theme',
                name: 'Theme',
                description: 'UI theme preference',
                type: { type: 'enum' },
                value: 'system',
                defaultValue: 'system',
                options: [
                  { label: 'System', value: 'system' },
                  { label: 'Light', value: 'light' },
                  { label: 'Dark', value: 'dark' },
                  { label: 'High Contrast', value: 'high-contrast' }
                ]
              },
              {
                id: 'ui.fontSize',
                name: 'Font Size',
                description: 'Font size in pixels',
                type: { type: 'number', min: 10, max: 24 },
                value: 13,
                defaultValue: 13
              },
              {
                id: 'ui.animations',
                name: 'Enable Animations',
                description: 'Enable UI animations and transitions',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              }
            ]
          }
        ]
      },
      {
        id: 'performance',
        name: 'Performance',
        description: 'Performance and resource settings',
        icon: '⚡',
        order: 6,
        sections: [
          {
            id: 'caching',
            name: 'Caching',
            description: 'Cache settings for better performance',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'performance.enableCaching',
                name: 'Enable Caching',
                description: 'Cache results for faster performance',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'performance.cacheSize',
                name: 'Cache Size',
                description: 'Maximum cache size in MB',
                type: { type: 'number', min: 10, max: 1000 },
                value: 100,
                defaultValue: 100
              },
              {
                id: 'performance.maxWorkers',
                name: 'Max Workers',
                description: 'Maximum number of background workers',
                type: { type: 'number', min: 1, max: 16 },
                value: 4,
                defaultValue: 4
              }
            ]
          }
        ]
      },
      {
        id: 'security',
        name: 'Security',
        description: 'Security and privacy settings',
        icon: '🔒',
        order: 7,
        sections: [
          {
            id: 'privacy',
            name: 'Privacy',
            description: 'Privacy and data protection settings',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'security.enableSecurityScans',
                name: 'Enable Security Scans',
                description: 'Scan code for security vulnerabilities',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'security.apiKeyEncryption',
                name: 'Encrypt API Keys',
                description: 'Encrypt API keys in storage',
                type: { type: 'boolean' },
                value: true,
                defaultValue: true
              },
              {
                id: 'security.privacyMode',
                name: 'Privacy Mode',
                description: 'Enable privacy mode (no telemetry)',
                type: { type: 'boolean' },
                value: false,
                defaultValue: false
              }
            ]
          }
        ]
      },
      {
        id: 'advanced',
        name: 'Advanced',
        description: 'Advanced settings for power users',
        icon: '🔧',
        order: 8,
        sections: [
          {
            id: 'debug',
            name: 'Debug',
            description: 'Debug and development settings',
            collapsible: false,
            collapsed: false,
            fields: [
              {
                id: 'advanced.debugMode',
                name: 'Debug Mode',
                description: 'Enable debug mode for troubleshooting',
                type: { type: 'boolean' },
                value: false,
                defaultValue: false
              },
              {
                id: 'advanced.logLevel',
                name: 'Log Level',
                description: 'Logging verbosity level',
                type: { type: 'enum' },
                value: 'info',
                defaultValue: 'info',
                options: [
                  { label: 'Error', value: 'error' },
                  { label: 'Warning', value: 'warn' },
                  { label: 'Info', value: 'info' },
                  { label: 'Debug', value: 'debug' },
                  { label: 'Trace', value: 'trace' }
                ]
              },
              {
                id: 'advanced.enableExperimentalFeatures',
                name: 'Experimental Features',
                description: 'Enable experimental features (may be unstable)',
                type: { type: 'boolean' },
                value: false,
                defaultValue: false
              }
            ]
          }
        ]
      }
    ]
  }

  private async loadSettings(): Promise<void> {
    try {
      const stored = await this.storageService.get<SettingsConfiguration>('settings')
      if (stored) {
        this.currentSettings = { ...this.currentSettings, ...stored }
      }
    } catch (error) {
      this.logger.error('Failed to load settings:', error)
    }
  }

  private async sendSettings(): Promise<void> {
    if (this.panel) {
      this.panel.webview.postMessage({
        command: 'settingsLoaded',
        settings: this.currentSettings,
        categories: this.settingsCategories
      })
    }
  }

  private async updateSetting(field: string, value: any): Promise<void> {
    try {
      const parts = field.split('.')
      let current = this.currentSettings as any
      
      for (let i = 0; i < parts.length - 1; i++) {
        if (!current[parts[i]]) {
          current[parts[i]] = {}
        }
        current = current[parts[i]]
      }
      
      current[parts[parts.length - 1]] = value
      
      await this.storageService.set('settings', this.currentSettings)
      
      if (this.panel) {
        this.panel.webview.postMessage({
          command: 'settingUpdated',
          field,
          value
        })
      }
      
      this.logger.info(`Setting updated: ${field} = ${value}`)
    } catch (error) {
      this.logger.error('Failed to update setting:', error)
    }
  }

  private async validateSettings(settings: SettingsConfiguration): Promise<void> {
    const errors: SettingsError[] = []
    const warnings: SettingsWarning[] = []

    // Validate AI settings
    if (settings.ai.provider === 'openai' && !settings.ai.apiKey) {
      errors.push({
        field: 'ai.apiKey',
        message: 'API key is required for OpenAI provider',
        severity: 'error'
      })
    }

    if (settings.ai.maxTokens < 100 || settings.ai.maxTokens > 4000) {
      warnings.push({
        field: 'ai.maxTokens',
        message: 'Max tokens should be between 100 and 4000',
        suggestion: 'Consider using a value between 1000-2000 for best results'
      })
    }

    // Validate performance settings
    if (settings.performance.memoryLimit < 256) {
      warnings.push({
        field: 'performance.memoryLimit',
        message: 'Memory limit is quite low',
        suggestion: 'Consider increasing to at least 512MB for better performance'
      })
    }

    const result: SettingsValidationResult = {
      valid: errors.length === 0,
      errors,
      warnings
    }

    if (this.panel) {
      this.panel.webview.postMessage({
        command: 'validationResult',
        result
      })
    }
  }

  private async resetSettings(category?: string): Promise<void> {
    try {
      const defaults = this.getDefaultSettings()
      
      if (category) {
        (this.currentSettings as any)[category] = (defaults as any)[category]
      } else {
        this.currentSettings = defaults
      }
      
      await this.storageService.set('settings', this.currentSettings)
      await this.sendSettings()
      
      this.logger.info(`Settings reset: ${category || 'all'}`)
    } catch (error) {
      this.logger.error('Failed to reset settings:', error)
    }
  }

  private async importSettings(data: any): Promise<void> {
    try {
      this.currentSettings = { ...this.currentSettings, ...data }
      await this.storageService.set('settings', this.currentSettings)
      await this.sendSettings()
      
      this.logger.info('Settings imported successfully')
    } catch (error) {
      this.logger.error('Failed to import settings:', error)
    }
  }

  private async exportSettings(options: SettingsImportExport): Promise<void> {
    try {
      const data = options.format === 'json' 
        ? JSON.stringify(this.currentSettings, null, 2)
        : this.currentSettings // Would implement YAML/INI serialization
      
      const blob = new Blob([data], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `settings.${options.format}`
      a.click()
      
      URL.revokeObjectURL(url)
      
      this.logger.info('Settings exported successfully')
    } catch (error) {
      this.logger.error('Failed to export settings:', error)
    }
  }

  private async testConnection(provider: string, settings: any): Promise<void> {
    try {
      // Would implement actual connection testing
      const result = {
        success: true,
        message: 'Connection test successful',
        latency: 150
      }
      
      if (this.panel) {
        this.panel.webview.postMessage({
          command: 'connectionTestResult',
          result
        })
      }
    } catch (error) {
      this.logger.error('Connection test failed:', error)
      
      if (this.panel) {
        this.panel.webview.postMessage({
          command: 'connectionTestResult',
          result: {
            success: false,
            message: `Connection test failed: ${error}`,
            latency: 0
          }
        })
      }
    }
  }

  private async saveSettings(settings: SettingsConfiguration): Promise<void> {
    try {
      this.currentSettings = settings
      await this.storageService.set('settings', this.currentSettings)
      await this.preferenceTracker.updatePreferences(settings)
      
      this.logger.info('Settings saved successfully')
    } catch (error) {
      this.logger.error('Failed to save settings:', error)
    }
  }

  private async refreshSettings(): Promise<void> {
    await this.loadSettings()
    await this.sendSettings()
  }

  private async showTooltip(field: string, content: string): Promise<void> {
    // Would implement tooltip display
    this.logger.info(`Tooltip for ${field}: ${content}`)
  }

  public getCurrentSettings(): SettingsConfiguration {
    return this.currentSettings
  }

  public async updateSettings(settings: Partial<SettingsConfiguration>): Promise<void> {
    this.currentSettings = { ...this.currentSettings, ...settings }
    await this.storageService.set('settings', this.currentSettings)
    await this.sendSettings()
  }

  public dispose(): void {
    this.disposables.forEach(d => d.dispose())
    this.panel?.dispose()
  }
}