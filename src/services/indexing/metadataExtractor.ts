import * as crypto from 'crypto'
import { FileSystemService } from '../fileSystemService'
import { ProjectFile } from '../projectScanner'

export interface FileMetadata {
  path: string
  relativePath: string
  name: string
  extension: string
  size: number
  modifiedTime: Date
  createdTime: Date
  language?: string
  isText: boolean
  contentHash: string
  lineCount: number
  characterCount: number
  encoding: string
  permissions?: string
  isSymlink: boolean
  isBinary: boolean
}

export interface ContentMetadata {
  lineCount: number
  characterCount: number
  wordCount: number
  encoding: string
  hasUnicode: boolean
  isEmpty: boolean
  isMinified: boolean
  averageLineLength: number
  maxLineLength: number
  blankLines: number
  commentLines: number
  codeLines: number
}

export interface ImportExportMetadata {
  imports: ImportInfo[]
  exports: ExportInfo[]
  dependencies: string[]
  internalDependencies: string[]
  externalDependencies: string[]
}

export interface ImportInfo {
  module: string
  importType: 'import' | 'require' | 'dynamic'
  importedNames: string[]
  defaultImport?: string
  namespaceImport?: string
  lineNumber: number
  isLocal: boolean
}

export interface ExportInfo {
  name: string
  exportType: 'named' | 'default' | 'namespace'
  lineNumber: number
  isReexport: boolean
  originalModule?: string
}

export class MetadataExtractor {
  private fileSystem: FileSystemService

  constructor(fileSystem: FileSystemService) {
    this.fileSystem = fileSystem
  }

  async extractFileMetadata(filePath: string, projectRoot: string): Promise<FileMetadata> {
    const stats = await this.fileSystem.stat(filePath)
    const relativePath = this.fileSystem.relative(projectRoot, filePath)
    const name = this.fileSystem.basename(filePath)
    const extension = this.fileSystem.extname(filePath)
    
    const isText = this.isTextFile(extension)
    let contentHash = ''
    let lineCount = 0
    let characterCount = 0
    let encoding = 'utf-8'
    let isBinary = false

    try {
      if (isText && stats.size < 10 * 1024 * 1024) { // Only process text files under 10MB
        const content = await this.fileSystem.readFile(filePath)
        contentHash = this.calculateContentHash(content)
        const contentMetadata = this.extractContentMetadata(content)
        lineCount = contentMetadata.lineCount
        characterCount = contentMetadata.characterCount
        encoding = contentMetadata.encoding
        isBinary = false
      } else {
        const buffer = await this.fileSystem.readFile(filePath)
        contentHash = this.calculateContentHash(buffer)
        isBinary = this.isBinaryFile(buffer)
        characterCount = buffer.length
      }
    } catch (error) {
      // Handle read errors gracefully
      contentHash = `error:${error}`
    }

    return {
      path: filePath,
      relativePath,
      name,
      extension,
      size: stats.size,
      modifiedTime: stats.mtime,
      createdTime: stats.birthtime || stats.mtime,
      language: this.detectLanguage(extension),
      isText,
      contentHash,
      lineCount,
      characterCount,
      encoding,
      isSymlink: stats.isSymbolicLink(),
      isBinary
    }
  }

  extractContentMetadata(content: string): ContentMetadata {
    const lines = content.split('\n')
    const lineCount = lines.length
    const characterCount = content.length
    const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
    
    const hasUnicode = /[^\x00-\x7F]/.test(content)
    const isEmpty = content.trim().length === 0
    
    const averageLineLength = lineCount > 0 ? characterCount / lineCount : 0
    const maxLineLength = Math.max(...lines.map(line => line.length))
    
    const blankLines = lines.filter(line => line.trim().length === 0).length
    const commentLines = this.countCommentLines(lines)
    const codeLines = lineCount - blankLines - commentLines
    
    const isMinified = this.isMinified(content, averageLineLength, maxLineLength)

    return {
      lineCount,
      characterCount,
      wordCount,
      encoding: 'utf-8',
      hasUnicode,
      isEmpty,
      isMinified,
      averageLineLength,
      maxLineLength,
      blankLines,
      commentLines,
      codeLines
    }
  }

  async extractImportExportMetadata(filePath: string, content: string): Promise<ImportExportMetadata> {
    const extension = this.fileSystem.extname(filePath)
    const language = this.detectLanguage(extension)

    if (!language) {
      return {
        imports: [],
        exports: [],
        dependencies: [],
        internalDependencies: [],
        externalDependencies: []
      }
    }

    switch (language) {
      case 'javascript':
      case 'typescript':
        return this.extractJSImportExports(content)
      case 'python':
        return this.extractPythonImportExports(content)
      default:
        return {
          imports: [],
          exports: [],
          dependencies: [],
          internalDependencies: [],
          externalDependencies: []
        }
    }
  }

  private extractJSImportExports(content: string): ImportExportMetadata {
    const imports: ImportInfo[] = []
    const exports: ExportInfo[] = []
    const lines = content.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      const lineNumber = i + 1

      // Import statements
      const importMatch = line.match(/^import\s+(.+?)\s+from\s+['"](.+?)['"]/)
      if (importMatch) {
        const [, importClause, module] = importMatch
        const importedNames = this.parseImportClause(importClause)
        imports.push({
          module,
          importType: 'import',
          importedNames: importedNames.named,
          defaultImport: importedNames.default,
          namespaceImport: importedNames.namespace,
          lineNumber,
          isLocal: this.isLocalModule(module)
        })
      }

      // Require statements
      const requireMatch = line.match(/(?:const|let|var)\s+(.+?)\s*=\s*require\(['"](.+?)['"]\)/)
      if (requireMatch) {
        const [, importClause, module] = requireMatch
        imports.push({
          module,
          importType: 'require',
          importedNames: [importClause.replace(/[{}]/g, '').trim()],
          lineNumber,
          isLocal: this.isLocalModule(module)
        })
      }

      // Export statements
      const exportMatch = line.match(/^export\s+(.+)/)
      if (exportMatch) {
        const exportClause = exportMatch[1]
        if (exportClause.startsWith('default')) {
          exports.push({
            name: 'default',
            exportType: 'default',
            lineNumber,
            isReexport: false
          })
        } else if (exportClause.startsWith('{')) {
          const namedExports = this.parseExportClause(exportClause)
          namedExports.forEach(name => {
            exports.push({
              name,
              exportType: 'named',
              lineNumber,
              isReexport: false
            })
          })
        }
      }
    }

    const dependencies = [...new Set(imports.map(imp => imp.module))]
    const internalDependencies = dependencies.filter(dep => this.isLocalModule(dep))
    const externalDependencies = dependencies.filter(dep => !this.isLocalModule(dep))

    return {
      imports,
      exports,
      dependencies,
      internalDependencies,
      externalDependencies
    }
  }

  private extractPythonImportExports(content: string): ImportExportMetadata {
    const imports: ImportInfo[] = []
    const exports: ExportInfo[] = []
    const lines = content.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      const lineNumber = i + 1

      // Import statements
      const importMatch = line.match(/^import\s+(.+)/)
      if (importMatch) {
        const modules = importMatch[1].split(',').map(m => m.trim())
        modules.forEach(module => {
          imports.push({
            module,
            importType: 'import',
            importedNames: [module],
            lineNumber,
            isLocal: this.isLocalModule(module)
          })
        })
      }

      // From import statements
      const fromImportMatch = line.match(/^from\s+(.+?)\s+import\s+(.+)/)
      if (fromImportMatch) {
        const [, module, importClause] = fromImportMatch
        const importedNames = importClause.split(',').map(name => name.trim())
        imports.push({
          module,
          importType: 'import',
          importedNames,
          lineNumber,
          isLocal: this.isLocalModule(module)
        })
      }
    }

    const dependencies = [...new Set(imports.map(imp => imp.module))]
    const internalDependencies = dependencies.filter(dep => this.isLocalModule(dep))
    const externalDependencies = dependencies.filter(dep => !this.isLocalModule(dep))

    return {
      imports,
      exports,
      dependencies,
      internalDependencies,
      externalDependencies
    }
  }

  private parseImportClause(clause: string): { named: string[], default?: string, namespace?: string } {
    const result: { named: string[], default?: string, namespace?: string } = { named: [] }
    
    if (clause.includes('* as ')) {
      const namespaceMatch = clause.match(/\*\s+as\s+(\w+)/)
      if (namespaceMatch) {
        result.namespace = namespaceMatch[1]
      }
    } else if (clause.includes('{')) {
      const namedMatch = clause.match(/\{([^}]+)\}/)
      if (namedMatch) {
        result.named = namedMatch[1].split(',').map(name => name.trim())
      }
    } else {
      result.default = clause.trim()
    }

    return result
  }

  private parseExportClause(clause: string): string[] {
    const namedMatch = clause.match(/\{([^}]+)\}/)
    if (namedMatch) {
      return namedMatch[1].split(',').map(name => name.trim())
    }
    return []
  }

  private isLocalModule(module: string): boolean {
    return module.startsWith('./') || module.startsWith('../') || module.startsWith('/')
  }

  private detectLanguage(extension: string): string | undefined {
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.c': 'c',
      '.cpp': 'cpp',
      '.cs': 'csharp',
      '.php': 'php',
      '.rb': 'ruby',
      '.go': 'go',
      '.rs': 'rust',
      '.swift': 'swift',
      '.kt': 'kotlin',
      '.scala': 'scala',
      '.sh': 'shell',
      '.ps1': 'powershell',
      '.html': 'html',
      '.css': 'css',
      '.scss': 'scss',
      '.json': 'json',
      '.xml': 'xml',
      '.yaml': 'yaml',
      '.yml': 'yaml',
      '.md': 'markdown',
      '.sql': 'sql'
    }

    return languageMap[extension.toLowerCase()]
  }

  private isTextFile(extension: string): boolean {
    const textExtensions = [
      '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.c', '.cpp', '.cs', '.php',
      '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.sh', '.ps1', '.html',
      '.css', '.scss', '.sass', '.less', '.json', '.xml', '.yaml', '.yml',
      '.md', '.txt', '.sql', '.r', '.m', '.pl', '.lua', '.vim', '.dockerfile'
    ]

    return textExtensions.includes(extension.toLowerCase()) || extension === ''
  }

  private isBinaryFile(buffer: Buffer): boolean {
    const maxCheckBytes = Math.min(buffer.length, 1024)
    let nullBytes = 0
    let controlBytes = 0

    for (let i = 0; i < maxCheckBytes; i++) {
      const byte = buffer[i]
      if (byte === 0) {
        nullBytes++
      } else if (byte < 32 && byte !== 9 && byte !== 10 && byte !== 13) {
        controlBytes++
      }
    }

    return nullBytes > 0 || controlBytes > maxCheckBytes * 0.3
  }

  private calculateContentHash(content: string | Buffer): string {
    return crypto.createHash('sha256').update(content).digest('hex')
  }

  private countCommentLines(lines: string[]): number {
    let commentLines = 0
    let inBlockComment = false

    for (const line of lines) {
      const trimmed = line.trim()
      
      if (trimmed.startsWith('//') || trimmed.startsWith('#')) {
        commentLines++
      } else if (trimmed.startsWith('/*') || trimmed.startsWith('"""') || trimmed.startsWith("'''")) {
        commentLines++
        inBlockComment = true
      } else if (inBlockComment && (trimmed.endsWith('*/') || trimmed.endsWith('"""') || trimmed.endsWith("'''"))) {
        commentLines++
        inBlockComment = false
      } else if (inBlockComment) {
        commentLines++
      }
    }

    return commentLines
  }

  private isMinified(content: string, averageLineLength: number, maxLineLength: number): boolean {
    return averageLineLength > 200 || maxLineLength > 1000 || content.split('\n').length < 10
  }
}