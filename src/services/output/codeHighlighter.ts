import * as vscode from 'vscode'

export interface CodeHighlight {
  range: vscode.Range
  type: HighlightType
  style: HighlightStyle
  tooltip?: string
  metadata: HighlightMetadata
}

export interface HighlightType {
  category: 'syntax' | 'semantic' | 'diagnostic' | 'search' | 'diff' | 'coverage' | 'custom'
  subcategory: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  persistent: boolean
}

export interface HighlightStyle {
  backgroundColor?: string
  color?: string
  border?: BorderStyle
  opacity?: number
  fontWeight?: 'normal' | 'bold'
  fontStyle?: 'normal' | 'italic'
  textDecoration?: 'none' | 'underline' | 'strikethrough' | 'overline'
  outline?: OutlineStyle
  gutter?: GutterStyle
  minimap?: MinimapStyle
}

export interface BorderStyle {
  width: number
  style: 'solid' | 'dashed' | 'dotted' | 'double'
  color: string
  radius?: number
}

export interface OutlineStyle {
  width: number
  style: 'solid' | 'dashed' | 'dotted'
  color: string
}

export interface GutterStyle {
  backgroundColor?: string
  color?: string
  icon?: string
  text?: string
}

export interface MinimapStyle {
  color?: string
  opacity?: number
}

export interface HighlightMetadata {
  id: string
  source: string
  created: Date
  expires?: Date
  confidence: number
  language: string
  context: string
  tags: string[]
  interactive: boolean
}

export interface SyntaxHighlighter {
  language: string
  name: string
  patterns: SyntaxPattern[]
  themes: SyntaxTheme[]
  extensions: string[]
  aliases: string[]
  configuration: SyntaxConfiguration
}

export interface SyntaxPattern {
  name: string
  pattern: string | RegExp
  scope: string
  captures?: PatternCapture[]
  include?: string[]
  begin?: string
  end?: string
  contentName?: string
  repository?: Record<string, SyntaxPattern>
}

export interface PatternCapture {
  index: number
  name: string
  scope: string
}

export interface SyntaxTheme {
  name: string
  type: 'light' | 'dark' | 'high_contrast'
  colors: ThemeColors
  tokenColors: TokenColor[]
  semanticColors: SemanticColor[]
}

export interface ThemeColors {
  background: string
  foreground: string
  selection: string
  lineHighlight: string
  cursor: string
  border: string
  [key: string]: string
}

export interface TokenColor {
  scope: string | string[]
  settings: TokenSettings
}

export interface TokenSettings {
  foreground?: string
  background?: string
  fontStyle?: string
}

export interface SemanticColor {
  type: string
  modifier?: string
  settings: TokenSettings
}

export interface SyntaxConfiguration {
  comments: CommentConfiguration
  brackets: BracketConfiguration
  autoClosingPairs: AutoClosingPair[]
  surroundingPairs: SurroundingPair[]
  folding: FoldingConfiguration
  indentation: IndentationConfiguration
  wordPattern: string
}

export interface CommentConfiguration {
  lineComment?: string
  blockComment?: [string, string]
}

export interface BracketConfiguration {
  colorized: boolean
  pairs: BracketPair[]
}

export interface BracketPair {
  open: string
  close: string
}

export interface AutoClosingPair {
  open: string
  close: string
  notIn?: string[]
}

export interface SurroundingPair {
  open: string
  close: string
}

export interface FoldingConfiguration {
  markers: FoldingMarker[]
  offSide: boolean
}

export interface FoldingMarker {
  start: string
  end: string
}

export interface IndentationConfiguration {
  increaseIndentPattern?: string
  decreaseIndentPattern?: string
  indentNextLinePattern?: string
  unIndentedLinePattern?: string
}

export interface HighlightRule {
  id: string
  name: string
  description: string
  language: string
  patterns: HighlightPattern[]
  conditions: HighlightCondition[]
  actions: HighlightAction[]
  enabled: boolean
  priority: number
}

export interface HighlightPattern {
  type: 'regex' | 'literal' | 'semantic' | 'ast'
  pattern: string
  flags?: string
  scope?: string
  caseSensitive: boolean
  wholeWord: boolean
  multiline: boolean
}

export interface HighlightCondition {
  type: 'language' | 'file_type' | 'context' | 'selection' | 'cursor_position'
  operator: 'equals' | 'contains' | 'matches' | 'in' | 'not_in'
  value: any
  weight: number
}

export interface HighlightAction {
  type: 'highlight' | 'underline' | 'border' | 'background' | 'gutter' | 'minimap'
  style: HighlightStyle
  duration?: number
  animation?: AnimationStyle
}

export interface AnimationStyle {
  type: 'fade' | 'slide' | 'bounce' | 'pulse' | 'flash'
  duration: number
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out'
  iterations: number | 'infinite'
}

export interface HighlightGroup {
  id: string
  name: string
  highlights: CodeHighlight[]
  style: GroupStyle
  metadata: GroupMetadata
}

export interface GroupStyle {
  connected: boolean
  connectionStyle?: ConnectionStyle
  groupBackground?: string
  groupBorder?: BorderStyle
}

export interface ConnectionStyle {
  type: 'line' | 'arrow' | 'bracket' | 'curve'
  color: string
  width: number
  style: 'solid' | 'dashed' | 'dotted'
}

export interface GroupMetadata {
  category: string
  description: string
  created: Date
  persistent: boolean
  interactive: boolean
  tags: string[]
}

export interface DiffHighlight {
  type: 'added' | 'removed' | 'modified' | 'moved'
  oldRange?: vscode.Range
  newRange?: vscode.Range
  content?: string
  metadata: DiffMetadata
}

export interface DiffMetadata {
  author?: string
  timestamp?: Date
  commit?: string
  branch?: string
  message?: string
  confidence: number
}

export interface CoverageHighlight {
  range: vscode.Range
  type: 'covered' | 'uncovered' | 'partially_covered'
  executionCount: number
  branchCoverage?: BranchCoverage
  metadata: CoverageMetadata
}

export interface BranchCoverage {
  total: number
  covered: number
  branches: BranchInfo[]
}

export interface BranchInfo {
  id: string
  condition: string
  covered: boolean
  executionCount: number
}

export interface CoverageMetadata {
  testSuite: string
  timestamp: Date
  version: string
  confidence: number
}

export interface SearchHighlight {
  range: vscode.Range
  query: string
  type: 'exact' | 'fuzzy' | 'regex' | 'semantic'
  relevance: number
  context: SearchContext
}

export interface SearchContext {
  before: string
  after: string
  lineNumber: number
  columnNumber: number
  fileName: string
}

export interface HighlightProvider {
  id: string
  name: string
  languages: string[]
  priority: number
  enabled: boolean
  configuration: ProviderConfiguration
}

export interface ProviderConfiguration {
  realtime: boolean
  debounceTime: number
  maxHighlights: number
  cacheable: boolean
  cacheTimeout: number
  dependencies: string[]
}

export class CodeHighlighter {
  private highlighters: Map<string, SyntaxHighlighter>
  private decorationTypes: Map<string, vscode.TextEditorDecorationType>
  private activeHighlights: Map<string, CodeHighlight[]>
  private highlightGroups: Map<string, HighlightGroup>
  private highlightRules: HighlightRule[]
  private providers: Map<string, HighlightProvider>
  private themes: Map<string, SyntaxTheme>
  private currentTheme: SyntaxTheme
  private disposables: vscode.Disposable[]
  private isEnabled: boolean

  constructor() {
    this.highlighters = new Map()
    this.decorationTypes = new Map()
    this.activeHighlights = new Map()
    this.highlightGroups = new Map()
    this.highlightRules = []
    this.providers = new Map()
    this.themes = new Map()
    this.currentTheme = this.getDefaultTheme()
    this.disposables = []
    this.isEnabled = true

    this.initializeHighlighters()
    this.initializeThemes()
    this.initializeProviders()
    this.registerEventListeners()
  }

  private initializeHighlighters(): void {
    // Initialize common syntax highlighters
    this.highlighters.set('typescript', {
      language: 'typescript',
      name: 'TypeScript',
      patterns: [
        {
          name: 'keyword',
          pattern: /\b(class|interface|type|enum|namespace|module|import|export|from|as|function|const|let|var|if|else|for|while|do|switch|case|default|break|continue|return|throw|try|catch|finally|async|await|yield|new|this|super|extends|implements|public|private|protected|static|readonly|abstract)\b/g,
          scope: 'keyword.control.ts'
        },
        {
          name: 'type',
          pattern: /\b(string|number|boolean|object|null|undefined|void|any|unknown|never)\b/g,
          scope: 'support.type.primitive.ts'
        },
        {
          name: 'string',
          pattern: /(['"`])((?:\\.|(?!\1)[^\\])*?)\1/g,
          scope: 'string.quoted.ts'
        },
        {
          name: 'comment',
          pattern: /\/\/.*$|\/\*[\s\S]*?\*\//gm,
          scope: 'comment.ts'
        },
        {
          name: 'number',
          pattern: /\b\d+(?:\.\d+)?(?:[eE][+-]?\d+)?\b/g,
          scope: 'constant.numeric.ts'
        }
      ],
      themes: [],
      extensions: ['.ts', '.tsx'],
      aliases: ['ts', 'typescript'],
      configuration: {
        comments: {
          lineComment: '//',
          blockComment: ['/*', '*/']
        },
        brackets: {
          colorized: true,
          pairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' },
            { open: '<', close: '>' }
          ]
        },
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' }
        ],
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" },
          { open: '`', close: '`' }
        ],
        folding: {
          markers: [
            { start: '{', end: '}' },
            { start: '/*', end: '*/' }
          ],
          offSide: false
        },
        indentation: {
          increaseIndentPattern: '^.*\\{[^}]*$',
          decreaseIndentPattern: '^\\s*\\}.*$'
        },
        wordPattern: '[a-zA-Z_$][a-zA-Z0-9_$]*'
      }
    })

    this.highlighters.set('python', {
      language: 'python',
      name: 'Python',
      patterns: [
        {
          name: 'keyword',
          pattern: /\b(and|as|assert|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|raise|return|try|while|with|yield|async|await)\b/g,
          scope: 'keyword.control.python'
        },
        {
          name: 'builtin',
          pattern: /\b(int|float|str|bool|list|dict|tuple|set|None|True|False|len|range|enumerate|zip|map|filter|sorted|reversed|sum|min|max|abs|round|print|input|open|type|isinstance|hasattr|getattr|setattr|delattr)\b/g,
          scope: 'support.function.builtin.python'
        },
        {
          name: 'string',
          pattern: /(['"])((?:\\.|(?!\1)[^\\])*?)\1|'''[\s\S]*?'''|"""[\s\S]*?"""/g,
          scope: 'string.quoted.python'
        },
        {
          name: 'comment',
          pattern: /#.*$/gm,
          scope: 'comment.python'
        },
        {
          name: 'number',
          pattern: /\b\d+(?:\.\d+)?(?:[eE][+-]?\d+)?\b/g,
          scope: 'constant.numeric.python'
        }
      ],
      themes: [],
      extensions: ['.py', '.pyw', '.pyi'],
      aliases: ['py', 'python'],
      configuration: {
        comments: {
          lineComment: '#'
        },
        brackets: {
          colorized: true,
          pairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' }
          ]
        },
        autoClosingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" }
        ],
        surroundingPairs: [
          { open: '{', close: '}' },
          { open: '[', close: ']' },
          { open: '(', close: ')' },
          { open: '"', close: '"' },
          { open: "'", close: "'" }
        ],
        folding: {
          markers: [
            { start: ':', end: '' }
          ],
          offSide: true
        },
        indentation: {
          increaseIndentPattern: '^.*:[ \\t]*$',
          decreaseIndentPattern: '^[ \\t]*(?:return|pass|break|continue|raise)\\b.*$'
        },
        wordPattern: '[a-zA-Z_][a-zA-Z0-9_]*'
      }
    })
  }

  private initializeThemes(): void {
    // Initialize common themes
    this.themes.set('default_dark', {
      name: 'Default Dark',
      type: 'dark',
      colors: {
        background: '#1e1e1e',
        foreground: '#d4d4d4',
        selection: '#264f78',
        lineHighlight: '#2a2d2e',
        cursor: '#ffffff',
        border: '#3e3e3e'
      },
      tokenColors: [
        {
          scope: 'keyword',
          settings: {
            foreground: '#569cd6',
            fontStyle: 'bold'
          }
        },
        {
          scope: 'string',
          settings: {
            foreground: '#ce9178'
          }
        },
        {
          scope: 'comment',
          settings: {
            foreground: '#6a9955',
            fontStyle: 'italic'
          }
        },
        {
          scope: 'constant.numeric',
          settings: {
            foreground: '#b5cea8'
          }
        },
        {
          scope: 'support.type',
          settings: {
            foreground: '#4ec9b0'
          }
        }
      ],
      semanticColors: [
        {
          type: 'class',
          settings: {
            foreground: '#4ec9b0'
          }
        },
        {
          type: 'function',
          settings: {
            foreground: '#dcdcaa'
          }
        },
        {
          type: 'variable',
          settings: {
            foreground: '#9cdcfe'
          }
        }
      ]
    })

    this.themes.set('default_light', {
      name: 'Default Light',
      type: 'light',
      colors: {
        background: '#ffffff',
        foreground: '#000000',
        selection: '#add6ff',
        lineHighlight: '#f0f0f0',
        cursor: '#000000',
        border: '#e0e0e0'
      },
      tokenColors: [
        {
          scope: 'keyword',
          settings: {
            foreground: '#0000ff',
            fontStyle: 'bold'
          }
        },
        {
          scope: 'string',
          settings: {
            foreground: '#a31515'
          }
        },
        {
          scope: 'comment',
          settings: {
            foreground: '#008000',
            fontStyle: 'italic'
          }
        },
        {
          scope: 'constant.numeric',
          settings: {
            foreground: '#098658'
          }
        },
        {
          scope: 'support.type',
          settings: {
            foreground: '#267f99'
          }
        }
      ],
      semanticColors: [
        {
          type: 'class',
          settings: {
            foreground: '#267f99'
          }
        },
        {
          type: 'function',
          settings: {
            foreground: '#795e26'
          }
        },
        {
          type: 'variable',
          settings: {
            foreground: '#001080'
          }
        }
      ]
    })
  }

  private initializeProviders(): void {
    // Initialize built-in providers
    this.providers.set('syntax', {
      id: 'syntax',
      name: 'Syntax Highlighter',
      languages: ['typescript', 'javascript', 'python', 'java', 'csharp', 'cpp', 'c'],
      priority: 1,
      enabled: true,
      configuration: {
        realtime: true,
        debounceTime: 100,
        maxHighlights: 10000,
        cacheable: true,
        cacheTimeout: 300000,
        dependencies: []
      }
    })

    this.providers.set('semantic', {
      id: 'semantic',
      name: 'Semantic Highlighter',
      languages: ['typescript', 'javascript', 'python'],
      priority: 2,
      enabled: true,
      configuration: {
        realtime: false,
        debounceTime: 500,
        maxHighlights: 5000,
        cacheable: true,
        cacheTimeout: 600000,
        dependencies: ['syntax']
      }
    })

    this.providers.set('diagnostic', {
      id: 'diagnostic',
      name: 'Diagnostic Highlighter',
      languages: ['*'],
      priority: 3,
      enabled: true,
      configuration: {
        realtime: true,
        debounceTime: 200,
        maxHighlights: 1000,
        cacheable: false,
        cacheTimeout: 0,
        dependencies: []
      }
    })
  }

  private registerEventListeners(): void {
    // Listen to active editor changes
    this.disposables.push(
      vscode.window.onDidChangeActiveTextEditor(editor => {
        if (editor) {
          this.highlightDocument(editor.document)
        }
      })
    )

    // Listen to document changes
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(event => {
        this.updateHighlights(event.document, event.contentChanges)
      })
    )

    // Listen to selection changes
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection(event => {
        this.updateSelectionHighlights(event.textEditor, event.selections)
      })
    )

    // Listen to configuration changes
    this.disposables.push(
      vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('editor.semanticHighlighting')) {
          this.refreshAllHighlights()
        }
      })
    )
  }

  async highlightDocument(document: vscode.TextDocument): Promise<void> {
    if (!this.isEnabled) return

    const language = document.languageId
    const text = document.getText()
    const uri = document.uri.toString()

    try {
      // Clear existing highlights
      this.clearHighlights(uri)

      // Apply syntax highlighting
      const syntaxHighlights = await this.applySyntaxHighlighting(text, language)
      
      // Apply semantic highlighting
      const semanticHighlights = await this.applySemanticHighlighting(document)
      
      // Apply diagnostic highlighting
      const diagnosticHighlights = await this.applyDiagnosticHighlighting(document)
      
      // Combine all highlights
      const allHighlights = [
        ...syntaxHighlights,
        ...semanticHighlights,
        ...diagnosticHighlights
      ]

      // Store highlights
      this.activeHighlights.set(uri, allHighlights)

      // Apply to editor
      await this.applyHighlights(document, allHighlights)

    } catch (error) {
      console.error('Error highlighting document:', error)
    }
  }

  private async applySyntaxHighlighting(text: string, language: string): Promise<CodeHighlight[]> {
    const highlights: CodeHighlight[] = []
    const highlighter = this.highlighters.get(language)

    if (!highlighter) {
      return highlights
    }

    const lines = text.split('\n')
    let lineIndex = 0

    for (const line of lines) {
      for (const pattern of highlighter.patterns) {
        const regex = pattern.pattern instanceof RegExp ? pattern.pattern : new RegExp(pattern.pattern, 'g')
        let match: RegExpExecArray | null

        while ((match = regex.exec(line)) !== null) {
          const startPos = new vscode.Position(lineIndex, match.index)
          const endPos = new vscode.Position(lineIndex, match.index + match[0].length)
          const range = new vscode.Range(startPos, endPos)

          const highlight: CodeHighlight = {
            range,
            type: {
              category: 'syntax',
              subcategory: pattern.name,
              priority: 'medium',
              persistent: true
            },
            style: this.getStyleForScope(pattern.scope),
            tooltip: `${pattern.name}: ${match[0]}`,
            metadata: {
              id: `syntax_${lineIndex}_${match.index}_${pattern.name}`,
              source: 'syntax_highlighter',
              created: new Date(),
              confidence: 1.0,
              language,
              context: line,
              tags: ['syntax', pattern.name],
              interactive: false
            }
          }

          highlights.push(highlight)
        }
      }
      lineIndex++
    }

    return highlights
  }

  private async applySemanticHighlighting(document: vscode.TextDocument): Promise<CodeHighlight[]> {
    const highlights: CodeHighlight[] = []
    
    // Use VSCode's built-in semantic tokens provider
    const semanticTokens = await vscode.commands.executeCommand<vscode.SemanticTokens>(
      'vscode.provideDocumentSemanticTokens',
      document.uri
    )

    if (!semanticTokens) {
      return highlights
    }

    const legend = await vscode.commands.executeCommand<vscode.SemanticTokensLegend>(
      'vscode.provideDocumentSemanticTokensLegend',
      document.uri
    )

    if (!legend) {
      return highlights
    }

    // Decode semantic tokens
    const tokens = this.decodeSemanticTokens(semanticTokens.data, legend)

    for (const token of tokens) {
      const startPos = new vscode.Position(token.line, token.character)
      const endPos = new vscode.Position(token.line, token.character + token.length)
      const range = new vscode.Range(startPos, endPos)

      const highlight: CodeHighlight = {
        range,
        type: {
          category: 'semantic',
          subcategory: token.type,
          priority: 'high',
          persistent: true
        },
        style: this.getStyleForSemanticToken(token.type, token.modifiers),
        tooltip: `${token.type}: ${document.getText(range)}`,
        metadata: {
          id: `semantic_${token.line}_${token.character}_${token.type}`,
          source: 'semantic_highlighter',
          created: new Date(),
          confidence: 0.9,
          language: document.languageId,
          context: document.lineAt(token.line).text,
          tags: ['semantic', token.type, ...token.modifiers],
          interactive: false
        }
      }

      highlights.push(highlight)
    }

    return highlights
  }

  private async applyDiagnosticHighlighting(document: vscode.TextDocument): Promise<CodeHighlight[]> {
    const highlights: CodeHighlight[] = []
    const diagnostics = vscode.languages.getDiagnostics(document.uri)

    for (const diagnostic of diagnostics) {
      const highlight: CodeHighlight = {
        range: diagnostic.range,
        type: {
          category: 'diagnostic',
          subcategory: vscode.DiagnosticSeverity[diagnostic.severity],
          priority: diagnostic.severity <= vscode.DiagnosticSeverity.Error ? 'critical' : 'medium',
          persistent: true
        },
        style: this.getStyleForDiagnostic(diagnostic.severity),
        tooltip: diagnostic.message,
        metadata: {
          id: `diagnostic_${diagnostic.range.start.line}_${diagnostic.range.start.character}_${diagnostic.severity}`,
          source: 'diagnostic_highlighter',
          created: new Date(),
          confidence: 1.0,
          language: document.languageId,
          context: document.getText(diagnostic.range),
          tags: ['diagnostic', vscode.DiagnosticSeverity[diagnostic.severity]],
          interactive: true
        }
      }

      highlights.push(highlight)
    }

    return highlights
  }

  private async applyHighlights(document: vscode.TextDocument, highlights: CodeHighlight[]): Promise<void> {
    const editor = vscode.window.visibleTextEditors.find(e => e.document.uri.toString() === document.uri.toString())
    if (!editor) return

    // Group highlights by decoration type
    const decorationMap = new Map<string, vscode.DecorationOptions[]>()

    for (const highlight of highlights) {
      const decorationType = this.getOrCreateDecorationType(highlight)
      const decorationKey = this.getDecorationKey(highlight)

      if (!decorationMap.has(decorationKey)) {
        decorationMap.set(decorationKey, [])
      }

      const decorationOptions: vscode.DecorationOptions = {
        range: highlight.range,
        hoverMessage: highlight.tooltip,
        renderOptions: this.getRenderOptions(highlight)
      }

      decorationMap.get(decorationKey)!.push(decorationOptions)
    }

    // Apply decorations
    for (const [decorationKey, decorationOptions] of decorationMap) {
      const decorationType = this.decorationTypes.get(decorationKey)
      if (decorationType) {
        editor.setDecorations(decorationType, decorationOptions)
      }
    }
  }

  private getOrCreateDecorationType(highlight: CodeHighlight): vscode.TextEditorDecorationType {
    const key = this.getDecorationKey(highlight)
    
    if (!this.decorationTypes.has(key)) {
      const decorationType = vscode.window.createTextEditorDecorationType({
        backgroundColor: highlight.style.backgroundColor,
        color: highlight.style.color,
        fontWeight: highlight.style.fontWeight,
        fontStyle: highlight.style.fontStyle,
        textDecoration: highlight.style.textDecoration,
        border: highlight.style.border ? `${highlight.style.border.width}px ${highlight.style.border.style} ${highlight.style.border.color}` : undefined,
        outline: highlight.style.outline ? `${highlight.style.outline.width}px ${highlight.style.outline.style} ${highlight.style.outline.color}` : undefined,
        opacity: highlight.style.opacity?.toString(),
        gutterIconPath: highlight.style.gutter?.icon,
        gutterIconSize: 'contain',
        overviewRulerColor: highlight.style.minimap?.color,
        overviewRulerLane: vscode.OverviewRulerLane.Right
      })

      this.decorationTypes.set(key, decorationType)
    }

    return this.decorationTypes.get(key)!
  }

  private getDecorationKey(highlight: CodeHighlight): string {
    return `${highlight.type.category}_${highlight.type.subcategory}_${highlight.type.priority}`
  }

  private getRenderOptions(highlight: CodeHighlight): vscode.DecorationInstanceRenderOptions {
    return {
      before: highlight.style.gutter?.text ? {
        contentText: highlight.style.gutter.text,
        color: highlight.style.gutter.color,
        backgroundColor: highlight.style.gutter.backgroundColor
      } : undefined,
      after: undefined
    }
  }

  private getStyleForScope(scope: string): HighlightStyle {
    const tokenColor = this.currentTheme.tokenColors.find(tc => 
      Array.isArray(tc.scope) ? tc.scope.includes(scope) : tc.scope === scope
    )

    if (tokenColor) {
      return {
        color: tokenColor.settings.foreground,
        backgroundColor: tokenColor.settings.background,
        fontStyle: tokenColor.settings.fontStyle?.includes('italic') ? 'italic' : 'normal',
        fontWeight: tokenColor.settings.fontStyle?.includes('bold') ? 'bold' : 'normal'
      }
    }

    return {
      color: this.currentTheme.colors.foreground
    }
  }

  private getStyleForSemanticToken(type: string, modifiers: string[]): HighlightStyle {
    const semanticColor = this.currentTheme.semanticColors.find(sc => sc.type === type)
    
    if (semanticColor) {
      return {
        color: semanticColor.settings.foreground,
        backgroundColor: semanticColor.settings.background,
        fontStyle: semanticColor.settings.fontStyle?.includes('italic') ? 'italic' : 'normal',
        fontWeight: semanticColor.settings.fontStyle?.includes('bold') ? 'bold' : 'normal'
      }
    }

    return {
      color: this.currentTheme.colors.foreground
    }
  }

  private getStyleForDiagnostic(severity: vscode.DiagnosticSeverity): HighlightStyle {
    switch (severity) {
      case vscode.DiagnosticSeverity.Error:
        return {
          textDecoration: 'underline',
          color: '#ff0000',
          outline: {
            width: 1,
            style: 'solid',
            color: '#ff0000'
          }
        }
      case vscode.DiagnosticSeverity.Warning:
        return {
          textDecoration: 'underline',
          color: '#ffaa00',
          outline: {
            width: 1,
            style: 'solid',
            color: '#ffaa00'
          }
        }
      case vscode.DiagnosticSeverity.Information:
        return {
          textDecoration: 'underline',
          color: '#0088ff',
          outline: {
            width: 1,
            style: 'dotted',
            color: '#0088ff'
          }
        }
      case vscode.DiagnosticSeverity.Hint:
        return {
          textDecoration: 'underline',
          color: '#888888',
          outline: {
            width: 1,
            style: 'dotted',
            color: '#888888'
          }
        }
      default:
        return {}
    }
  }

  private decodeSemanticTokens(data: Uint32Array, legend: vscode.SemanticTokensLegend): any[] {
    const tokens: any[] = []
    let line = 0
    let character = 0

    for (let i = 0; i < data.length; i += 5) {
      const deltaLine = data[i]
      const deltaCharacter = data[i + 1]
      const length = data[i + 2]
      const tokenType = data[i + 3]
      const tokenModifiers = data[i + 4]

      line += deltaLine
      character = deltaLine === 0 ? character + deltaCharacter : deltaCharacter

      const type = legend.tokenTypes[tokenType]
      const modifiers = this.decodeTokenModifiers(tokenModifiers, legend.tokenModifiers)

      tokens.push({
        line,
        character,
        length,
        type,
        modifiers
      })
    }

    return tokens
  }

  private decodeTokenModifiers(encoded: number, modifiers: string[]): string[] {
    const result: string[] = []
    let index = 0

    while (encoded > 0) {
      if (encoded & 1) {
        result.push(modifiers[index])
      }
      encoded >>= 1
      index++
    }

    return result
  }

  private getDefaultTheme(): SyntaxTheme {
    return this.themes.get('default_dark') || {
      name: 'Default',
      type: 'dark',
      colors: {
        background: '#1e1e1e',
        foreground: '#d4d4d4',
        selection: '#264f78',
        lineHighlight: '#2a2d2e',
        cursor: '#ffffff',
        border: '#3e3e3e'
      },
      tokenColors: [],
      semanticColors: []
    }
  }

  private async updateHighlights(document: vscode.TextDocument, changes: readonly vscode.TextDocumentContentChangeEvent[]): Promise<void> {
    if (!this.isEnabled) return

    // For now, just re-highlight the entire document
    // In a production implementation, we would optimize this to only update affected ranges
    await this.highlightDocument(document)
  }

  private async updateSelectionHighlights(editor: vscode.TextEditor, selections: readonly vscode.Selection[]): Promise<void> {
    if (!this.isEnabled || selections.length === 0) return

    const highlights: CodeHighlight[] = []

    for (const selection of selections) {
      if (!selection.isEmpty) {
        const highlight: CodeHighlight = {
          range: selection,
          type: {
            category: 'search',
            subcategory: 'selection',
            priority: 'high',
            persistent: false
          },
          style: {
            backgroundColor: this.currentTheme.colors.selection,
            opacity: 0.3
          },
          metadata: {
            id: `selection_${selection.start.line}_${selection.start.character}`,
            source: 'selection_highlighter',
            created: new Date(),
            confidence: 1.0,
            language: editor.document.languageId,
            context: editor.document.getText(selection),
            tags: ['selection'],
            interactive: false
          }
        }

        highlights.push(highlight)
      }
    }

    // Apply selection highlights
    await this.applyHighlights(editor.document, highlights)
  }

  private clearHighlights(uri: string): void {
    this.activeHighlights.delete(uri)
  }

  private async refreshAllHighlights(): Promise<void> {
    for (const editor of vscode.window.visibleTextEditors) {
      await this.highlightDocument(editor.document)
    }
  }

  // Public API methods
  async highlightRange(document: vscode.TextDocument, range: vscode.Range, type: HighlightType, style: HighlightStyle): Promise<string> {
    const highlight: CodeHighlight = {
      range,
      type,
      style,
      metadata: {
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        source: 'api',
        created: new Date(),
        confidence: 1.0,
        language: document.languageId,
        context: document.getText(range),
        tags: ['custom'],
        interactive: false
      }
    }

    const uri = document.uri.toString()
    const existingHighlights = this.activeHighlights.get(uri) || []
    existingHighlights.push(highlight)
    this.activeHighlights.set(uri, existingHighlights)

    await this.applyHighlights(document, [highlight])

    return highlight.metadata.id
  }

  async removeHighlight(document: vscode.TextDocument, highlightId: string): Promise<void> {
    const uri = document.uri.toString()
    const highlights = this.activeHighlights.get(uri) || []
    const filteredHighlights = highlights.filter(h => h.metadata.id !== highlightId)
    this.activeHighlights.set(uri, filteredHighlights)

    await this.applyHighlights(document, filteredHighlights)
  }

  async highlightSearch(document: vscode.TextDocument, query: string, type: SearchHighlight['type'] = 'exact'): Promise<SearchHighlight[]> {
    const highlights: SearchHighlight[] = []
    const text = document.getText()
    const lines = text.split('\n')

    let regex: RegExp
    if (type === 'regex') {
      regex = new RegExp(query, 'gi')
    } else if (type === 'exact') {
      regex = new RegExp(query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi')
    } else {
      // Fuzzy search - simplified implementation
      const fuzzyPattern = query.split('').map(char => char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('.*?')
      regex = new RegExp(fuzzyPattern, 'gi')
    }

    for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
      const line = lines[lineIndex]
      let match: RegExpExecArray | null

      while ((match = regex.exec(line)) !== null) {
        const startPos = new vscode.Position(lineIndex, match.index)
        const endPos = new vscode.Position(lineIndex, match.index + match[0].length)
        const range = new vscode.Range(startPos, endPos)

        const searchHighlight: SearchHighlight = {
          range,
          query,
          type,
          relevance: this.calculateRelevance(match[0], query),
          context: {
            before: line.substring(0, match.index),
            after: line.substring(match.index + match[0].length),
            lineNumber: lineIndex + 1,
            columnNumber: match.index + 1,
            fileName: document.fileName
          }
        }

        highlights.push(searchHighlight)
      }
    }

    return highlights
  }

  private calculateRelevance(match: string, query: string): number {
    if (match.toLowerCase() === query.toLowerCase()) return 1.0
    if (match.toLowerCase().includes(query.toLowerCase())) return 0.8
    return 0.5
  }

  async highlightDiff(oldDocument: vscode.TextDocument, newDocument: vscode.TextDocument): Promise<DiffHighlight[]> {
    const diffHighlights: DiffHighlight[] = []
    const oldText = oldDocument.getText()
    const newText = newDocument.getText()

    // Simple diff implementation - in production, use a proper diff library
    const oldLines = oldText.split('\n')
    const newLines = newText.split('\n')

    const maxLength = Math.max(oldLines.length, newLines.length)

    for (let i = 0; i < maxLength; i++) {
      const oldLine = oldLines[i] || ''
      const newLine = newLines[i] || ''

      if (oldLine !== newLine) {
        if (oldLine && !newLine) {
          // Line removed
          diffHighlights.push({
            type: 'removed',
            oldRange: new vscode.Range(i, 0, i, oldLine.length),
            content: oldLine,
            metadata: {
              confidence: 1.0
            }
          })
        } else if (!oldLine && newLine) {
          // Line added
          diffHighlights.push({
            type: 'added',
            newRange: new vscode.Range(i, 0, i, newLine.length),
            content: newLine,
            metadata: {
              confidence: 1.0
            }
          })
        } else {
          // Line modified
          diffHighlights.push({
            type: 'modified',
            oldRange: new vscode.Range(i, 0, i, oldLine.length),
            newRange: new vscode.Range(i, 0, i, newLine.length),
            content: newLine,
            metadata: {
              confidence: 1.0
            }
          })
        }
      }
    }

    return diffHighlights
  }

  async highlightCoverage(document: vscode.TextDocument, coverage: CoverageHighlight[]): Promise<void> {
    const highlights: CodeHighlight[] = []

    for (const cov of coverage) {
      const highlight: CodeHighlight = {
        range: cov.range,
        type: {
          category: 'coverage',
          subcategory: cov.type,
          priority: 'medium',
          persistent: true
        },
        style: this.getStyleForCoverage(cov.type, cov.executionCount),
        tooltip: `Coverage: ${cov.type} (${cov.executionCount} executions)`,
        metadata: {
          id: `coverage_${cov.range.start.line}_${cov.range.start.character}`,
          source: 'coverage_highlighter',
          created: new Date(),
          confidence: cov.metadata.confidence,
          language: document.languageId,
          context: document.getText(cov.range),
          tags: ['coverage', cov.type],
          interactive: false
        }
      }

      highlights.push(highlight)
    }

    await this.applyHighlights(document, highlights)
  }

  private getStyleForCoverage(type: CoverageHighlight['type'], executionCount: number): HighlightStyle {
    switch (type) {
      case 'covered':
        return {
          backgroundColor: executionCount > 10 ? '#00ff0040' : '#00ff0020',
          color: '#008000'
        }
      case 'uncovered':
        return {
          backgroundColor: '#ff000040',
          color: '#ff0000'
        }
      case 'partially_covered':
        return {
          backgroundColor: '#ffff0040',
          color: '#ff8000'
        }
      default:
        return {}
    }
  }

  createHighlightGroup(name: string, highlights: CodeHighlight[]): string {
    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const group: HighlightGroup = {
      id: groupId,
      name,
      highlights,
      style: {
        connected: false
      },
      metadata: {
        category: 'custom',
        description: `Custom highlight group: ${name}`,
        created: new Date(),
        persistent: true,
        interactive: false,
        tags: ['group', 'custom']
      }
    }

    this.highlightGroups.set(groupId, group)
    return groupId
  }

  addHighlightRule(rule: HighlightRule): void {
    this.highlightRules.push(rule)
  }

  removeHighlightRule(ruleId: string): void {
    this.highlightRules = this.highlightRules.filter(r => r.id !== ruleId)
  }

  setTheme(themeName: string): void {
    const theme = this.themes.get(themeName)
    if (theme) {
      this.currentTheme = theme
      this.refreshAllHighlights()
    }
  }

  getActiveHighlights(uri: string): CodeHighlight[] {
    return this.activeHighlights.get(uri) || []
  }

  getAvailableThemes(): string[] {
    return Array.from(this.themes.keys())
  }

  getAvailableLanguages(): string[] {
    return Array.from(this.highlighters.keys())
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    if (!enabled) {
      this.clearAllHighlights()
    } else {
      this.refreshAllHighlights()
    }
  }

  private clearAllHighlights(): void {
    for (const decorationType of this.decorationTypes.values()) {
      decorationType.dispose()
    }
    this.decorationTypes.clear()
    this.activeHighlights.clear()
  }

  dispose(): void {
    this.clearAllHighlights()
    this.disposables.forEach(d => d.dispose())
    this.highlightGroups.clear()
  }
}