import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { StorageService } from '../storageService'
import { LearningEngine } from './learningEngine'

export interface FeedbackEntry {
  id: string
  type: FeedbackType
  content: FeedbackContent
  context: FeedbackContext
  timestamp: Date
  userId: string
  processed: boolean
  impact: FeedbackImpact
  metadata: Record<string, any>
}

export interface FeedbackType {
  category: 'explicit' | 'implicit' | 'behavioral' | 'system'
  subcategory: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
}

export interface FeedbackContent {
  rating?: number
  sentiment?: 'positive' | 'negative' | 'neutral'
  text?: string
  tags: string[]
  suggestion?: string
  expectedOutcome?: string
  actualOutcome?: string
}

export interface FeedbackContext {
  feature: string
  action: string
  filePath?: string
  language?: string
  session: string
  userAgent?: string
  workspaceRoot?: string
  codeContext?: string
  errorMessage?: string
  performance?: PerformanceMetrics
}

export interface PerformanceMetrics {
  responseTime: number
  accuracy: number
  relevance: number
  completeness: number
  satisfaction: number
}

export interface FeedbackImpact {
  priority: 'low' | 'medium' | 'high' | 'critical'
  affectedComponents: string[]
  estimatedUsers: number
  businessValue: number
  technicalDebt: number
  userExperience: number
}

export interface FeedbackAnalysis {
  trends: FeedbackTrend[]
  patterns: FeedbackPattern[]
  insights: FeedbackInsight[]
  recommendations: FeedbackRecommendation[]
  summary: FeedbackSummary
}

export interface FeedbackTrend {
  metric: string
  direction: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  magnitude: number
  timeframe: string
  significance: number
  dataPoints: TrendDataPoint[]
}

export interface TrendDataPoint {
  timestamp: Date
  value: number
  context: any
}

export interface FeedbackPattern {
  id: string
  name: string
  description: string
  frequency: number
  conditions: PatternCondition[]
  outcomes: PatternOutcome[]
  confidence: number
  examples: FeedbackEntry[]
}

export interface PatternCondition {
  field: string
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in'
  value: any
  weight: number
}

export interface PatternOutcome {
  type: 'improvement' | 'degradation' | 'neutral'
  metric: string
  impact: number
  probability: number
}

export interface FeedbackInsight {
  type: 'user_satisfaction' | 'performance_issue' | 'feature_request' | 'bug_report' | 'usage_pattern'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  evidence: FeedbackEntry[]
  actionable: boolean
  estimatedEffort: number
}

export interface FeedbackRecommendation {
  id: string
  title: string
  description: string
  category: 'feature' | 'bugfix' | 'performance' | 'usability' | 'documentation'
  priority: 'low' | 'medium' | 'high' | 'critical'
  effort: 'trivial' | 'minor' | 'major' | 'epic'
  impact: 'low' | 'medium' | 'high' | 'critical'
  confidence: number
  supportingFeedback: string[]
  implementationSteps: string[]
  expectedOutcome: string
  metrics: string[]
}

export interface FeedbackSummary {
  totalFeedback: number
  averageRating: number
  sentimentDistribution: Record<string, number>
  topIssues: string[]
  topRequests: string[]
  userSatisfaction: number
  timeframe: string
}

export interface CollectionStrategy {
  name: string
  description: string
  triggers: CollectionTrigger[]
  methods: CollectionMethod[]
  frequency: 'continuous' | 'periodic' | 'event_driven'
  enabled: boolean
}

export interface CollectionTrigger {
  event: string
  condition: string
  probability: number
  cooldown: number
}

export interface CollectionMethod {
  type: 'inline' | 'popup' | 'background' | 'survey' | 'telemetry'
  parameters: Record<string, any>
  intrusiveness: 'none' | 'low' | 'medium' | 'high'
  expectedResponse: number
}

export class FeedbackCollector {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private storage: StorageService
  private learningEngine: LearningEngine
  private feedbackEntries: FeedbackEntry[]
  private collectionStrategies: CollectionStrategy[]
  private analysisCache: Map<string, FeedbackAnalysis>
  private currentSession: string

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    storage: StorageService,
    learningEngine: LearningEngine
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.storage = storage
    this.learningEngine = learningEngine
    this.feedbackEntries = []
    this.collectionStrategies = []
    this.analysisCache = new Map()
    this.currentSession = this.generateSessionId()
    
    this.initializeCollectionStrategies()
  }

  private initializeCollectionStrategies(): void {
    this.collectionStrategies = [
      {
        name: 'Code Completion Feedback',
        description: 'Collect feedback on code completion suggestions',
        triggers: [
          {
            event: 'completion_accepted',
            condition: 'random(0.1)', // 10% chance
            probability: 0.1,
            cooldown: 300000 // 5 minutes
          },
          {
            event: 'completion_rejected',
            condition: 'consecutive_rejections > 3',
            probability: 0.5,
            cooldown: 600000 // 10 minutes
          }
        ],
        methods: [
          {
            type: 'inline',
            parameters: { timeout: 5000 },
            intrusiveness: 'low',
            expectedResponse: 0.3
          }
        ],
        frequency: 'event_driven',
        enabled: true
      },
      {
        name: 'Search Quality Feedback',
        description: 'Collect feedback on search result quality',
        triggers: [
          {
            event: 'search_completed',
            condition: 'results_count > 0',
            probability: 0.05,
            cooldown: 900000 // 15 minutes
          }
        ],
        methods: [
          {
            type: 'background',
            parameters: { delay: 10000 },
            intrusiveness: 'none',
            expectedResponse: 0.8
          }
        ],
        frequency: 'event_driven',
        enabled: true
      },
      {
        name: 'Performance Feedback',
        description: 'Collect feedback on system performance',
        triggers: [
          {
            event: 'slow_response',
            condition: 'response_time > 5000',
            probability: 0.8,
            cooldown: 1800000 // 30 minutes
          }
        ],
        methods: [
          {
            type: 'popup',
            parameters: { dismissible: true },
            intrusiveness: 'medium',
            expectedResponse: 0.6
          }
        ],
        frequency: 'event_driven',
        enabled: true
      },
      {
        name: 'Weekly Satisfaction Survey',
        description: 'Regular satisfaction survey',
        triggers: [
          {
            event: 'weekly_check',
            condition: 'active_sessions > 10',
            probability: 1.0,
            cooldown: 604800000 // 1 week
          }
        ],
        methods: [
          {
            type: 'survey',
            parameters: { questions: 5, estimated_time: 120 },
            intrusiveness: 'high',
            expectedResponse: 0.2
          }
        ],
        frequency: 'periodic',
        enabled: true
      }
    ]
  }

  async collectFeedback(
    type: FeedbackType,
    content: FeedbackContent,
    context: FeedbackContext
  ): Promise<string> {
    const feedbackId = this.generateFeedbackId()
    
    const feedback: FeedbackEntry = {
      id: feedbackId,
      type,
      content,
      context: {
        ...context,
        session: this.currentSession
      },
      timestamp: new Date(),
      userId: 'default_user', // Would be actual user ID
      processed: false,
      impact: await this.assessImpact(type, content, context),
      metadata: {}
    }
    
    this.feedbackEntries.push(feedback)
    
    // Trigger immediate processing for high-impact feedback
    if (feedback.impact.priority === 'critical' || feedback.impact.priority === 'high') {
      await this.processFeedback(feedback)
    }
    
    // Save to storage
    await this.saveFeedback(feedback)
    
    return feedbackId
  }

  async collectImplicitFeedback(
    action: string,
    outcome: 'success' | 'failure' | 'partial',
    context: FeedbackContext,
    metrics?: PerformanceMetrics
  ): Promise<void> {
    const feedbackType: FeedbackType = {
      category: 'implicit',
      subcategory: 'behavioral',
      severity: outcome === 'failure' ? 'medium' : 'low',
      confidence: 0.7
    }
    
    const feedbackContent: FeedbackContent = {
      sentiment: outcome === 'success' ? 'positive' : outcome === 'failure' ? 'negative' : 'neutral',
      tags: ['implicit', 'behavioral', action],
      actualOutcome: outcome
    }
    
    const enhancedContext: FeedbackContext = {
      ...context,
      performance: metrics
    }
    
    await this.collectFeedback(feedbackType, feedbackContent, enhancedContext)
  }

  async collectExplicitFeedback(
    rating: number,
    text: string,
    context: FeedbackContext,
    suggestion?: string
  ): Promise<string> {
    const feedbackType: FeedbackType = {
      category: 'explicit',
      subcategory: 'user_rating',
      severity: rating < 3 ? 'high' : rating < 4 ? 'medium' : 'low',
      confidence: 0.9
    }
    
    const feedbackContent: FeedbackContent = {
      rating,
      sentiment: rating >= 4 ? 'positive' : rating <= 2 ? 'negative' : 'neutral',
      text,
      tags: ['explicit', 'rating', 'user_input'],
      suggestion
    }
    
    return await this.collectFeedback(feedbackType, feedbackContent, context)
  }

  private async assessImpact(
    type: FeedbackType,
    content: FeedbackContent,
    context: FeedbackContext
  ): Promise<FeedbackImpact> {
    let priority: FeedbackImpact['priority'] = 'low'
    let estimatedUsers = 1
    let businessValue = 0.1
    let technicalDebt = 0.1
    let userExperience = 0.1
    
    // Assess priority based on type and content
    if (type.category === 'explicit' && content.rating && content.rating <= 2) {
      priority = 'high'
      userExperience = 0.8
      businessValue = 0.6
    } else if (type.category === 'system' && type.severity === 'critical') {
      priority = 'critical'
      estimatedUsers = 100
      technicalDebt = 0.9
      businessValue = 0.7
    } else if (context.errorMessage) {
      priority = 'medium'
      technicalDebt = 0.5
      userExperience = 0.4
    }
    
    // Assess affected components
    const affectedComponents = this.identifyAffectedComponents(context)
    
    return {
      priority,
      affectedComponents,
      estimatedUsers,
      businessValue,
      technicalDebt,
      userExperience
    }
  }

  private identifyAffectedComponents(context: FeedbackContext): string[] {
    const components: string[] = []
    
    if (context.feature) {
      components.push(context.feature)
    }
    
    if (context.language) {
      components.push(`${context.language}_support`)
    }
    
    if (context.performance) {
      components.push('performance')
    }
    
    if (context.errorMessage) {
      components.push('error_handling')
    }
    
    return components
  }

  private async processFeedback(feedback: FeedbackEntry): Promise<void> {
    try {
      // Update learning models with feedback
      await this.updateLearningModels(feedback)
      
      // Trigger immediate actions for critical feedback
      if (feedback.impact.priority === 'critical') {
        await this.handleCriticalFeedback(feedback)
      }
      
      // Mark as processed
      feedback.processed = true
      
      // Update analysis cache
      this.analysisCache.clear()
      
    } catch (error) {
      console.error('Error processing feedback:', error)
    }
  }

  private async updateLearningModels(feedback: FeedbackEntry): Promise<void> {
    const models = this.learningEngine.getModels()
    
    for (const model of models) {
      if (this.isFeedbackRelevantToModel(feedback, model)) {
        // Convert feedback to training sample
        const trainingSample = this.convertFeedbackToTrainingSample(feedback, model)
        
        // Trigger incremental learning
        if (feedback.impact.priority === 'high' || feedback.impact.priority === 'critical') {
          await this.learningEngine.trainModel(model.id, true)
        }
      }
    }
  }

  private isFeedbackRelevantToModel(feedback: FeedbackEntry, model: any): boolean {
    const context = feedback.context
    
    switch (model.type.domain) {
      case 'code_completion':
        return context.feature === 'completion' || context.action.includes('complete')
      case 'pattern_recognition':
        return context.feature === 'pattern' || context.codeContext !== undefined
      case 'preference_learning':
        return feedback.type.category === 'explicit' || feedback.type.category === 'behavioral'
      case 'workflow_optimization':
        return context.performance !== undefined
      default:
        return false
    }
  }

  private convertFeedbackToTrainingSample(feedback: FeedbackEntry, model: any): any {
    return {
      id: feedback.id,
      input: {
        context: feedback.context,
        feature: feedback.context.feature,
        action: feedback.context.action
      },
      output: {
        rating: feedback.content.rating,
        sentiment: feedback.content.sentiment,
        success: feedback.content.sentiment === 'positive'
      },
      weight: this.calculateFeedbackWeight(feedback),
      timestamp: feedback.timestamp,
      context: feedback.context
    }
  }

  private calculateFeedbackWeight(feedback: FeedbackEntry): number {
    let weight = 1.0
    
    // Explicit feedback has higher weight
    if (feedback.type.category === 'explicit') {
      weight *= 1.5
    }
    
    // High confidence feedback has higher weight
    weight *= feedback.type.confidence
    
    // Recent feedback has higher weight
    const hoursSinceCreation = (Date.now() - feedback.timestamp.getTime()) / (1000 * 60 * 60)
    weight *= Math.exp(-hoursSinceCreation / 24) // Decay over 24 hours
    
    return weight
  }

  private async handleCriticalFeedback(feedback: FeedbackEntry): Promise<void> {
    // Log critical feedback
    console.warn('Critical feedback received:', feedback)
    
    // Notify development team (would integrate with notification system)
    await this.notifyDevelopmentTeam(feedback)
    
    // Create immediate action items
    await this.createActionItems(feedback)
  }

  private async notifyDevelopmentTeam(feedback: FeedbackEntry): Promise<void> {
    // This would integrate with notification systems (email, Slack, etc.)
    console.log('Notifying development team about critical feedback:', feedback.id)
  }

  private async createActionItems(feedback: FeedbackEntry): Promise<void> {
    // This would integrate with issue tracking systems
    console.log('Creating action items for feedback:', feedback.id)
  }

  async analyzeFeedback(timeframe: string = '7d'): Promise<FeedbackAnalysis> {
    const cacheKey = `analysis_${timeframe}`
    
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!
    }
    
    const startDate = this.getStartDate(timeframe)
    const relevantFeedback = this.feedbackEntries.filter(f => f.timestamp >= startDate)
    
    const trends = this.analyzeTrends(relevantFeedback)
    const patterns = this.analyzePatterns(relevantFeedback)
    const insights = this.generateInsights(relevantFeedback)
    const recommendations = this.generateRecommendations(relevantFeedback, insights)
    const summary = this.generateSummary(relevantFeedback)
    
    const analysis: FeedbackAnalysis = {
      trends,
      patterns,
      insights,
      recommendations,
      summary
    }
    
    this.analysisCache.set(cacheKey, analysis)
    return analysis
  }

  private getStartDate(timeframe: string): Date {
    const now = new Date()
    const match = timeframe.match(/(\d+)([dwmy])/)
    
    if (!match) return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // Default 7 days
    
    const value = parseInt(match[1])
    const unit = match[2]
    
    switch (unit) {
      case 'd':
        return new Date(now.getTime() - value * 24 * 60 * 60 * 1000)
      case 'w':
        return new Date(now.getTime() - value * 7 * 24 * 60 * 60 * 1000)
      case 'm':
        return new Date(now.getTime() - value * 30 * 24 * 60 * 60 * 1000)
      case 'y':
        return new Date(now.getTime() - value * 365 * 24 * 60 * 60 * 1000)
      default:
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }
  }

  private analyzeTrends(feedback: FeedbackEntry[]): FeedbackTrend[] {
    const trends: FeedbackTrend[] = []
    
    // Analyze rating trends
    const ratingTrend = this.analyzeRatingTrend(feedback)
    if (ratingTrend) trends.push(ratingTrend)
    
    // Analyze volume trends
    const volumeTrend = this.analyzeVolumeTrend(feedback)
    if (volumeTrend) trends.push(volumeTrend)
    
    // Analyze sentiment trends
    const sentimentTrend = this.analyzeSentimentTrend(feedback)
    if (sentimentTrend) trends.push(sentimentTrend)
    
    return trends
  }

  private analyzeRatingTrend(feedback: FeedbackEntry[]): FeedbackTrend | null {
    const ratingFeedback = feedback.filter(f => f.content.rating !== undefined)
    if (ratingFeedback.length < 2) return null
    
    const dataPoints = ratingFeedback.map(f => ({
      timestamp: f.timestamp,
      value: f.content.rating!,
      context: f.context
    }))
    
    const avgRating = dataPoints.reduce((sum, p) => sum + p.value, 0) / dataPoints.length
    const trend = dataPoints.length > 1 ? 
      (dataPoints[dataPoints.length - 1].value - dataPoints[0].value) / dataPoints.length : 0
    
    return {
      metric: 'average_rating',
      direction: trend > 0.1 ? 'increasing' : trend < -0.1 ? 'decreasing' : 'stable',
      magnitude: Math.abs(trend),
      timeframe: '7d',
      significance: Math.abs(trend) > 0.5 ? 0.8 : 0.4,
      dataPoints
    }
  }

  private analyzeVolumeTrend(feedback: FeedbackEntry[]): FeedbackTrend | null {
    const dailyVolumes = new Map<string, number>()
    
    for (const f of feedback) {
      const dateKey = f.timestamp.toISOString().split('T')[0]
      dailyVolumes.set(dateKey, (dailyVolumes.get(dateKey) || 0) + 1)
    }
    
    const dataPoints = Array.from(dailyVolumes.entries()).map(([date, volume]) => ({
      timestamp: new Date(date),
      value: volume,
      context: { date }
    }))
    
    if (dataPoints.length < 2) return null
    
    const avgVolume = dataPoints.reduce((sum, p) => sum + p.value, 0) / dataPoints.length
    const trend = dataPoints.length > 1 ? 
      (dataPoints[dataPoints.length - 1].value - dataPoints[0].value) / dataPoints.length : 0
    
    return {
      metric: 'feedback_volume',
      direction: trend > 0.5 ? 'increasing' : trend < -0.5 ? 'decreasing' : 'stable',
      magnitude: Math.abs(trend),
      timeframe: '7d',
      significance: Math.abs(trend) > avgVolume * 0.2 ? 0.7 : 0.3,
      dataPoints
    }
  }

  private analyzeSentimentTrend(feedback: FeedbackEntry[]): FeedbackTrend | null {
    const sentimentFeedback = feedback.filter(f => f.content.sentiment !== undefined)
    if (sentimentFeedback.length < 2) return null
    
    const sentimentValues = sentimentFeedback.map(f => {
      const value = f.content.sentiment === 'positive' ? 1 : 
                   f.content.sentiment === 'negative' ? -1 : 0
      return {
        timestamp: f.timestamp,
        value,
        context: f.context
      }
    })
    
    const avgSentiment = sentimentValues.reduce((sum, p) => sum + p.value, 0) / sentimentValues.length
    const trend = sentimentValues.length > 1 ? 
      (sentimentValues[sentimentValues.length - 1].value - sentimentValues[0].value) / sentimentValues.length : 0
    
    return {
      metric: 'sentiment_score',
      direction: trend > 0.1 ? 'increasing' : trend < -0.1 ? 'decreasing' : 'stable',
      magnitude: Math.abs(trend),
      timeframe: '7d',
      significance: Math.abs(trend) > 0.3 ? 0.8 : 0.4,
      dataPoints: sentimentValues
    }
  }

  private analyzePatterns(feedback: FeedbackEntry[]): FeedbackPattern[] {
    // This would implement pattern recognition in feedback
    // For now, return empty array
    return []
  }

  private generateInsights(feedback: FeedbackEntry[]): FeedbackInsight[] {
    const insights: FeedbackInsight[] = []
    
    // Critical feedback insight
    const criticalFeedback = feedback.filter(f => f.impact.priority === 'critical')
    if (criticalFeedback.length > 0) {
      insights.push({
        type: 'bug_report',
        title: 'Critical Issues Detected',
        description: `${criticalFeedback.length} critical issues reported`,
        severity: 'critical',
        confidence: 0.9,
        evidence: criticalFeedback,
        actionable: true,
        estimatedEffort: 8
      })
    }
    
    // Low satisfaction insight
    const lowRatingFeedback = feedback.filter(f => f.content.rating && f.content.rating <= 2)
    if (lowRatingFeedback.length > feedback.length * 0.2) {
      insights.push({
        type: 'user_satisfaction',
        title: 'Low User Satisfaction',
        description: `${Math.round(lowRatingFeedback.length / feedback.length * 100)}% of feedback has low ratings`,
        severity: 'high',
        confidence: 0.8,
        evidence: lowRatingFeedback,
        actionable: true,
        estimatedEffort: 16
      })
    }
    
    return insights
  }

  private generateRecommendations(feedback: FeedbackEntry[], insights: FeedbackInsight[]): FeedbackRecommendation[] {
    const recommendations: FeedbackRecommendation[] = []
    
    for (const insight of insights) {
      if (insight.actionable) {
        recommendations.push({
          id: `rec_${Date.now()}`,
          title: `Address ${insight.title}`,
          description: `Recommended action based on ${insight.title}`,
          category: insight.type === 'bug_report' ? 'bugfix' : 'usability',
          priority: insight.severity === 'critical' ? 'critical' : 'high',
          effort: insight.estimatedEffort > 10 ? 'major' : 'minor',
          impact: insight.severity === 'critical' ? 'critical' : 'high',
          confidence: insight.confidence,
          supportingFeedback: insight.evidence.map(e => e.id),
          implementationSteps: [`Analyze ${insight.type}`, 'Implement fix', 'Test solution'],
          expectedOutcome: 'Improved user satisfaction',
          metrics: ['user_rating', 'error_rate', 'satisfaction_score']
        })
      }
    }
    
    return recommendations
  }

  private generateSummary(feedback: FeedbackEntry[]): FeedbackSummary {
    const totalFeedback = feedback.length
    const ratingFeedback = feedback.filter(f => f.content.rating !== undefined)
    const averageRating = ratingFeedback.length > 0 ? 
      ratingFeedback.reduce((sum, f) => sum + f.content.rating!, 0) / ratingFeedback.length : 0
    
    const sentimentDistribution = {
      positive: feedback.filter(f => f.content.sentiment === 'positive').length,
      negative: feedback.filter(f => f.content.sentiment === 'negative').length,
      neutral: feedback.filter(f => f.content.sentiment === 'neutral').length
    }
    
    const topIssues = this.extractTopIssues(feedback)
    const topRequests = this.extractTopRequests(feedback)
    
    return {
      totalFeedback,
      averageRating,
      sentimentDistribution,
      topIssues,
      topRequests,
      userSatisfaction: averageRating / 5 * 100, // Convert to percentage
      timeframe: '7d'
    }
  }

  private extractTopIssues(feedback: FeedbackEntry[]): string[] {
    const issues = feedback
      .filter(f => f.content.sentiment === 'negative' || f.content.rating && f.content.rating <= 2)
      .map(f => f.context.feature)
      .filter(Boolean)
    
    const issueCounts = new Map<string, number>()
    issues.forEach(issue => {
      issueCounts.set(issue, (issueCounts.get(issue) || 0) + 1)
    })
    
    return Array.from(issueCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([issue]) => issue)
  }

  private extractTopRequests(feedback: FeedbackEntry[]): string[] {
    const requests = feedback
      .filter(f => f.content.suggestion)
      .map(f => f.content.suggestion!)
      .filter(Boolean)
    
    // Simple keyword extraction
    const keywords = new Map<string, number>()
    requests.forEach(request => {
      const words = request.toLowerCase().split(/\s+/)
      words.forEach(word => {
        if (word.length > 3) {
          keywords.set(word, (keywords.get(word) || 0) + 1)
        }
      })
    })
    
    return Array.from(keywords.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([keyword]) => keyword)
  }

  private async saveFeedback(feedback: FeedbackEntry): Promise<void> {
    try {
      await this.storage.setWorkspaceState(`feedback_${feedback.id}`, feedback)
    } catch (error) {
      console.error('Error saving feedback:', error)
    }
  }

  private generateFeedbackId(): string {
    return `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  getFeedbackEntries(): FeedbackEntry[] {
    return [...this.feedbackEntries]
  }

  getCollectionStrategies(): CollectionStrategy[] {
    return [...this.collectionStrategies]
  }

  async clearFeedback(): Promise<void> {
    this.feedbackEntries = []
    this.analysisCache.clear()
  }
}