import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { RelevanceScorer, RelevanceScore } from './relevanceScorer'
import { ContextInfo } from './contextAnalyzer'

export interface OptimizedContext {
  primaryContext: ContextBlock[]
  secondaryContext: ContextBlock[]
  metadata: ContextMetadata
  tokenUsage: TokenUsage
  optimizationStrategy: OptimizationStrategy
}

export interface ContextBlock {
  type: 'file' | 'function' | 'class' | 'symbol' | 'snippet'
  filePath: string
  content: string
  startLine?: number
  endLine?: number
  relevanceScore: number
  reason: string
  priority: number
  tokenCount: number
  language: string
  isComplete: boolean
}

export interface ContextMetadata {
  totalFiles: number
  selectedFiles: number
  totalTokens: number
  usedTokens: number
  remainingTokens: number
  compressionRatio: number
  selectionCriteria: SelectionCriteria
  executionTime: number
}

export interface TokenUsage {
  instruction: number
  primaryContext: number
  secondaryContext: number
  metadata: number
  reserved: number
  available: number
  total: number
}

export interface OptimizationStrategy {
  approach: 'comprehensive' | 'focused' | 'minimal' | 'adaptive'
  prioritization: 'relevance' | 'recency' | 'size' | 'mixed'
  compressionLevel: 'none' | 'light' | 'moderate' | 'aggressive'
  includeTests: boolean
  includeDocs: boolean
  maxDepth: number
  confidence: number
}

export interface SelectionCriteria {
  minRelevanceScore: number
  maxFileSize: number
  maxTotalFiles: number
  includePatterns: string[]
  excludePatterns: string[]
  languagePreferences: string[]
  contextWindow: number
  focusArea?: FocusArea
}

export interface FocusArea {
  type: 'cursor' | 'selection' | 'function' | 'class' | 'file'
  radius: number
  weight: number
  includeRelated: boolean
}

export interface OptimizationOptions {
  maxTokens: number
  strategy?: OptimizationStrategy
  criteria?: Partial<SelectionCriteria>
  userIntent?: string
  preserveCompleteness?: boolean
  enableCompression?: boolean
  adaptToModel?: string
}

export interface CompressionOptions {
  removeComments: boolean
  removeBlankLines: boolean
  minimizeWhitespace: boolean
  extractSignatures: boolean
  summarizeDocstrings: boolean
  skipImplementations: boolean
}

export class ContextOptimizer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private relevanceScorer: RelevanceScorer
  private tokenCounter: TokenCounter

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    relevanceScorer: RelevanceScorer
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.relevanceScorer = relevanceScorer
    this.tokenCounter = new TokenCounter()
  }

  async optimizeContext(
    contextInfo: ContextInfo,
    options: OptimizationOptions
  ): Promise<OptimizedContext> {
    const startTime = Date.now()
    
    // Determine optimization strategy
    const strategy = this.determineStrategy(contextInfo, options)
    
    // Collect candidate content
    const candidates = await this.collectCandidates(contextInfo, strategy)
    
    // Score and rank content
    const scoredCandidates = await this.scoreAndRankContent(candidates, contextInfo, strategy)
    
    // Select optimal content within token limits
    const selection = await this.selectOptimalContent(scoredCandidates, options, strategy)
    
    // Compress content if needed
    const optimizedSelection = await this.compressContent(selection, strategy)
    
    // Build final context
    const optimizedContext = await this.buildOptimizedContext(
      optimizedSelection,
      contextInfo,
      options,
      strategy,
      startTime
    )

    return optimizedContext
  }

  private determineStrategy(contextInfo: ContextInfo, options: OptimizationOptions): OptimizationStrategy {
    const defaultStrategy: OptimizationStrategy = {
      approach: 'adaptive',
      prioritization: 'mixed',
      compressionLevel: 'moderate',
      includeTests: false,
      includeDocs: false,
      maxDepth: 3,
      confidence: 0.7
    }

    if (options.strategy) {
      return { ...defaultStrategy, ...options.strategy }
    }

    // Adaptive strategy based on context
    const fileCount = contextInfo.relatedFiles.length
    const hasSelection = !!contextInfo.selectionContext
    const hasRecentActivity = contextInfo.recentActivity.recentFiles.length > 0

    let approach: OptimizationStrategy['approach'] = 'adaptive'
    let prioritization: OptimizationStrategy['prioritization'] = 'mixed'
    let compressionLevel: OptimizationStrategy['compressionLevel'] = 'moderate'

    if (fileCount > 20) {
      approach = 'focused'
      compressionLevel = 'aggressive'
    } else if (fileCount < 5) {
      approach = 'comprehensive'
      compressionLevel = 'light'
    }

    if (hasSelection) {
      prioritization = 'relevance'
      approach = 'focused'
    }

    if (hasRecentActivity) {
      prioritization = 'recency'
    }

    return {
      ...defaultStrategy,
      approach,
      prioritization,
      compressionLevel
    }
  }

  private async collectCandidates(contextInfo: ContextInfo, strategy: OptimizationStrategy): Promise<ContentCandidate[]> {
    const candidates: ContentCandidate[] = []

    // Add current file
    candidates.push({
      type: 'file',
      filePath: contextInfo.currentFile.path,
      content: contextInfo.currentFile.content || '',
      priority: 1.0,
      reason: 'Current file'
    })

    // Add related files
    for (const relatedFile of contextInfo.relatedFiles) {
      if (relatedFile.relevanceScore >= strategy.confidence) {
        try {
          const content = await this.fileSystem.readFile(relatedFile.path)
          candidates.push({
            type: 'file',
            filePath: relatedFile.path,
            content,
            priority: relatedFile.relevanceScore,
            reason: relatedFile.reason
          })
        } catch (error) {
          console.warn(`Could not read file ${relatedFile.path}:`, error)
        }
      }
    }

    // Add focused content based on selection or cursor
    if (contextInfo.selectionContext) {
      candidates.push(...await this.extractSelectionCandidates(contextInfo))
    } else if (contextInfo.cursorContext) {
      candidates.push(...await this.extractCursorCandidates(contextInfo))
    }

    // Add symbol-specific content
    candidates.push(...await this.extractSymbolCandidates(contextInfo, strategy))

    return candidates
  }

  private async extractSelectionCandidates(contextInfo: ContextInfo): Promise<ContentCandidate[]> {
    const candidates: ContentCandidate[] = []
    const selection = contextInfo.selectionContext!

    // Add selected content as high priority
    candidates.push({
      type: 'snippet',
      filePath: contextInfo.currentFile.path,
      content: selection.selectedText,
      startLine: selection.startLine,
      endLine: selection.endLine,
      priority: 0.95,
      reason: 'Selected text'
    })

    // Add surrounding context
    const surroundingLines = 10
    const fullContent = contextInfo.currentFile.content || ''
    const lines = fullContent.split('\n')
    
    const contextStart = Math.max(0, selection.startLine - surroundingLines)
    const contextEnd = Math.min(lines.length - 1, selection.endLine + surroundingLines)
    
    if (contextStart < selection.startLine || contextEnd > selection.endLine) {
      const surroundingContent = lines.slice(contextStart, contextEnd + 1).join('\n')
      candidates.push({
        type: 'snippet',
        filePath: contextInfo.currentFile.path,
        content: surroundingContent,
        startLine: contextStart,
        endLine: contextEnd,
        priority: 0.8,
        reason: 'Surrounding context'
      })
    }

    return candidates
  }

  private async extractCursorCandidates(contextInfo: ContextInfo): Promise<ContentCandidate[]> {
    const candidates: ContentCandidate[] = []
    const cursor = contextInfo.cursorContext

    // Add current function/class if available
    if (cursor.isInFunction || cursor.isInClass) {
      const symbolContext = await this.findContainingSymbol(
        contextInfo.currentFile,
        cursor.line,
        cursor.column
      )
      
      if (symbolContext) {
        candidates.push({
          type: symbolContext.type === 'function' ? 'function' : 'class',
          filePath: contextInfo.currentFile.path,
          content: symbolContext.content,
          startLine: symbolContext.startLine,
          endLine: symbolContext.endLine,
          priority: 0.9,
          reason: `Current ${symbolContext.type}`
        })
      }
    }

    // Add surrounding lines
    const radius = 20
    const fullContent = contextInfo.currentFile.content || ''
    const lines = fullContent.split('\n')
    
    const start = Math.max(0, cursor.line - radius)
    const end = Math.min(lines.length - 1, cursor.line + radius)
    
    const surroundingContent = lines.slice(start, end + 1).join('\n')
    candidates.push({
      type: 'snippet',
      filePath: contextInfo.currentFile.path,
      content: surroundingContent,
      startLine: start,
      endLine: end,
      priority: 0.7,
      reason: 'Cursor vicinity'
    })

    return candidates
  }

  private async extractSymbolCandidates(contextInfo: ContextInfo, strategy: OptimizationStrategy): Promise<ContentCandidate[]> {
    const candidates: ContentCandidate[] = []

    // Add high-importance symbols from current file
    for (const symbol of contextInfo.currentFile.symbols) {
      if (symbol.isExported || symbol.complexity > 3) {
        const symbolContent = await this.extractSymbolContent(
          contextInfo.currentFile.path,
          symbol
        )
        
        if (symbolContent) {
          candidates.push({
            type: 'symbol',
            filePath: contextInfo.currentFile.path,
            content: symbolContent.content,
            startLine: symbolContent.startLine,
            endLine: symbolContent.endLine,
            priority: symbol.isExported ? 0.8 : 0.6,
            reason: `${symbol.type}: ${symbol.name}`
          })
        }
      }
    }

    return candidates
  }

  private async scoreAndRankContent(
    candidates: ContentCandidate[],
    contextInfo: ContextInfo,
    strategy: OptimizationStrategy
  ): Promise<ScoredCandidate[]> {
    const scoredCandidates: ScoredCandidate[] = []

    for (const candidate of candidates) {
      const tokenCount = this.tokenCounter.count(candidate.content)
      const score = this.calculateContentScore(candidate, contextInfo, strategy)
      
      scoredCandidates.push({
        ...candidate,
        score,
        tokenCount,
        efficiency: score / Math.max(tokenCount, 1)
      })
    }

    // Sort by score and efficiency
    return scoredCandidates.sort((a, b) => {
      if (strategy.prioritization === 'size') {
        return a.tokenCount - b.tokenCount
      }
      return b.score - a.score
    })
  }

  private calculateContentScore(
    candidate: ContentCandidate,
    contextInfo: ContextInfo,
    strategy: OptimizationStrategy
  ): number {
    let score = candidate.priority

    // Boost score based on type
    if (candidate.type === 'file' && candidate.filePath === contextInfo.currentFile.path) {
      score *= 1.2
    } else if (candidate.type === 'function' || candidate.type === 'class') {
      score *= 1.1
    } else if (candidate.type === 'snippet') {
      score *= 1.05
    }

    // Consider recency if available
    if (strategy.prioritization === 'recency') {
      const isRecent = contextInfo.recentActivity.recentFiles.includes(candidate.filePath)
      if (isRecent) {
        score *= 1.15
      }
    }

    // Consider file size efficiency
    const tokenCount = this.tokenCounter.count(candidate.content)
    if (tokenCount > 1000) {
      score *= 0.9 // Slight penalty for very large content
    }

    return score
  }

  private async selectOptimalContent(
    candidates: ScoredCandidate[],
    options: OptimizationOptions,
    strategy: OptimizationStrategy
  ): Promise<ScoredCandidate[]> {
    const selected: ScoredCandidate[] = []
    let totalTokens = 0
    const reservedTokens = Math.floor(options.maxTokens * 0.1) // Reserve 10% for metadata

    for (const candidate of candidates) {
      if (totalTokens + candidate.tokenCount <= options.maxTokens - reservedTokens) {
        selected.push(candidate)
        totalTokens += candidate.tokenCount
      } else if (strategy.compressionLevel !== 'none') {
        // Try compression
        const compressed = await this.compressCandidate(candidate, strategy)
        if (totalTokens + compressed.tokenCount <= options.maxTokens - reservedTokens) {
          selected.push(compressed)
          totalTokens += compressed.tokenCount
        }
      }

      // Stop if we've reached a reasonable limit
      if (selected.length >= 50) break
    }

    return selected
  }

  private async compressContent(
    candidates: ScoredCandidate[],
    strategy: OptimizationStrategy
  ): Promise<ScoredCandidate[]> {
    if (strategy.compressionLevel === 'none') {
      return candidates
    }

    const compressed: ScoredCandidate[] = []
    
    for (const candidate of candidates) {
      const compressedCandidate = await this.compressCandidate(candidate, strategy)
      compressed.push(compressedCandidate)
    }

    return compressed
  }

  private async compressCandidate(
    candidate: ScoredCandidate,
    strategy: OptimizationStrategy
  ): Promise<ScoredCandidate> {
    const options: CompressionOptions = {
      removeComments: strategy.compressionLevel === 'aggressive',
      removeBlankLines: strategy.compressionLevel !== 'light',
      minimizeWhitespace: strategy.compressionLevel === 'aggressive',
      extractSignatures: strategy.compressionLevel === 'aggressive',
      summarizeDocstrings: strategy.compressionLevel !== 'light',
      skipImplementations: strategy.compressionLevel === 'aggressive' && candidate.type === 'function'
    }

    let compressed = candidate.content

    if (options.removeComments) {
      compressed = this.removeComments(compressed)
    }

    if (options.removeBlankLines) {
      compressed = this.removeBlankLines(compressed)
    }

    if (options.minimizeWhitespace) {
      compressed = this.minimizeWhitespace(compressed)
    }

    if (options.extractSignatures && (candidate.type === 'function' || candidate.type === 'class')) {
      compressed = this.extractSignatures(compressed)
    }

    return {
      ...candidate,
      content: compressed,
      tokenCount: this.tokenCounter.count(compressed)
    }
  }

  private removeComments(content: string): string {
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // Block comments
      .replace(/\/\/.*$/gm, '') // Line comments
      .replace(/#.*$/gm, '') // Python comments
  }

  private removeBlankLines(content: string): string {
    return content
      .split('\n')
      .filter(line => line.trim().length > 0)
      .join('\n')
  }

  private minimizeWhitespace(content: string): string {
    return content
      .replace(/\s+/g, ' ')
      .replace(/\s*{\s*/g, '{')
      .replace(/\s*}\s*/g, '}')
      .replace(/\s*;\s*/g, ';')
      .trim()
  }

  private extractSignatures(content: string): string {
    const lines = content.split('\n')
    const signatures: string[] = []
    
    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed.includes('function ') || 
          trimmed.includes('class ') ||
          trimmed.includes('def ') ||
          trimmed.includes('interface ') ||
          trimmed.includes('type ')) {
        signatures.push(trimmed)
      }
    }
    
    return signatures.join('\n')
  }

  private async buildOptimizedContext(
    selection: ScoredCandidate[],
    contextInfo: ContextInfo,
    options: OptimizationOptions,
    strategy: OptimizationStrategy,
    startTime: number
  ): Promise<OptimizedContext> {
    const primaryContext: ContextBlock[] = []
    const secondaryContext: ContextBlock[] = []

    let primaryTokens = 0
    let secondaryTokens = 0

    for (const candidate of selection) {
      const block: ContextBlock = {
        type: candidate.type,
        filePath: candidate.filePath,
        content: candidate.content,
        startLine: candidate.startLine,
        endLine: candidate.endLine,
        relevanceScore: candidate.score,
        reason: candidate.reason,
        priority: candidate.priority,
        tokenCount: candidate.tokenCount,
        language: this.detectLanguage(candidate.filePath),
        isComplete: this.isCompleteBlock(candidate)
      }

      if (candidate.priority >= 0.7) {
        primaryContext.push(block)
        primaryTokens += candidate.tokenCount
      } else {
        secondaryContext.push(block)
        secondaryTokens += candidate.tokenCount
      }
    }

    const totalTokens = primaryTokens + secondaryTokens
    const executionTime = Date.now() - startTime

    const metadata: ContextMetadata = {
      totalFiles: contextInfo.relatedFiles.length + 1,
      selectedFiles: new Set(selection.map(s => s.filePath)).size,
      totalTokens: options.maxTokens,
      usedTokens: totalTokens,
      remainingTokens: options.maxTokens - totalTokens,
      compressionRatio: this.calculateCompressionRatio(selection),
      selectionCriteria: this.buildSelectionCriteria(options, strategy),
      executionTime
    }

    const tokenUsage: TokenUsage = {
      instruction: 0, // Would be calculated based on user intent
      primaryContext: primaryTokens,
      secondaryContext: secondaryTokens,
      metadata: this.tokenCounter.count(JSON.stringify(metadata)),
      reserved: Math.floor(options.maxTokens * 0.1),
      available: options.maxTokens - totalTokens,
      total: options.maxTokens
    }

    return {
      primaryContext,
      secondaryContext,
      metadata,
      tokenUsage,
      optimizationStrategy: strategy
    }
  }

  private async findContainingSymbol(file: any, line: number, column: number): Promise<any> {
    // This would find the function or class containing the cursor position
    // For now, return null
    return null
  }

  private async extractSymbolContent(filePath: string, symbol: any): Promise<any> {
    // This would extract the full content of a symbol
    // For now, return null
    return null
  }

  private detectLanguage(filePath: string): string {
    const ext = this.fileSystem.extname(filePath).toLowerCase()
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.c': 'c',
      '.cpp': 'cpp'
    }
    return languageMap[ext] || 'text'
  }

  private isCompleteBlock(candidate: ScoredCandidate): boolean {
    return candidate.type === 'file' || 
           (candidate.startLine !== undefined && candidate.endLine !== undefined)
  }

  private calculateCompressionRatio(selection: ScoredCandidate[]): number {
    // This would calculate how much the content was compressed
    return 1.0 // No compression for now
  }

  private buildSelectionCriteria(options: OptimizationOptions, strategy: OptimizationStrategy): SelectionCriteria {
    return {
      minRelevanceScore: strategy.confidence,
      maxFileSize: 100000, // 100KB
      maxTotalFiles: 20,
      includePatterns: [],
      excludePatterns: ['node_modules/**', '.git/**'],
      languagePreferences: [],
      contextWindow: options.maxTokens
    }
  }
}

interface ContentCandidate {
  type: 'file' | 'function' | 'class' | 'symbol' | 'snippet'
  filePath: string
  content: string
  startLine?: number
  endLine?: number
  priority: number
  reason: string
}

interface ScoredCandidate extends ContentCandidate {
  score: number
  tokenCount: number
  efficiency: number
}

class TokenCounter {
  count(text: string): number {
    // Simple token counting - in practice would use a proper tokenizer
    return Math.ceil(text.length / 4)
  }
}