import { Logger, LogLevel } from '../../../utils/logger'

describe('Logger', () => {
  let logger: Logger

  beforeEach(() => {
    logger = new Logger('Test Logger')
  })

  afterEach(() => {
    logger.dispose()
  })

  describe('log level filtering', () => {
    it('should respect log level settings', () => {
      const mockAppendLine = jest.fn()
      logger['outputChannel'].appendLine = mockAppendLine
      
      logger.setLogLevel(LogLevel.WARN)
      
      logger.debug('debug message')
      logger.info('info message')
      logger.warn('warn message')
      logger.error('error message')
      
      expect(mockAppendLine).toHaveBeenCalledTimes(2)
      expect(mockAppendLine).toHaveBeenCalledWith(expect.stringContaining('warn message'))
      expect(mockAppendLine).toHaveBeenCalledWith(expect.stringContaining('error message'))
    })
  })

  describe('log entry storage', () => {
    it('should store log entries', () => {
      logger.info('test message', { key: 'value' })
      
      const entries = logger.getLogEntries()
      expect(entries).toHaveLength(1)
      expect(entries[0].message).toBe('test message')
      expect(entries[0].level).toBe(LogLevel.INFO)
      expect(entries[0].context).toEqual({ key: 'value' })
    })

    it('should limit stored entries', () => {
      logger['maxLogEntries'] = 3
      
      logger.info('message 1')
      logger.info('message 2')
      logger.info('message 3')
      logger.info('message 4')
      
      const entries = logger.getLogEntries()
      expect(entries).toHaveLength(3)
      expect(entries[0].message).toBe('message 2')
      expect(entries[2].message).toBe('message 4')
    })
  })

  describe('error logging', () => {
    it('should log errors with stack traces', () => {
      const error = new Error('Test error')
      logger.error('Error occurred', error)
      
      const entries = logger.getLogEntries()
      expect(entries).toHaveLength(1)
      expect(entries[0].error).toBe(error)
    })
  })

  describe('log filtering by level', () => {
    it('should filter entries by log level', () => {
      logger.debug('debug message')
      logger.info('info message')
      logger.warn('warn message')
      logger.error('error message')
      
      const errorEntries = logger.getLogEntriesByLevel(LogLevel.ERROR)
      expect(errorEntries).toHaveLength(1)
      expect(errorEntries[0].message).toBe('error message')
    })
  })

  describe('log export', () => {
    it('should export logs as formatted string', () => {
      logger.info('test message')
      
      const exported = logger.exportLogs()
      expect(exported).toContain('test message')
      expect(exported).toContain('[INFO]')
    })
  })

  describe('log clearing', () => {
    it('should clear all log entries', () => {
      logger.info('test message')
      expect(logger.getLogEntries()).toHaveLength(1)
      
      logger.clear()
      expect(logger.getLogEntries()).toHaveLength(0)
    })
  })
})