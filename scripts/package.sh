#!/bin/bash

# Package and validate extension for Natural Language Task Transformer
set -e

echo "🚀 Starting extension packaging process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is required but not installed"
        exit 1
    fi
    
    if ! command -v npx &> /dev/null; then
        print_error "npx is required but not installed"
        exit 1
    fi
    
    print_status "All dependencies are available"
}

# Clean previous builds
clean_build() {
    print_status "Cleaning previous builds..."
    rm -rf out/
    rm -rf *.vsix
    print_status "Clean completed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm ci --silent
    print_status "Dependencies installed"
}

# Install VSCE if not available
install_vsce() {
    print_status "Checking VSCE..."
    if ! command -v vsce &> /dev/null; then
        print_warning "VSCE not found, installing..."
        npm install -g @vscode/vsce
        print_status "VSCE installed"
    else
        print_status "VSCE is available"
    fi
}

# Run linting
run_lint() {
    print_status "Running linter..."
    npm run lint
    print_status "Linting completed"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    if npm test; then
        print_status "All tests passed"
    else
        print_error "Tests failed"
        exit 1
    fi
}

# Compile TypeScript
compile_typescript() {
    print_status "Compiling TypeScript..."
    npm run compile
    print_status "TypeScript compilation completed"
}

# Validate package.json
validate_package() {
    print_status "Validating package.json..."
    
    # Check required fields
    if ! grep -q '"name"' package.json; then
        print_error "package.json missing 'name' field"
        exit 1
    fi
    
    if ! grep -q '"version"' package.json; then
        print_error "package.json missing 'version' field"
        exit 1
    fi
    
    if ! grep -q '"engines"' package.json; then
        print_error "package.json missing 'engines' field"
        exit 1
    fi
    
    if ! grep -q '"main"' package.json; then
        print_error "package.json missing 'main' field"
        exit 1
    fi
    
    print_status "package.json validation passed"
}

# Validate extension structure
validate_structure() {
    print_status "Validating extension structure..."
    
    # Check required files
    if [ ! -f "src/extension.ts" ]; then
        print_error "Missing src/extension.ts"
        exit 1
    fi
    
    if [ ! -f "README.md" ]; then
        print_error "Missing README.md"
        exit 1
    fi
    
    if [ ! -f "CHANGELOG.md" ]; then
        print_error "Missing CHANGELOG.md"
        exit 1
    fi
    
    if [ ! -d "out" ]; then
        print_error "Missing compiled output directory"
        exit 1
    fi
    
    print_status "Extension structure validation passed"
}

# Package extension
package_extension() {
    print_status "Packaging extension..."
    
    # Get version from package.json
    VERSION=$(node -p "require('./package.json').version")
    print_status "Packaging version: $VERSION"
    
    # Package with VSCE
    vsce package --out "natural-language-task-transformer-${VERSION}.vsix"
    
    if [ -f "natural-language-task-transformer-${VERSION}.vsix" ]; then
        print_status "Extension packaged successfully: natural-language-task-transformer-${VERSION}.vsix"
    else
        print_error "Failed to create package"
        exit 1
    fi
}

# Validate packaged extension
validate_package_contents() {
    print_status "Validating package contents..."
    
    VERSION=$(node -p "require('./package.json').version")
    PACKAGE_FILE="natural-language-task-transformer-${VERSION}.vsix"
    
    if [ ! -f "$PACKAGE_FILE" ]; then
        print_error "Package file not found: $PACKAGE_FILE"
        exit 1
    fi
    
    # Check package size
    SIZE=$(stat -f%z "$PACKAGE_FILE" 2>/dev/null || stat -c%s "$PACKAGE_FILE")
    SIZE_MB=$((SIZE / 1024 / 1024))
    
    if [ $SIZE_MB -gt 50 ]; then
        print_warning "Package size is large: ${SIZE_MB}MB"
    else
        print_status "Package size: ${SIZE_MB}MB"
    fi
    
    print_status "Package validation completed"
}

# Test installation
test_installation() {
    print_status "Testing installation..."
    
    VERSION=$(node -p "require('./package.json').version")
    PACKAGE_FILE="natural-language-task-transformer-${VERSION}.vsix"
    
    # Test if package can be installed (dry run)
    if command -v code &> /dev/null; then
        print_status "VS Code CLI available for testing"
        # Note: This would actually install the extension
        # code --install-extension "$PACKAGE_FILE" --force
        print_status "Installation test completed (dry run)"
    else
        print_warning "VS Code CLI not available, skipping installation test"
    fi
}

# Generate checksums
generate_checksums() {
    print_status "Generating checksums..."
    
    VERSION=$(node -p "require('./package.json').version")
    PACKAGE_FILE="natural-language-task-transformer-${VERSION}.vsix"
    
    if command -v shasum &> /dev/null; then
        shasum -a 256 "$PACKAGE_FILE" > "${PACKAGE_FILE}.sha256"
        print_status "SHA256 checksum generated: ${PACKAGE_FILE}.sha256"
    elif command -v sha256sum &> /dev/null; then
        sha256sum "$PACKAGE_FILE" > "${PACKAGE_FILE}.sha256"
        print_status "SHA256 checksum generated: ${PACKAGE_FILE}.sha256"
    else
        print_warning "SHA256 tool not available, skipping checksum generation"
    fi
}

# Create release notes
create_release_notes() {
    print_status "Creating release notes..."
    
    VERSION=$(node -p "require('./package.json').version")
    RELEASE_NOTES="release-notes-${VERSION}.md"
    
    cat > "$RELEASE_NOTES" << EOF
# Release Notes - Version ${VERSION}

## What's New

- See [CHANGELOG.md](CHANGELOG.md) for detailed changes

## Installation

### From VS Code Marketplace
1. Open VS Code
2. Go to Extensions view (Ctrl+Shift+X)
3. Search for "Natural Language Task Transformer"
4. Click Install

### From VSIX File
1. Download the .vsix file
2. Open VS Code
3. Go to Extensions view (Ctrl+Shift+X)
4. Click "..." menu → "Install from VSIX..."
5. Select the downloaded .vsix file

## Requirements

- VS Code 1.82.0 or later
- OpenAI or Anthropic API key

## Support

- [Documentation](https://github.com/yourusername/natural-language-task-transformer/blob/main/docs/README.md)
- [Issues](https://github.com/yourusername/natural-language-task-transformer/issues)
- [Discussions](https://github.com/yourusername/natural-language-task-transformer/discussions)

## Package Information

- Version: ${VERSION}
- Package: natural-language-task-transformer-${VERSION}.vsix
- Size: $(stat -f%z "natural-language-task-transformer-${VERSION}.vsix" 2>/dev/null || stat -c%s "natural-language-task-transformer-${VERSION}.vsix" | awk '{print int($1/1024/1024) "MB"}')
- Built: $(date)
EOF

    print_status "Release notes created: $RELEASE_NOTES"
}

# Main execution
main() {
    echo "🎯 Natural Language Task Transformer - Package Script"
    echo "=================================================="
    
    # Set script directory as working directory
    cd "$(dirname "$0")/.."
    
    # Run all packaging steps
    check_dependencies
    clean_build
    install_dependencies
    install_vsce
    validate_package
    run_lint
    run_tests
    compile_typescript
    validate_structure
    package_extension
    validate_package_contents
    test_installation
    generate_checksums
    create_release_notes
    
    echo ""
    echo "🎉 Extension packaging completed successfully!"
    echo "=================================================="
    
    VERSION=$(node -p "require('./package.json').version")
    PACKAGE_FILE="natural-language-task-transformer-${VERSION}.vsix"
    
    echo "📦 Package: $PACKAGE_FILE"
    echo "📄 Release Notes: release-notes-${VERSION}.md"
    echo "🔐 Checksum: ${PACKAGE_FILE}.sha256"
    echo ""
    echo "Next steps:"
    echo "1. Test the extension manually"
    echo "2. Upload to VS Code Marketplace"
    echo "3. Create GitHub release"
    echo "4. Update documentation"
    echo ""
    echo "Happy coding! 🚀"
}

# Run main function
main "$@"