import { PythonAnalyzer, PythonSymbol, PythonAnalysisResult } from './pythonAnalyzer'
import { ConnectionManager } from '../database/connectionManager'
import { FileSystemService } from '../fileSystemService'

export interface PythonSymbolInfo {
  id?: number
  fileId: number
  name: string
  type: string
  line: number
  column: number
  endLine: number
  endColumn: number
  scope: string
  signature?: string
  documentation?: string
  decorators: string[]
  isAsync: boolean
  isPrivate: boolean
  isStatic: boolean
  isClassMethod: boolean
  isProperty: boolean
  parentClass?: string
  complexity: number
  references: number
  usages: PythonSymbolUsage[]
}

export interface PythonSymbolUsage {
  filePath: string
  line: number
  column: number
  usageType: 'declaration' | 'reference' | 'call' | 'import' | 'inheritance' | 'decorator'
  context?: string
}

export interface PythonSymbolIndex {
  symbols: Map<string, PythonSymbolInfo[]>
  globalSymbols: Map<string, PythonSymbolInfo>
  classSymbols: Map<string, PythonSymbolInfo[]>
  functionSymbols: Map<string, PythonSymbolInfo[]>
  variableSymbols: Map<string, PythonSymbolInfo[]>
  constantSymbols: Map<string, PythonSymbolInfo[]>
  symbolsByFile: Map<string, PythonSymbolInfo[]>
  decoratedSymbols: Map<string, PythonSymbolInfo[]>
}

export interface PythonSymbolSearchResult {
  symbol: PythonSymbolInfo
  filePath: string
  score: number
  context: string
  matchType: 'exact' | 'partial' | 'fuzzy'
}

export class PythonSymbolExtractor {
  private db: ConnectionManager
  private fileSystem: FileSystemService
  private pythonAnalyzer: PythonAnalyzer
  private symbolIndex: PythonSymbolIndex

  constructor(
    db: ConnectionManager,
    fileSystem: FileSystemService,
    pythonAnalyzer: PythonAnalyzer
  ) {
    this.db = db
    this.fileSystem = fileSystem
    this.pythonAnalyzer = pythonAnalyzer
    this.symbolIndex = {
      symbols: new Map(),
      globalSymbols: new Map(),
      classSymbols: new Map(),
      functionSymbols: new Map(),
      variableSymbols: new Map(),
      constantSymbols: new Map(),
      symbolsByFile: new Map(),
      decoratedSymbols: new Map()
    }
  }

  async extractSymbolsFromFile(filePath: string): Promise<PythonSymbolInfo[]> {
    const analysisResult = await this.pythonAnalyzer.analyzeFile(filePath)
    const fileRecord = await this.getFileRecord(filePath)
    
    if (!fileRecord) {
      throw new Error(`File not found in database: ${filePath}`)
    }

    const symbols: PythonSymbolInfo[] = []
    
    for (const pythonSymbol of analysisResult.symbols) {
      const symbolInfo = await this.convertPythonSymbolToSymbolInfo(pythonSymbol, fileRecord.id)
      symbols.push(symbolInfo)
    }

    await this.saveSymbolsToDatabase(symbols)
    return symbols
  }

  async extractSymbolsFromProject(projectRoot: string, filePaths: string[]): Promise<PythonSymbolIndex> {
    const allSymbols: PythonSymbolInfo[] = []

    for (const filePath of filePaths) {
      if (!this.isPythonFile(filePath)) {
        continue
      }

      try {
        const fileRecord = await this.getFileRecord(filePath)
        if (!fileRecord) continue

        const analysisResult = await this.pythonAnalyzer.analyzeFile(filePath)
        const symbols = await this.processFileSymbols(analysisResult, fileRecord.id)
        allSymbols.push(...symbols)
      } catch (error) {
        console.error(`Error processing Python file ${filePath}:`, error)
      }
    }

    await this.saveSymbolsToDatabase(allSymbols)
    this.buildSymbolIndex(allSymbols)
    
    return this.symbolIndex
  }

  private async convertPythonSymbolToSymbolInfo(
    pythonSymbol: PythonSymbol,
    fileId: number
  ): Promise<PythonSymbolInfo> {
    const complexity = this.calculateSymbolComplexity(pythonSymbol)
    const references = await this.countSymbolReferences(pythonSymbol.name, fileId)
    const usages = await this.findSymbolUsages(pythonSymbol.name, fileId)

    return {
      fileId,
      name: pythonSymbol.name,
      type: pythonSymbol.type,
      line: pythonSymbol.line,
      column: pythonSymbol.column,
      endLine: pythonSymbol.endLine,
      endColumn: pythonSymbol.endColumn,
      scope: pythonSymbol.scope,
      signature: pythonSymbol.signature,
      documentation: pythonSymbol.documentation,
      decorators: pythonSymbol.decorators,
      isAsync: pythonSymbol.isAsync,
      isPrivate: pythonSymbol.isPrivate,
      isStatic: pythonSymbol.isStatic,
      isClassMethod: pythonSymbol.isClassMethod,
      isProperty: pythonSymbol.isProperty,
      parentClass: pythonSymbol.parentClass,
      complexity,
      references,
      usages
    }
  }

  private async processFileSymbols(
    analysis: PythonAnalysisResult,
    fileId: number
  ): Promise<PythonSymbolInfo[]> {
    const symbols: PythonSymbolInfo[] = []
    
    for (const pythonSymbol of analysis.symbols) {
      const symbolInfo = await this.convertPythonSymbolToSymbolInfo(pythonSymbol, fileId)
      symbols.push(symbolInfo)
    }

    return symbols
  }

  private async saveSymbolsToDatabase(symbols: PythonSymbolInfo[]): Promise<void> {
    await this.db.transaction(async (tx) => {
      for (const symbol of symbols) {
        // Clear existing symbols for this file
        await tx.execute(
          tx.delete()
            .from('symbols')
            .where('file_id', '=', symbol.fileId)
        )

        // Insert new symbol
        await tx.execute(
          tx.insert()
            .into('symbols')
            .columns([
              'file_id', 'name', 'type', 'line_start', 'line_end',
              'column_start', 'column_end', 'scope', 'signature',
              'documentation', 'is_exported'
            ])
            .values([
              symbol.fileId,
              symbol.name,
              symbol.type,
              symbol.line,
              symbol.endLine,
              symbol.column,
              symbol.endColumn,
              symbol.scope,
              symbol.signature,
              symbol.documentation,
              !symbol.isPrivate ? 1 : 0 // Python doesn't have explicit exports, use public/private
            ])
        )
      }
    })
  }

  private buildSymbolIndex(symbols: PythonSymbolInfo[]): void {
    this.symbolIndex.symbols.clear()
    this.symbolIndex.globalSymbols.clear()
    this.symbolIndex.classSymbols.clear()
    this.symbolIndex.functionSymbols.clear()
    this.symbolIndex.variableSymbols.clear()
    this.symbolIndex.constantSymbols.clear()
    this.symbolIndex.symbolsByFile.clear()
    this.symbolIndex.decoratedSymbols.clear()

    for (const symbol of symbols) {
      // Index by name
      if (!this.symbolIndex.symbols.has(symbol.name)) {
        this.symbolIndex.symbols.set(symbol.name, [])
      }
      this.symbolIndex.symbols.get(symbol.name)!.push(symbol)

      // Index global symbols
      if (symbol.scope === 'global') {
        this.symbolIndex.globalSymbols.set(symbol.name, symbol)
      }

      // Index by type
      switch (symbol.type) {
        case 'class':
          if (!this.symbolIndex.classSymbols.has(symbol.name)) {
            this.symbolIndex.classSymbols.set(symbol.name, [])
          }
          this.symbolIndex.classSymbols.get(symbol.name)!.push(symbol)
          break
        case 'function':
        case 'method':
          if (!this.symbolIndex.functionSymbols.has(symbol.name)) {
            this.symbolIndex.functionSymbols.set(symbol.name, [])
          }
          this.symbolIndex.functionSymbols.get(symbol.name)!.push(symbol)
          break
        case 'variable':
          if (!this.symbolIndex.variableSymbols.has(symbol.name)) {
            this.symbolIndex.variableSymbols.set(symbol.name, [])
          }
          this.symbolIndex.variableSymbols.get(symbol.name)!.push(symbol)
          break
        case 'constant':
          if (!this.symbolIndex.constantSymbols.has(symbol.name)) {
            this.symbolIndex.constantSymbols.set(symbol.name, [])
          }
          this.symbolIndex.constantSymbols.get(symbol.name)!.push(symbol)
          break
      }

      // Index by file
      const fileKey = symbol.fileId.toString()
      if (!this.symbolIndex.symbolsByFile.has(fileKey)) {
        this.symbolIndex.symbolsByFile.set(fileKey, [])
      }
      this.symbolIndex.symbolsByFile.get(fileKey)!.push(symbol)

      // Index by decorators
      for (const decorator of symbol.decorators) {
        if (!this.symbolIndex.decoratedSymbols.has(decorator)) {
          this.symbolIndex.decoratedSymbols.set(decorator, [])
        }
        this.symbolIndex.decoratedSymbols.get(decorator)!.push(symbol)
      }
    }
  }

  async searchSymbols(query: string, options: {
    type?: string
    scope?: string
    hasDecorator?: string
    isAsync?: boolean
    isPrivate?: boolean
    limit?: number
  } = {}): Promise<PythonSymbolSearchResult[]> {
    const results: PythonSymbolSearchResult[] = []
    const searchQuery = query.toLowerCase()
    
    const queryBuilder = this.db.query()
      .select('s.*, f.path as file_path')
      .from('symbols s')
      .innerJoin('files f', 'f.id = s.file_id')
      .where('s.name', 'LIKE', `%${query}%`)

    if (options.type) {
      queryBuilder.where('s.type', '=', options.type)
    }

    if (options.scope) {
      queryBuilder.where('s.scope', '=', options.scope)
    }

    if (options.limit) {
      queryBuilder.limit(options.limit)
    }

    const symbols = await this.db.findMany(queryBuilder)

    for (const symbol of symbols) {
      const score = this.calculateSearchScore(symbol.name, searchQuery)
      const context = await this.getSymbolContext(symbol.file_id, symbol.line_start)
      const matchType = this.determineMatchType(symbol.name, query)
      
      results.push({
        symbol: {
          id: symbol.id,
          fileId: symbol.file_id,
          name: symbol.name,
          type: symbol.type,
          line: symbol.line_start,
          column: symbol.column_start,
          endLine: symbol.line_end,
          endColumn: symbol.column_end,
          scope: symbol.scope,
          signature: symbol.signature,
          documentation: symbol.documentation,
          decorators: [],
          isAsync: false,
          isPrivate: symbol.name.startsWith('_'),
          isStatic: false,
          isClassMethod: false,
          isProperty: false,
          complexity: 0,
          references: 0,
          usages: []
        },
        filePath: symbol.file_path,
        score,
        context,
        matchType
      })
    }

    return results.sort((a, b) => b.score - a.score)
  }

  async getSymbolDefinition(symbolName: string, filePath: string): Promise<PythonSymbolInfo | null> {
    const fileRecord = await this.getFileRecord(filePath)
    if (!fileRecord) return null

    const symbol = await this.db.findOne(
      this.db.query()
        .select('*')
        .from('symbols')
        .where('name', '=', symbolName)
        .where('file_id', '=', fileRecord.id)
    )

    if (!symbol) return null

    return {
      id: symbol.id,
      fileId: symbol.file_id,
      name: symbol.name,
      type: symbol.type,
      line: symbol.line_start,
      column: symbol.column_start,
      endLine: symbol.line_end,
      endColumn: symbol.column_end,
      scope: symbol.scope,
      signature: symbol.signature,
      documentation: symbol.documentation,
      decorators: [],
      isAsync: false,
      isPrivate: symbol.name.startsWith('_'),
      isStatic: false,
      isClassMethod: false,
      isProperty: false,
      complexity: 0,
      references: 0,
      usages: []
    }
  }

  async getClassMethods(className: string, filePath: string): Promise<PythonSymbolInfo[]> {
    const fileRecord = await this.getFileRecord(filePath)
    if (!fileRecord) return []

    const methods = await this.db.findMany(
      this.db.query()
        .select('*')
        .from('symbols')
        .where('file_id', '=', fileRecord.id)
        .where('scope', 'LIKE', `%${className}%`)
        .where('type', '=', 'method')
    )

    return methods.map(method => ({
      id: method.id,
      fileId: method.file_id,
      name: method.name,
      type: method.type,
      line: method.line_start,
      column: method.column_start,
      endLine: method.line_end,
      endColumn: method.column_end,
      scope: method.scope,
      signature: method.signature,
      documentation: method.documentation,
      decorators: [],
      isAsync: false,
      isPrivate: method.name.startsWith('_'),
      isStatic: false,
      isClassMethod: false,
      isProperty: false,
      parentClass: className,
      complexity: 0,
      references: 0,
      usages: []
    }))
  }

  async getSymbolsByDecorator(decorator: string): Promise<PythonSymbolInfo[]> {
    if (this.symbolIndex.decoratedSymbols.has(decorator)) {
      return this.symbolIndex.decoratedSymbols.get(decorator)!
    }
    return []
  }

  async getAsyncFunctions(filePath?: string): Promise<PythonSymbolInfo[]> {
    let queryBuilder = this.db.query()
      .select('s.*, f.path as file_path')
      .from('symbols s')
      .innerJoin('files f', 'f.id = s.file_id')
      .where('s.type', '=', 'function')
      .where('s.signature', 'LIKE', '%async%')

    if (filePath) {
      queryBuilder = queryBuilder.where('f.path', '=', filePath)
    }

    const symbols = await this.db.findMany(queryBuilder)
    
    return symbols.map(symbol => ({
      id: symbol.id,
      fileId: symbol.file_id,
      name: symbol.name,
      type: symbol.type,
      line: symbol.line_start,
      column: symbol.column_start,
      endLine: symbol.line_end,
      endColumn: symbol.column_end,
      scope: symbol.scope,
      signature: symbol.signature,
      documentation: symbol.documentation,
      decorators: [],
      isAsync: true,
      isPrivate: symbol.name.startsWith('_'),
      isStatic: false,
      isClassMethod: false,
      isProperty: false,
      complexity: 0,
      references: 0,
      usages: []
    }))
  }

  private isPythonFile(filePath: string): boolean {
    return filePath.endsWith('.py') || filePath.endsWith('.pyw')
  }

  private async getFileRecord(filePath: string): Promise<{ id: number } | null> {
    return await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', filePath)
    )
  }

  private calculateSymbolComplexity(symbol: PythonSymbol): number {
    let complexity = 1

    if (symbol.type === 'function') {
      complexity += 2
      if (symbol.arguments) {
        complexity += symbol.arguments.length * 0.5
      }
      if (symbol.isAsync) {
        complexity += 1
      }
    } else if (symbol.type === 'class') {
      complexity += 3
    }

    if (symbol.decorators.length > 0) {
      complexity += symbol.decorators.length * 0.5
    }

    return Math.round(complexity)
  }

  private async countSymbolReferences(symbolName: string, fileId: number): Promise<number> {
    const result = await this.db.findOne(
      this.db.query()
        .select('COUNT(*) as count')
        .from('file_contents fc')
        .where('fc.file_id', '=', fileId)
        .where('fc.content', 'LIKE', `%${symbolName}%`)
    )

    return result?.count || 0
  }

  private async findSymbolUsages(symbolName: string, fileId: number): Promise<PythonSymbolUsage[]> {
    // This is a simplified implementation
    // In a real implementation, you would use AST parsing to find actual usages
    return []
  }

  private calculateSearchScore(symbolName: string, query: string): number {
    const name = symbolName.toLowerCase()
    const q = query.toLowerCase()
    
    if (name === q) return 100
    if (name.startsWith(q)) return 90
    if (name.endsWith(q)) return 80
    if (name.includes(q)) return 70
    
    // Snake_case matching
    if (name.includes('_')) {
      const parts = name.split('_')
      for (const part of parts) {
        if (part.startsWith(q)) return 85
      }
    }
    
    // Fuzzy matching
    let score = 0
    let j = 0
    
    for (let i = 0; i < name.length && j < q.length; i++) {
      if (name[i] === q[j]) {
        score += 10
        j++
      }
    }
    
    return score
  }

  private async getSymbolContext(fileId: number, lineNumber: number): Promise<string> {
    const content = await this.db.findOne(
      this.db.query()
        .select('content')
        .from('file_contents')
        .where('file_id', '=', fileId)
    )

    if (!content) return ''

    const lines = content.content.split('\n')
    if (lineNumber > 0 && lineNumber <= lines.length) {
      return lines[lineNumber - 1].trim()
    }

    return ''
  }

  private determineMatchType(symbolName: string, query: string): 'exact' | 'partial' | 'fuzzy' {
    const name = symbolName.toLowerCase()
    const q = query.toLowerCase()

    if (name === q) return 'exact'
    if (name.includes(q) || q.includes(name)) return 'partial'
    return 'fuzzy'
  }

  getSymbolIndex(): PythonSymbolIndex {
    return this.symbolIndex
  }
}