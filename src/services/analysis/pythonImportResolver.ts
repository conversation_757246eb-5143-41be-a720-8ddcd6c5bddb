import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { PythonImport, PythonAnalyzer } from './pythonAnalyzer'

export interface ResolvedPythonImport {
  originalModule: string
  resolvedPath?: string
  isExternal: boolean
  isBuiltin: boolean
  isLocal: boolean
  moduleType: 'module' | 'package' | 'builtin' | 'external'
  importedNames: string[]
  alias?: string
  line: number
  column: number
  level: number
  isFromImport: boolean
}

export interface PythonModuleInfo {
  filePath: string
  relativePath: string
  moduleName: string
  packageName?: string
  isPackage: boolean
  isInitFile: boolean
  exports: string[]
  imports: ResolvedPythonImport[]
  dependencies: string[]
  submodules: string[]
}

export interface PythonImportGraph {
  modules: Map<string, PythonModuleInfo>
  packages: Map<string, string[]> // package name -> module paths
  dependencies: Map<string, string[]>
  reverseDependencies: Map<string, string[]>
  externalModules: Set<string>
  builtinModules: Set<string>
  unresolvedImports: Map<string, string[]>
}

export interface PythonResolutionOptions {
  projectRoot: string
  pythonPath?: string[]
  sitePackages?: string[]
  resolveStdLib?: boolean
  followInitFiles?: boolean
  cacheResults?: boolean
}

export class PythonImportResolver {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private pythonAnalyzer: PythonAnalyzer
  private cache: Map<string, ResolvedPythonImport> = new Map()
  private moduleCache: Map<string, PythonModuleInfo> = new Map()
  private options: Required<PythonResolutionOptions>

  private builtinModules = new Set([
    'os', 'sys', 'json', 'time', 'datetime', 'math', 'random', 'itertools',
    'collections', 'functools', 'operator', 'copy', 'pickle', 'sqlite3',
    'urllib', 'http', 'ssl', 'socket', 'threading', 'multiprocessing',
    'subprocess', 'shutil', 'glob', 'fnmatch', 'tempfile', 'pathlib',
    'argparse', 'logging', 'warnings', 'traceback', 'inspect', 'types',
    'typing', 're', 'string', 'io', 'csv', 'configparser', 'hashlib',
    'hmac', 'secrets', 'uuid', 'base64', 'binascii', 'struct', 'codecs',
    'locale', 'calendar', 'zoneinfo', 'decimal', 'fractions', 'statistics',
    'array', 'weakref', 'gc', 'ctypes', 'asyncio', 'concurrent',
    'contextlib', 'abc', 'atexit', 'signal', 'platform', 'errno'
  ])

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    pythonAnalyzer: PythonAnalyzer
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.pythonAnalyzer = pythonAnalyzer
    this.options = {
      projectRoot: '',
      pythonPath: [],
      sitePackages: [],
      resolveStdLib: true,
      followInitFiles: true,
      cacheResults: true
    }
  }

  async resolveImport(
    fromFile: string,
    importInfo: PythonImport,
    options?: Partial<PythonResolutionOptions>
  ): Promise<ResolvedPythonImport | null> {
    const resolveOptions = { ...this.options, ...options }
    const cacheKey = `${fromFile}:${importInfo.module}:${importInfo.level}`

    if (resolveOptions.cacheResults && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    const resolved = await this.performResolution(fromFile, importInfo, resolveOptions)
    
    if (resolved && resolveOptions.cacheResults) {
      this.cache.set(cacheKey, resolved)
    }

    return resolved
  }

  private async performResolution(
    fromFile: string,
    importInfo: PythonImport,
    options: Required<PythonResolutionOptions>
  ): Promise<ResolvedPythonImport | null> {
    const module = importInfo.module
    const level = importInfo.level

    // Check if it's a builtin module
    if (this.builtinModules.has(module)) {
      return this.createResolvedImport(importInfo, undefined, false, true, false, 'builtin')
    }

    // Handle relative imports
    if (level > 0 || module.startsWith('.')) {
      return await this.resolveRelativeImport(fromFile, importInfo, options)
    }

    // Handle absolute imports
    return await this.resolveAbsoluteImport(fromFile, importInfo, options)
  }

  private async resolveRelativeImport(
    fromFile: string,
    importInfo: PythonImport,
    options: Required<PythonResolutionOptions>
  ): Promise<ResolvedPythonImport | null> {
    const fromDir = this.fileSystem.dirname(fromFile)
    const module = importInfo.module
    const level = importInfo.level

    // Calculate the target directory based on the level
    let targetDir = fromDir
    for (let i = 0; i < level; i++) {
      targetDir = this.fileSystem.dirname(targetDir)
    }

    // If module is empty (e.g., "from . import something"), resolve to the target directory
    if (!module) {
      const initFile = this.fileSystem.joinPath(targetDir, '__init__.py')
      if (await this.fileSystem.exists(initFile)) {
        return this.createResolvedImport(importInfo, initFile, false, false, true, 'package')
      }
      return null
    }

    // Resolve the module path
    const moduleParts = module.split('.')
    let currentDir = targetDir

    for (let i = 0; i < moduleParts.length; i++) {
      const part = moduleParts[i]
      const isLast = i === moduleParts.length - 1

      if (isLast) {
        // Try .py file first
        const pyFile = this.fileSystem.joinPath(currentDir, `${part}.py`)
        if (await this.fileSystem.exists(pyFile)) {
          return this.createResolvedImport(importInfo, pyFile, false, false, true, 'module')
        }

        // Try package directory
        const packageDir = this.fileSystem.joinPath(currentDir, part)
        const initFile = this.fileSystem.joinPath(packageDir, '__init__.py')
        if (await this.fileSystem.exists(initFile)) {
          return this.createResolvedImport(importInfo, initFile, false, false, true, 'package')
        }
      } else {
        // Must be a package directory
        currentDir = this.fileSystem.joinPath(currentDir, part)
        const initFile = this.fileSystem.joinPath(currentDir, '__init__.py')
        if (!await this.fileSystem.exists(initFile)) {
          return null // Package not found
        }
      }
    }

    return null
  }

  private async resolveAbsoluteImport(
    fromFile: string,
    importInfo: PythonImport,
    options: Required<PythonResolutionOptions>
  ): Promise<ResolvedPythonImport | null> {
    const module = importInfo.module
    const moduleParts = module.split('.')

    // Search in project root first
    const projectResolved = await this.searchInDirectory(
      options.projectRoot, 
      moduleParts, 
      importInfo
    )
    if (projectResolved) {
      return projectResolved
    }

    // Search in Python path
    for (const pythonPath of options.pythonPath) {
      const pathResolved = await this.searchInDirectory(
        pythonPath, 
        moduleParts, 
        importInfo
      )
      if (pathResolved) {
        return pathResolved
      }
    }

    // Search in site-packages
    for (const sitePackage of options.sitePackages) {
      const siteResolved = await this.searchInDirectory(
        sitePackage, 
        moduleParts, 
        importInfo
      )
      if (siteResolved) {
        return siteResolved
      }
    }

    // Mark as external if not found
    return this.createResolvedImport(importInfo, undefined, true, false, false, 'external')
  }

  private async searchInDirectory(
    baseDir: string,
    moduleParts: string[],
    importInfo: PythonImport
  ): Promise<ResolvedPythonImport | null> {
    let currentDir = baseDir

    for (let i = 0; i < moduleParts.length; i++) {
      const part = moduleParts[i]
      const isLast = i === moduleParts.length - 1

      if (isLast) {
        // Try .py file first
        const pyFile = this.fileSystem.joinPath(currentDir, `${part}.py`)
        if (await this.fileSystem.exists(pyFile)) {
          const isLocal = this.isInProjectRoot(pyFile)
          return this.createResolvedImport(
            importInfo, 
            pyFile, 
            !isLocal, 
            false, 
            isLocal, 
            'module'
          )
        }

        // Try package directory
        const packageDir = this.fileSystem.joinPath(currentDir, part)
        const initFile = this.fileSystem.joinPath(packageDir, '__init__.py')
        if (await this.fileSystem.exists(initFile)) {
          const isLocal = this.isInProjectRoot(initFile)
          return this.createResolvedImport(
            importInfo, 
            initFile, 
            !isLocal, 
            false, 
            isLocal, 
            'package'
          )
        }
      } else {
        // Must be a package directory
        currentDir = this.fileSystem.joinPath(currentDir, part)
        const initFile = this.fileSystem.joinPath(currentDir, '__init__.py')
        if (!await this.fileSystem.exists(initFile)) {
          return null // Package not found
        }
      }
    }

    return null
  }

  private createResolvedImport(
    importInfo: PythonImport,
    resolvedPath: string | undefined,
    isExternal: boolean,
    isBuiltin: boolean,
    isLocal: boolean,
    moduleType: ResolvedPythonImport['moduleType']
  ): ResolvedPythonImport {
    return {
      originalModule: importInfo.module,
      resolvedPath,
      isExternal,
      isBuiltin,
      isLocal,
      moduleType,
      importedNames: importInfo.names,
      alias: importInfo.alias,
      line: importInfo.line,
      column: importInfo.column,
      level: importInfo.level,
      isFromImport: importInfo.isFromImport
    }
  }

  async buildImportGraph(
    projectRoot: string,
    filePaths: string[],
    options?: Partial<PythonResolutionOptions>
  ): Promise<PythonImportGraph> {
    const resolveOptions = { ...this.options, projectRoot, ...options }
    
    const graph: PythonImportGraph = {
      modules: new Map(),
      packages: new Map(),
      dependencies: new Map(),
      reverseDependencies: new Map(),
      externalModules: new Set(),
      builtinModules: new Set(),
      unresolvedImports: new Map()
    }

    // Filter to Python files only
    const pythonFiles = filePaths.filter(path => this.isPythonFile(path))

    for (const filePath of pythonFiles) {
      const moduleInfo = await this.getModuleInfo(filePath, resolveOptions)
      graph.modules.set(filePath, moduleInfo)

      // Track packages
      if (moduleInfo.packageName) {
        if (!graph.packages.has(moduleInfo.packageName)) {
          graph.packages.set(moduleInfo.packageName, [])
        }
        graph.packages.get(moduleInfo.packageName)!.push(filePath)
      }

      const dependencies: string[] = []
      const unresolvedImports: string[] = []

      for (const importInfo of moduleInfo.imports) {
        if (importInfo.resolvedPath) {
          dependencies.push(importInfo.resolvedPath)
          
          if (importInfo.isExternal) {
            graph.externalModules.add(importInfo.originalModule)
          }
          
          if (importInfo.isBuiltin) {
            graph.builtinModules.add(importInfo.originalModule)
          }

          // Update reverse dependencies
          if (!graph.reverseDependencies.has(importInfo.resolvedPath)) {
            graph.reverseDependencies.set(importInfo.resolvedPath, [])
          }
          graph.reverseDependencies.get(importInfo.resolvedPath)!.push(filePath)
        } else if (!importInfo.isExternal && !importInfo.isBuiltin) {
          unresolvedImports.push(importInfo.originalModule)
        }
      }

      graph.dependencies.set(filePath, dependencies)
      
      if (unresolvedImports.length > 0) {
        graph.unresolvedImports.set(filePath, unresolvedImports)
      }
    }

    return graph
  }

  private async getModuleInfo(
    filePath: string,
    options: Required<PythonResolutionOptions>
  ): Promise<PythonModuleInfo> {
    const cacheKey = filePath
    
    if (options.cacheResults && this.moduleCache.has(cacheKey)) {
      return this.moduleCache.get(cacheKey)!
    }

    const relativePath = this.fileSystem.relative(options.projectRoot, filePath)
    const fileName = this.fileSystem.basename(filePath, '.py')
    const isInitFile = fileName === '__init__'
    const isPackage = isInitFile
    
    // Extract module and package names
    const pathParts = relativePath.split('/').filter(part => part !== '')
    pathParts[pathParts.length - 1] = fileName // Replace .py with module name
    
    let moduleName: string
    let packageName: string | undefined

    if (isInitFile) {
      // For __init__.py files, the module name is the directory name
      pathParts.pop() // Remove __init__
      moduleName = pathParts.length > 0 ? pathParts[pathParts.length - 1] : ''
      packageName = pathParts.join('.')
    } else {
      moduleName = fileName
      packageName = pathParts.length > 1 ? pathParts.slice(0, -1).join('.') : undefined
    }

    // Analyze the file
    const analysisResult = await this.pythonAnalyzer.analyzeFile(filePath)
    const imports: ResolvedPythonImport[] = []
    
    for (const importInfo of analysisResult.imports) {
      const resolved = await this.resolveImport(filePath, importInfo, options)
      if (resolved) {
        imports.push(resolved)
      }
    }

    const exports = this.extractExports(analysisResult)
    const dependencies = imports
      .filter(imp => imp.resolvedPath)
      .map(imp => imp.originalModule)
    
    const submodules = isPackage ? await this.findSubmodules(filePath) : []

    const moduleInfo: PythonModuleInfo = {
      filePath,
      relativePath,
      moduleName,
      packageName,
      isPackage,
      isInitFile,
      exports,
      imports,
      dependencies,
      submodules
    }

    if (options.cacheResults) {
      this.moduleCache.set(cacheKey, moduleInfo)
    }

    return moduleInfo
  }

  private extractExports(analysisResult: any): string[] {
    const exports: string[] = []
    
    // In Python, exports are typically determined by __all__ or public symbols
    for (const symbol of analysisResult.symbols) {
      if (!symbol.isPrivate) {
        exports.push(symbol.name)
      }
    }

    return exports
  }

  private async findSubmodules(packagePath: string): Promise<string[]> {
    const packageDir = this.fileSystem.dirname(packagePath)
    const submodules: string[] = []

    try {
      const items = await this.fileSystem.readdir(packageDir)
      
      for (const item of items) {
        const itemPath = this.fileSystem.joinPath(packageDir, item)
        
        if (item.endsWith('.py') && item !== '__init__.py') {
          // Python module
          submodules.push(item.replace('.py', ''))
        } else if (await this.fileSystem.isDirectory(itemPath)) {
          // Check if it's a package (has __init__.py)
          const initFile = this.fileSystem.joinPath(itemPath, '__init__.py')
          if (await this.fileSystem.exists(initFile)) {
            submodules.push(item)
          }
        }
      }
    } catch {
      // Ignore errors
    }

    return submodules
  }

  private isPythonFile(filePath: string): boolean {
    return filePath.endsWith('.py') || filePath.endsWith('.pyw')
  }

  private isInProjectRoot(filePath: string): boolean {
    return filePath.startsWith(this.options.projectRoot)
  }

  async getModuleDependencies(modulePath: string): Promise<string[]> {
    const moduleInfo = this.moduleCache.get(modulePath)
    if (moduleInfo) {
      return moduleInfo.dependencies
    }

    // Query database for dependencies
    const fileRecord = await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', modulePath)
    )

    if (!fileRecord) return []

    const dependencies = await this.db.findMany(
      this.db.query()
        .select('dependency_path')
        .from('dependencies')
        .where('file_id', '=', fileRecord.id)
    )

    return dependencies.map(dep => dep.dependency_path)
  }

  async getPackageModules(packageName: string): Promise<string[]> {
    const modules = await this.db.findMany(
      this.db.query()
        .select('f.path')
        .from('files f')
        .where('f.relative_path', 'LIKE', `${packageName}/%`)
        .where('f.extension', '=', '.py')
    )

    return modules.map(module => module.path)
  }

  clearCache(): void {
    this.cache.clear()
    this.moduleCache.clear()
  }

  getImportGraph(): PythonImportGraph | null {
    // This would return the last built import graph
    // Implementation depends on how you want to store the graph
    return null
  }
}