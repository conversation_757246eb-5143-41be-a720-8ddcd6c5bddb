import * as vscode from 'vscode'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  level: LogLevel
  message: string
  timestamp: Date
  context?: Record<string, any>
  error?: Error
}

export class Logger {
  private readonly outputChannel: vscode.OutputChannel
  private logLevel: LogLevel = LogLevel.INFO
  private logEntries: LogEntry[] = []
  private maxLogEntries = 1000

  constructor (private readonly name: string) {
    this.outputChannel = vscode.window.createOutputChannel(name)
  }

  setLogLevel (level: LogLevel): void {
    this.logLevel = level
  }

  debug (message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context)
  }

  info (message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context)
  }

  warn (message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context)
  }

  error (message: string, error?: Error, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context, error)
  }

  private log (level: LogLevel, message: string, context?: Record<string, any>, error?: Error): void {
    if (level < this.logLevel) {
      return
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context,
      error
    }

    this.logEntries.push(entry)
    
    if (this.logEntries.length > this.maxLogEntries) {
      this.logEntries.shift()
    }

    const formattedMessage = this.formatLogEntry(entry)
    this.outputChannel.appendLine(formattedMessage)

    if (level === LogLevel.ERROR) {
      console.error(formattedMessage)
    }
  }

  private formatLogEntry(entry: LogEntry): string {
    const timestamp = entry.timestamp.toISOString()
    const level = LogLevel[entry.level]
    let message = `[${timestamp}] [${level}] ${entry.message}`

    if (entry.context) {
      message += ` | Context: ${JSON.stringify(entry.context)}`
    }

    if (entry.error) {
      message += ` | Error: ${entry.error.message}`
      if (entry.error.stack) {
        message += `\nStack: ${entry.error.stack}`
      }
    }

    return message
  }

  show (): void {
    this.outputChannel.show()
  }

  hide (): void {
    this.outputChannel.hide()
  }

  clear (): void {
    this.outputChannel.clear()
    this.logEntries = []
  }

  getLogEntries (): LogEntry[] {
    return [...this.logEntries]
  }

  getLogEntriesByLevel (level: LogLevel): LogEntry[] {
    return this.logEntries.filter(entry => entry.level === level)
  }

  exportLogs (): string {
    return this.logEntries.map(entry => this.formatLogEntry(entry)).join('\n')
  }

  dispose (): void {
    this.outputChannel.dispose()
  }
}

export const logger = new Logger('Natural Language Task Transform')

export function createChildLogger (name: string): Logger {
  return new Logger(`Natural Language Task Transform - ${name}`)
}
