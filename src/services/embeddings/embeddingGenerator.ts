import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { LanceService, VectorRecord, VectorMetadata } from './lanceService'

export interface EmbeddingConfig {
  provider: 'openai' | 'huggingface' | 'sentence-transformers'
  model: string
  apiKey?: string
  batchSize: number
  maxTokens: number
  dimensions: number
  baseUrl?: string
}

export interface EmbeddingRequest {
  text: string
  metadata: VectorMetadata
  id?: string
}

export interface EmbeddingResponse {
  id: string
  embedding: number[]
  metadata: VectorMetadata
  text: string
  tokenCount: number
  processingTime: number
}

export interface ProcessingStats {
  totalProcessed: number
  totalErrors: number
  averageProcessingTime: number
  tokenUsage: number
  batchesProcessed: number
}

export interface TextChunk {
  text: string
  metadata: VectorMetadata
  chunkIndex: number
  totalChunks: number
  startOffset: number
  endOffset: number
}

export class EmbeddingGenerator {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private lanceService: LanceService
  private config: EmbeddingConfig
  private stats: ProcessingStats

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    lanceService: LanceService,
    config: EmbeddingConfig
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.lanceService = lanceService
    this.config = config
    this.stats = this.initializeStats()
  }

  private initializeStats(): ProcessingStats {
    return {
      totalProcessed: 0,
      totalErrors: 0,
      averageProcessingTime: 0,
      tokenUsage: 0,
      batchesProcessed: 0
    }
  }

  async generateEmbedding(text: string): Promise<number[]> {
    const startTime = Date.now()

    try {
      let embedding: number[]

      switch (this.config.provider) {
        case 'openai':
          embedding = await this.generateOpenAIEmbedding(text)
          break
        case 'huggingface':
          embedding = await this.generateHuggingFaceEmbedding(text)
          break
        case 'sentence-transformers':
          embedding = await this.generateSentenceTransformerEmbedding(text)
          break
        default:
          throw new Error(`Unsupported embedding provider: ${this.config.provider}`)
      }

      const processingTime = Date.now() - startTime
      this.updateStats(processingTime, text.length)

      return embedding
    } catch (error) {
      this.stats.totalErrors++
      console.error('Error generating embedding:', error)
      throw new Error(`Embedding generation failed: ${error.message}`)
    }
  }

  async generateEmbeddings(requests: EmbeddingRequest[]): Promise<EmbeddingResponse[]> {
    const responses: EmbeddingResponse[] = []
    const batches = this.batchRequests(requests, this.config.batchSize)

    for (const batch of batches) {
      try {
        const batchResponses = await this.processBatch(batch)
        responses.push(...batchResponses)
        this.stats.batchesProcessed++
      } catch (error) {
        console.error('Error processing batch:', error)
        // Continue with next batch
      }
    }

    return responses
  }

  private async processBatch(requests: EmbeddingRequest[]): Promise<EmbeddingResponse[]> {
    const startTime = Date.now()
    const responses: EmbeddingResponse[] = []

    for (const request of requests) {
      try {
        const embedding = await this.generateEmbedding(request.text)
        const processingTime = Date.now() - startTime

        responses.push({
          id: request.id || this.generateId(),
          embedding,
          metadata: request.metadata,
          text: request.text,
          tokenCount: this.estimateTokenCount(request.text),
          processingTime
        })
      } catch (error) {
        console.error('Error processing individual request:', error)
        this.stats.totalErrors++
      }
    }

    return responses
  }

  private async generateOpenAIEmbedding(text: string): Promise<number[]> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key not configured')
    }

    const openAI = await import('openai')
    const client = new openAI.OpenAI({ apiKey: this.config.apiKey })

    try {
      const response = await client.embeddings.create({
        model: this.config.model || 'text-embedding-ada-002',
        input: text,
        encoding_format: 'float'
      })

      return response.data[0].embedding
    } catch (error) {
      throw new Error(`OpenAI embedding generation failed: ${error.message}`)
    }
  }

  private async generateHuggingFaceEmbedding(text: string): Promise<number[]> {
    if (!this.config.apiKey) {
      throw new Error('Hugging Face API key not configured')
    }

    const response = await fetch(
      `https://api-inference.huggingface.co/pipeline/feature-extraction/${this.config.model}`,
      {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json'
        },
        method: 'POST',
        body: JSON.stringify({ inputs: text })
      }
    )

    if (!response.ok) {
      throw new Error(`Hugging Face API error: ${response.statusText}`)
    }

    const embedding = await response.json()
    return Array.isArray(embedding[0]) ? embedding[0] : embedding
  }

  private async generateSentenceTransformerEmbedding(text: string): Promise<number[]> {
    // This would typically use a local sentence-transformers model
    // For now, we'll simulate with a placeholder
    throw new Error('Sentence-transformers provider not implemented yet')
  }

  async generateFileEmbeddings(filePath: string): Promise<VectorRecord[]> {
    const content = await this.fileSystem.readFile(filePath)
    const language = this.detectLanguage(filePath)
    const chunks = this.chunkText(content, filePath, language)
    const records: VectorRecord[] = []

    for (const chunk of chunks) {
      try {
        const embedding = await this.generateEmbedding(chunk.text)
        const id = this.generateId()

        records.push({
          id,
          vector: embedding,
          metadata: chunk.metadata,
          text: chunk.text,
          filePath,
          createdAt: new Date(),
          updatedAt: new Date()
        })
      } catch (error) {
        console.error(`Error generating embedding for chunk in ${filePath}:`, error)
      }
    }

    return records
  }

  private chunkText(content: string, filePath: string, language: string): TextChunk[] {
    const chunks: TextChunk[] = []
    const maxChunkSize = this.config.maxTokens * 3 // Rough estimate of tokens to characters

    if (content.length <= maxChunkSize) {
      chunks.push({
        text: content,
        metadata: {
          type: 'file',
          language,
          filePath,
          size: content.length,
          hash: this.generateHash(content),
          tags: [language, 'full-file'],
          importance: 1.0
        },
        chunkIndex: 0,
        totalChunks: 1,
        startOffset: 0,
        endOffset: content.length
      })
    } else {
      // Split into chunks
      const chunkCount = Math.ceil(content.length / maxChunkSize)
      
      for (let i = 0; i < chunkCount; i++) {
        const startOffset = i * maxChunkSize
        const endOffset = Math.min(startOffset + maxChunkSize, content.length)
        const chunkText = content.substring(startOffset, endOffset)

        chunks.push({
          text: chunkText,
          metadata: {
            type: 'snippet',
            language,
            filePath,
            size: chunkText.length,
            hash: this.generateHash(chunkText),
            tags: [language, 'chunk'],
            importance: 0.8
          },
          chunkIndex: i,
          totalChunks: chunkCount,
          startOffset,
          endOffset
        })
      }
    }

    return chunks
  }

  async generateSymbolEmbeddings(filePath: string): Promise<VectorRecord[]> {
    const records: VectorRecord[] = []
    
    // Get symbols from database
    const fileRecord = await this.db.findOne(
      this.db.query()
        .select('id')
        .from('files')
        .where('path', '=', filePath)
    )

    if (!fileRecord) {
      return records
    }

    const symbols = await this.db.findMany(
      this.db.query()
        .select('name, type, signature, documentation, line_start, line_end, is_exported')
        .from('symbols')
        .where('file_id', '=', fileRecord.id)
    )

    for (const symbol of symbols) {
      try {
        const symbolText = this.createSymbolText(symbol)
        const embedding = await this.generateEmbedding(symbolText)
        const id = this.generateId()

        records.push({
          id,
          vector: embedding,
          metadata: {
            type: 'symbol',
            language: this.detectLanguage(filePath),
            filePath,
            startLine: symbol.line_start,
            endLine: symbol.line_end,
            symbolName: symbol.name,
            symbolType: symbol.type,
            size: symbolText.length,
            hash: this.generateHash(symbolText),
            tags: [symbol.type, symbol.is_exported ? 'exported' : 'private'],
            importance: symbol.is_exported ? 1.0 : 0.7
          },
          text: symbolText,
          filePath,
          symbolName: symbol.name,
          createdAt: new Date(),
          updatedAt: new Date()
        })
      } catch (error) {
        console.error(`Error generating embedding for symbol ${symbol.name}:`, error)
      }
    }

    return records
  }

  private createSymbolText(symbol: any): string {
    let text = `${symbol.type} ${symbol.name}`
    
    if (symbol.signature) {
      text += `\n${symbol.signature}`
    }
    
    if (symbol.documentation) {
      text += `\n${symbol.documentation}`
    }
    
    return text
  }

  async processWorkspace(workspacePath: string): Promise<void> {
    const files = await this.findCodeFiles(workspacePath)
    const totalFiles = files.length
    
    console.log(`Processing ${totalFiles} files for embeddings...`)
    
    for (let i = 0; i < files.length; i++) {
      const filePath = files[i]
      
      try {
        // Generate file embeddings
        const fileRecords = await this.generateFileEmbeddings(filePath)
        await this.lanceService.addVectors(fileRecords)
        
        // Generate symbol embeddings
        const symbolRecords = await this.generateSymbolEmbeddings(filePath)
        await this.lanceService.addVectors(symbolRecords)
        
        console.log(`Processed ${i + 1}/${totalFiles}: ${filePath}`)
      } catch (error) {
        console.error(`Error processing ${filePath}:`, error)
      }
    }
    
    console.log('Workspace processing complete')
  }

  private async findCodeFiles(workspacePath: string): Promise<string[]> {
    const files: string[] = []
    const extensions = ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.go', '.rs', '.rb', '.php']
    
    const traverseDirectory = async (dirPath: string) => {
      const entries = await this.fileSystem.readdir(dirPath)
      
      for (const entry of entries) {
        const fullPath = this.fileSystem.joinPath(dirPath, entry)
        const stats = await this.fileSystem.stat(fullPath)
        
        if (stats.isDirectory()) {
          // Skip common directories to ignore
          if (!entry.startsWith('.') && !['node_modules', 'dist', 'build'].includes(entry)) {
            await traverseDirectory(fullPath)
          }
        } else if (stats.isFile()) {
          const ext = this.fileSystem.extname(entry)
          if (extensions.includes(ext)) {
            files.push(fullPath)
          }
        }
      }
    }
    
    await traverseDirectory(workspacePath)
    return files
  }

  private batchRequests(requests: EmbeddingRequest[], batchSize: number): EmbeddingRequest[][] {
    const batches: EmbeddingRequest[][] = []
    
    for (let i = 0; i < requests.length; i += batchSize) {
      batches.push(requests.slice(i, i + batchSize))
    }
    
    return batches
  }

  private updateStats(processingTime: number, textLength: number): void {
    this.stats.totalProcessed++
    this.stats.averageProcessingTime = 
      (this.stats.averageProcessingTime * (this.stats.totalProcessed - 1) + processingTime) / this.stats.totalProcessed
    this.stats.tokenUsage += this.estimateTokenCount(textLength.toString())
  }

  private estimateTokenCount(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for most languages
    return Math.ceil(text.length / 4)
  }

  private detectLanguage(filePath: string): string {
    const ext = this.fileSystem.extname(filePath).toLowerCase()
    const languageMap: { [key: string]: string } = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.rb': 'ruby',
      '.php': 'php'
    }
    return languageMap[ext] || 'unknown'
  }

  private generateId(): string {
    return `emb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateHash(text: string): string {
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }

  getStats(): ProcessingStats {
    return { ...this.stats }
  }

  resetStats(): void {
    this.stats = this.initializeStats()
  }
}