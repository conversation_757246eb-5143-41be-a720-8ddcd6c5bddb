import * as vscode from 'vscode'
import { Logger } from '../../utils/logger'
import { CacheManager } from '../cache/cacheManager'

export interface MemoryUsage {
  heapUsed: number
  heapTotal: number
  external: number
  rss: number
  arrayBuffers: number
}

export interface MemoryThreshold {
  warning: number
  critical: number
  emergency: number
}

export interface MemoryPool<T> {
  id: string
  name: string
  objects: T[]
  factory: () => T
  reset: (obj: T) => void
  maxSize: number
  currentSize: number
  totalCreated: number
  totalReused: number
  createdAt: Date
  lastUsed: Date
}

export interface MemoryStats {
  totalPools: number
  totalObjects: number
  totalMemoryUsed: number
  totalMemoryAllocated: number
  gcCount: number
  gcTime: number
  averageGcTime: number
  memoryLeaks: MemoryLeak[]
  largestObjects: MemoryObject[]
}

export interface MemoryLeak {
  id: string
  type: string
  size: number
  age: number
  stackTrace?: string
  references: number
  lastAccessed: Date
  createdAt: Date
}

export interface MemoryObject {
  id: string
  type: string
  size: number
  references: number
  stackTrace?: string
  createdAt: Date
}

export interface MemoryConfiguration {
  maxHeapSize: number
  gcInterval: number
  leakDetectionInterval: number
  poolCleanupInterval: number
  enableProfiling: boolean
  enableLeakDetection: boolean
  emergencyCleanupThreshold: number
  warningThreshold: number
  criticalThreshold: number
}

export class MemoryManager {
  private pools: Map<string, MemoryPool<any>> = new Map()
  private memoryUsageHistory: MemoryUsage[] = []
  private gcStats: { count: number; totalTime: number; lastGc: Date } = {
    count: 0,
    totalTime: 0,
    lastGc: new Date()
  }
  private leakDetectionTimer: NodeJS.Timeout | null = null
  private cleanupTimer: NodeJS.Timeout | null = null
  private profilingTimer: NodeJS.Timeout | null = null
  private weakRefs: WeakMap<object, MemoryObject> = new WeakMap()
  private trackedObjects: Map<string, MemoryObject> = new Map()
  private config: MemoryConfiguration

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly cacheManager?: CacheManager
  ) {
    this.config = this.getDefaultConfiguration()
    this.initializeMemoryManagement()
  }

  // Memory Pool Management
  public createPool<T>(
    id: string,
    name: string,
    factory: () => T,
    reset: (obj: T) => void,
    maxSize: number = 100
  ): MemoryPool<T> {
    const pool: MemoryPool<T> = {
      id,
      name,
      objects: [],
      factory,
      reset,
      maxSize,
      currentSize: 0,
      totalCreated: 0,
      totalReused: 0,
      createdAt: new Date(),
      lastUsed: new Date()
    }

    this.pools.set(id, pool)
    this.logger.info(`Created memory pool: ${name} (max size: ${maxSize})`)
    return pool
  }

  public getFromPool<T>(poolId: string): T | null {
    const pool = this.pools.get(poolId) as MemoryPool<T>
    if (!pool) {
      this.logger.warn(`Memory pool not found: ${poolId}`)
      return null
    }

    pool.lastUsed = new Date()

    if (pool.objects.length > 0) {
      const obj = pool.objects.pop()!
      pool.currentSize--
      pool.totalReused++
      return obj
    }

    // Create new object if pool is empty
    const newObj = pool.factory()
    pool.totalCreated++
    return newObj
  }

  public returnToPool<T>(poolId: string, obj: T): void {
    const pool = this.pools.get(poolId) as MemoryPool<T>
    if (!pool) {
      this.logger.warn(`Memory pool not found: ${poolId}`)
      return
    }

    if (pool.currentSize >= pool.maxSize) {
      // Pool is full, object will be garbage collected
      return
    }

    // Reset object to clean state
    pool.reset(obj)
    pool.objects.push(obj)
    pool.currentSize++
    pool.lastUsed = new Date()
  }

  public resizePool(poolId: string, newSize: number): boolean {
    const pool = this.pools.get(poolId)
    if (!pool) return false

    if (newSize < pool.currentSize) {
      // Remove excess objects
      const excess = pool.currentSize - newSize
      pool.objects.splice(0, excess)
      pool.currentSize = newSize
    }

    pool.maxSize = newSize
    this.logger.info(`Resized memory pool ${poolId} to ${newSize}`)
    return true
  }

  public destroyPool(poolId: string): boolean {
    const pool = this.pools.get(poolId)
    if (!pool) return false

    pool.objects.length = 0
    this.pools.delete(poolId)
    this.logger.info(`Destroyed memory pool: ${poolId}`)
    return true
  }

  // Memory Monitoring
  public getCurrentMemoryUsage(): MemoryUsage {
    const usage = process.memoryUsage()
    return {
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      external: usage.external,
      rss: usage.rss,
      arrayBuffers: usage.arrayBuffers
    }
  }

  public getMemoryStats(): MemoryStats {
    const currentUsage = this.getCurrentMemoryUsage()
    const totalPools = this.pools.size
    const totalObjects = Array.from(this.pools.values()).reduce((sum, pool) => sum + pool.currentSize, 0)
    const totalMemoryUsed = currentUsage.heapUsed
    const totalMemoryAllocated = currentUsage.heapTotal

    return {
      totalPools,
      totalObjects,
      totalMemoryUsed,
      totalMemoryAllocated,
      gcCount: this.gcStats.count,
      gcTime: this.gcStats.totalTime,
      averageGcTime: this.gcStats.count > 0 ? this.gcStats.totalTime / this.gcStats.count : 0,
      memoryLeaks: this.detectMemoryLeaks(),
      largestObjects: this.getLargestObjects()
    }
  }

  public startMemoryProfiling(): void {
    if (this.profilingTimer) return

    this.profilingTimer = setInterval(() => {
      const usage = this.getCurrentMemoryUsage()
      this.memoryUsageHistory.push(usage)
      
      // Keep only last 100 measurements
      if (this.memoryUsageHistory.length > 100) {
        this.memoryUsageHistory.shift()
      }
      
      this.checkMemoryThresholds(usage)
    }, 1000)

    this.logger.info('Started memory profiling')
  }

  public stopMemoryProfiling(): void {
    if (this.profilingTimer) {
      clearInterval(this.profilingTimer)
      this.profilingTimer = null
      this.logger.info('Stopped memory profiling')
    }
  }

  public getMemoryHistory(): MemoryUsage[] {
    return [...this.memoryUsageHistory]
  }

  // Memory Leak Detection
  public trackObject(obj: any, type: string, stackTrace?: string): string {
    const id = this.generateObjectId()
    const memoryObject: MemoryObject = {
      id,
      type,
      size: this.estimateObjectSize(obj),
      references: 0,
      stackTrace,
      createdAt: new Date()
    }

    this.trackedObjects.set(id, memoryObject)
    this.weakRefs.set(obj, memoryObject)
    return id
  }

  public untrackObject(id: string): void {
    this.trackedObjects.delete(id)
  }

  public detectMemoryLeaks(): MemoryLeak[] {
    const leaks: MemoryLeak[] = []
    const now = Date.now()
    const leakThresholdAge = 10 * 60 * 1000 // 10 minutes

    for (const [id, obj] of this.trackedObjects) {
      const age = now - obj.createdAt.getTime()
      if (age > leakThresholdAge) {
        leaks.push({
          id,
          type: obj.type,
          size: obj.size,
          age,
          stackTrace: obj.stackTrace,
          references: obj.references,
          lastAccessed: new Date(), // This would need to be tracked separately
          createdAt: obj.createdAt
        })
      }
    }

    return leaks
  }

  // Garbage Collection
  public forceGarbageCollection(): void {
    const startTime = Date.now()
    
    if (global.gc) {
      global.gc()
      const endTime = Date.now()
      const gcTime = endTime - startTime
      
      this.gcStats.count++
      this.gcStats.totalTime += gcTime
      this.gcStats.lastGc = new Date()
      
      this.logger.info(`Forced garbage collection completed in ${gcTime}ms`)
    } else {
      this.logger.warn('Garbage collection not available (run with --expose-gc)')
    }
  }

  public optimizeMemory(): void {
    this.logger.info('Starting memory optimization...')
    
    // Clean up pools
    this.cleanupPools()
    
    // Clear cache if available
    if (this.cacheManager) {
      this.cacheManager.clear({ priority: 'low' })
    }
    
    // Force garbage collection
    this.forceGarbageCollection()
    
    // Clear old memory usage history
    if (this.memoryUsageHistory.length > 50) {
      this.memoryUsageHistory.splice(0, this.memoryUsageHistory.length - 50)
    }
    
    this.logger.info('Memory optimization completed')
  }

  // Memory Cleanup
  public async emergencyCleanup(): Promise<void> {
    this.logger.warn('Starting emergency memory cleanup...')
    
    // Clear all low-priority pools
    for (const [id, pool] of this.pools) {
      if (pool.name.includes('temp') || pool.name.includes('cache')) {
        this.destroyPool(id)
      }
    }
    
    // Clear all caches
    if (this.cacheManager) {
      await this.cacheManager.clear()
    }
    
    // Clear tracked objects
    this.trackedObjects.clear()
    
    // Clear memory history
    this.memoryUsageHistory.length = 0
    
    // Force multiple garbage collections
    for (let i = 0; i < 3; i++) {
      this.forceGarbageCollection()
      await this.delay(100)
    }
    
    this.logger.warn('Emergency memory cleanup completed')
  }

  // Configuration
  public updateConfiguration(config: Partial<MemoryConfiguration>): void {
    this.config = { ...this.config, ...config }
    this.logger.info('Memory manager configuration updated')
  }

  public getConfiguration(): MemoryConfiguration {
    return { ...this.config }
  }

  // Utility Methods
  private initializeMemoryManagement(): void {
    // Start leak detection
    if (this.config.enableLeakDetection) {
      this.leakDetectionTimer = setInterval(() => {
        const leaks = this.detectMemoryLeaks()
        if (leaks.length > 0) {
          this.logger.warn(`Detected ${leaks.length} potential memory leaks`)
        }
      }, this.config.leakDetectionInterval)
    }

    // Start periodic cleanup
    this.cleanupTimer = setInterval(() => {
      this.cleanupPools()
    }, this.config.poolCleanupInterval)

    // Start profiling if enabled
    if (this.config.enableProfiling) {
      this.startMemoryProfiling()
    }
  }

  private cleanupPools(): void {
    let totalCleaned = 0
    
    for (const [id, pool] of this.pools) {
      const timeSinceLastUse = Date.now() - pool.lastUsed.getTime()
      const cleanupThreshold = 30 * 60 * 1000 // 30 minutes
      
      if (timeSinceLastUse > cleanupThreshold) {
        // Reduce pool size if not used recently
        const targetSize = Math.max(1, Math.floor(pool.maxSize * 0.5))
        if (pool.currentSize > targetSize) {
          const toRemove = pool.currentSize - targetSize
          pool.objects.splice(0, toRemove)
          pool.currentSize = targetSize
          totalCleaned += toRemove
        }
      }
    }
    
    if (totalCleaned > 0) {
      this.logger.info(`Cleaned up ${totalCleaned} unused objects from pools`)
    }
  }

  private checkMemoryThresholds(usage: MemoryUsage): void {
    const heapPercent = (usage.heapUsed / usage.heapTotal) * 100
    
    if (heapPercent > this.config.emergencyCleanupThreshold) {
      this.logger.error(`Critical memory usage: ${heapPercent.toFixed(1)}%`)
      this.emergencyCleanup()
    } else if (heapPercent > this.config.criticalThreshold) {
      this.logger.warn(`High memory usage: ${heapPercent.toFixed(1)}%`)
      this.optimizeMemory()
    } else if (heapPercent > this.config.warningThreshold) {
      this.logger.info(`Memory usage warning: ${heapPercent.toFixed(1)}%`)
    }
  }

  private getLargestObjects(): MemoryObject[] {
    return Array.from(this.trackedObjects.values())
      .sort((a, b) => b.size - a.size)
      .slice(0, 10)
  }

  private estimateObjectSize(obj: any): number {
    if (obj === null || obj === undefined) return 0
    if (typeof obj === 'string') return obj.length * 2
    if (typeof obj === 'number') return 8
    if (typeof obj === 'boolean') return 4
    if (obj instanceof ArrayBuffer) return obj.byteLength
    if (obj instanceof Array) return obj.length * 8
    if (typeof obj === 'object') {
      return Object.keys(obj).length * 20 // Rough estimate
    }
    return 100 // Default estimate
  }

  private generateObjectId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getDefaultConfiguration(): MemoryConfiguration {
    return {
      maxHeapSize: 512 * 1024 * 1024, // 512MB
      gcInterval: 5 * 60 * 1000, // 5 minutes
      leakDetectionInterval: 2 * 60 * 1000, // 2 minutes
      poolCleanupInterval: 10 * 60 * 1000, // 10 minutes
      enableProfiling: false,
      enableLeakDetection: true,
      emergencyCleanupThreshold: 90, // 90% heap usage
      warningThreshold: 70, // 70% heap usage
      criticalThreshold: 80, // 80% heap usage
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // Dispose
  public dispose(): void {
    if (this.leakDetectionTimer) {
      clearInterval(this.leakDetectionTimer)
      this.leakDetectionTimer = null
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    if (this.profilingTimer) {
      clearInterval(this.profilingTimer)
      this.profilingTimer = null
    }
    
    // Clear all pools
    this.pools.clear()
    this.trackedObjects.clear()
    this.memoryUsageHistory.length = 0
    
    this.logger.info('Memory manager disposed')
  }
}

// Specialized Memory Managers
export class BufferPool extends MemoryManager {
  private bufferPool: MemoryPool<Buffer>

  constructor(
    context: vscode.ExtensionContext,
    logger: Logger,
    bufferSize: number = 8192,
    maxBuffers: number = 50
  ) {
    super(context, logger)
    
    this.bufferPool = this.createPool(
      'buffer-pool',
      'Buffer Pool',
      () => Buffer.alloc(bufferSize),
      (buffer: Buffer) => buffer.fill(0),
      maxBuffers
    )
  }

  public getBuffer(): Buffer | null {
    return this.getFromPool<Buffer>('buffer-pool')
  }

  public returnBuffer(buffer: Buffer): void {
    this.returnToPool('buffer-pool', buffer)
  }
}

export class StringPool extends MemoryManager {
  private stringPool: MemoryPool<string[]>

  constructor(
    context: vscode.ExtensionContext,
    logger: Logger,
    maxStrings: number = 100
  ) {
    super(context, logger)
    
    this.stringPool = this.createPool(
      'string-pool',
      'String Pool',
      () => [],
      (strings: string[]) => strings.length = 0,
      maxStrings
    )
  }

  public getStringArray(): string[] | null {
    return this.getFromPool<string[]>('string-pool')
  }

  public returnStringArray(strings: string[]): void {
    this.returnToPool('string-pool', strings)
  }
}

export class ObjectPool<T> extends MemoryManager {
  private objectPool: MemoryPool<T>

  constructor(
    context: vscode.ExtensionContext,
    logger: Logger,
    factory: () => T,
    reset: (obj: T) => void,
    maxObjects: number = 50
  ) {
    super(context, logger)
    
    this.objectPool = this.createPool(
      'object-pool',
      'Object Pool',
      factory,
      reset,
      maxObjects
    )
  }

  public getObject(): T | null {
    return this.getFromPool<T>('object-pool')
  }

  public returnObject(obj: T): void {
    this.returnToPool('object-pool', obj)
  }
}

// Memory Utilities
export class MemoryUtils {
  public static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  public static calculateMemoryGrowth(usage: MemoryUsage[]): number {
    if (usage.length < 2) return 0
    
    const recent = usage.slice(-10)
    const oldest = recent[0]
    const newest = recent[recent.length - 1]
    
    return newest.heapUsed - oldest.heapUsed
  }

  public static findMemorySpikes(usage: MemoryUsage[], threshold: number = 50 * 1024 * 1024): MemoryUsage[] {
    const spikes: MemoryUsage[] = []
    
    for (let i = 1; i < usage.length; i++) {
      const growth = usage[i].heapUsed - usage[i - 1].heapUsed
      if (growth > threshold) {
        spikes.push(usage[i])
      }
    }
    
    return spikes
  }
}