# End-to-End Tests

This directory contains end-to-end tests for the Natural Language Task Transformer extension.

## Test Structure

### Core Feature Tests
- `extension-activation.test.ts` - Extension activation and command registration
- `task-transformation.test.ts` - Natural language to task transformation
- `context-suggestions.test.ts` - Context-aware code suggestions
- `configuration.test.ts` - Settings and configuration management

### Integration Tests
- `ai-provider.test.ts` - AI provider integration (OpenAI, Anthropic)
- `template-system.test.ts` - Template processing and customization
- `indexing-system.test.ts` - Codebase indexing and analysis
- `learning-system.test.ts` - Adaptive learning and preferences

### Performance Tests
- `performance-benchmarks.test.ts` - Response time and resource usage
- `memory-usage.test.ts` - Memory management and cleanup
- `large-codebase.test.ts` - Handling of large projects

### Security Tests
- `input-sanitization.test.ts` - Input validation and sanitization
- `api-key-security.test.ts` - API key handling and storage
- `security-audit.test.ts` - Security scanning functionality

## Running Tests

### Prerequisites
1. VS Code with the extension installed
2. Test API keys for OpenAI and Anthropic
3. Sample test projects in `test-fixtures/`

### Command Line
```bash
# Run all E2E tests
npm run test:e2e

# Run specific test suite
npm run test:e2e -- --grep "task transformation"

# Run with coverage
npm run test:e2e:coverage
```

### VS Code Test Runner
1. Open Command Palette (`Ctrl+Shift+P`)
2. Run "Test: Run All Tests"
3. Select "Extension Tests (E2E)"

## Test Configuration

### Environment Variables
```bash
# Test API keys (use test/development keys)
OPENAI_TEST_API_KEY=sk-test-...
ANTHROPIC_TEST_API_KEY=sk-ant-test-...

# Test configuration
TEST_TIMEOUT=30000
TEST_SLOW_THRESHOLD=5000
```

### Test Settings
```json
{
  "taskTransformer.testMode": true,
  "taskTransformer.aiProvider": "mock",
  "taskTransformer.enableLogging": true,
  "taskTransformer.indexingEnabled": false
}
```

## Test Data

### Sample Projects
- `test-fixtures/react-project/` - React application
- `test-fixtures/node-api/` - Node.js API server
- `test-fixtures/python-app/` - Python application
- `test-fixtures/mixed-project/` - Multi-language project

### Test Inputs
- `test-data/natural-language-inputs.json` - Sample transformation inputs
- `test-data/code-snippets.json` - Code examples for suggestions
- `test-data/expected-outputs.json` - Expected test results

## Writing Tests

### Test Template
```typescript
import * as vscode from 'vscode';
import { expect } from 'chai';
import { ExtensionTestHelper } from '../helpers/ExtensionTestHelper';

describe('Feature Name', () => {
  let testHelper: ExtensionTestHelper;

  before(async () => {
    testHelper = new ExtensionTestHelper();
    await testHelper.setup();
  });

  after(async () => {
    await testHelper.cleanup();
  });

  it('should perform expected behavior', async () => {
    // Test implementation
    const result = await testHelper.runCommand('taskTransformer.transform', {
      input: 'Create a login form'
    });
    
    expect(result).to.exist;
    expect(result.tasks).to.have.length.greaterThan(0);
  });
});
```

### Best Practices

1. **Use Test Helpers**: Utilize shared test utilities
2. **Mock External Services**: Mock AI providers for consistent testing
3. **Test Edge Cases**: Include error conditions and boundary cases
4. **Cleanup Resources**: Ensure proper cleanup after tests
5. **Use Assertions**: Make meaningful assertions about results

## Test Helpers

### ExtensionTestHelper
```typescript
class ExtensionTestHelper {
  async setup(): Promise<void> {
    // Initialize test environment
    await this.activateExtension();
    await this.setupMockServices();
  }

  async runCommand(command: string, args?: any): Promise<any> {
    // Execute VS Code command and return result
  }

  async cleanup(): Promise<void> {
    // Clean up test resources
  }
}
```

### MockAiService
```typescript
class MockAiService {
  async generateResponse(prompt: string): Promise<string> {
    // Return predictable test responses
    return this.getTestResponse(prompt);
  }
}
```

## CI/CD Integration

### GitHub Actions
```yaml
- name: Run E2E Tests
  run: |
    npm run test:e2e
    npm run test:e2e:coverage
  env:
    OPENAI_TEST_API_KEY: ${{ secrets.OPENAI_TEST_API_KEY }}
    ANTHROPIC_TEST_API_KEY: ${{ secrets.ANTHROPIC_TEST_API_KEY }}
```

### Test Reports
- JUnit XML reports for CI integration
- Coverage reports with lcov format
- Performance metrics and benchmarks

## Debugging Tests

### VS Code Debugging
1. Set breakpoints in test files
2. Use "Debug: Start Debugging" with "Extension Tests" configuration
3. Step through test execution

### Logging
```typescript
// Enable detailed logging in tests
import { logger } from '../../utils/logger';
logger.setLevel('debug');
```

### Test Isolation
- Each test runs in isolated environment
- Clean state between test runs
- Separate test workspaces

## Maintenance

### Regular Updates
- Update test data with new features
- Maintain test fixtures
- Update expected outputs
- Review test coverage

### Performance Monitoring
- Track test execution time
- Monitor resource usage
- Identify slow tests
- Optimize test performance

## Troubleshooting

### Common Issues
1. **Extension Not Activating**: Check extension activation events
2. **API Key Errors**: Verify test API keys are valid
3. **Timeout Issues**: Increase test timeout for slow operations
4. **Resource Leaks**: Ensure proper cleanup in test teardown

### Debug Commands
```bash
# Run single test with debug output
npm run test:e2e -- --grep "specific test" --verbose

# Run with extension host logs
code --extensionDevelopmentPath=. --extensionTestsPath=./out/test/e2e
```