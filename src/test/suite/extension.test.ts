import * as assert from 'assert'
import * as vscode from 'vscode'

suite('Extension Test Suite', () => {
  vscode.window.showInformationMessage('Start all tests.')

  test('Extension should be present', () => {
    assert.ok(vscode.extensions.getExtension('test.natural-language-task-transform'))
  })

  test('Extension should activate', async () => {
    const extension = vscode.extensions.getExtension('test.natural-language-task-transform')
    if (extension) {
      await extension.activate()
      assert.ok(extension.isActive)
    }
  })

  test('Commands should be registered', async () => {
    const commands = await vscode.commands.getCommands(true)
    
    assert.ok(commands.includes('naturalLanguageTaskTransform.transform'))
    assert.ok(commands.includes('naturalLanguageTaskTransform.suggest'))
    assert.ok(commands.includes('naturalLanguageTaskTransform.configure'))
  })

  test('Configuration should be accessible', () => {
    const config = vscode.workspace.getConfiguration('naturalLanguageTaskTransform')
    assert.ok(config)
    
    const aiProvider = config.get('aiProvider')
    assert.ok(typeof aiProvider === 'string')
  })

  test('Transform command should execute', async () => {
    try {
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.transform')
      // Command should not throw
      assert.ok(true)
    } catch (error) {
      assert.fail(`Transform command failed: ${error}`)
    }
  })

  test('Suggest command should execute', async () => {
    try {
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.suggest')
      // Command should not throw
      assert.ok(true)
    } catch (error) {
      assert.fail(`Suggest command failed: ${error}`)
    }
  })

  test('Configure command should execute', async () => {
    try {
      await vscode.commands.executeCommand('naturalLanguageTaskTransform.configure')
      // Command should not throw
      assert.ok(true)
    } catch (error) {
      assert.fail(`Configure command failed: ${error}`)
    }
  })

  test('Status bar should be created', () => {
    // This test would need access to the extension context
    // For now, we'll just check that no errors are thrown
    assert.ok(true)
  })
})