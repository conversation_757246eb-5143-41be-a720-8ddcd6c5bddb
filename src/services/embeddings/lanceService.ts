import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'

export interface LanceConfig {
  databasePath: string
  tableName: string
  vectorDimension: number
  storageOptions?: {
    enableIndexing: boolean
    indexType: 'ivf' | 'hnsw' | 'flat'
    numPartitions?: number
    maxConnectionsPerPartition?: number
    searchK?: number
  }
}

export interface VectorRecord {
  id: string
  vector: number[]
  metadata: VectorMetadata
  text?: string
  filePath?: string
  symbolName?: string
  createdAt: Date
  updatedAt: Date
}

export interface VectorMetadata {
  type: 'file' | 'function' | 'class' | 'symbol' | 'snippet' | 'documentation'
  language: string
  filePath: string
  startLine?: number
  endLine?: number
  symbolName?: string
  symbolType?: string
  scope?: string
  size: number
  hash: string
  tags: string[]
  importance: number
  context?: string
}

export interface QueryOptions {
  limit?: number
  filter?: Record<string, any>
  threshold?: number
  includeMetadata?: boolean
  includeVectors?: boolean
}

export interface SearchResult {
  id: string
  score: number
  vector?: number[]
  metadata: VectorMetadata
  text?: string
  distance: number
  similarity: number
}

export interface IndexStats {
  totalRecords: number
  vectorDimension: number
  indexSize: number
  memoryUsage: number
  diskUsage: number
  lastUpdated: Date
}

export class LanceService {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private config: LanceConfig
  private lanceDb: any // Will be dynamically imported
  private table: any
  private isInitialized = false

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    config: LanceConfig
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.config = config
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // Dynamic import of LanceDB
      this.lanceDb = await import('lancedb')
      
      // Ensure database directory exists
      const dbDir = this.fileSystem.dirname(this.config.databasePath)
      if (!await this.fileSystem.exists(dbDir)) {
        await this.fileSystem.mkdir(dbDir)
      }

      // Connect to LanceDB
      const db = await this.lanceDb.connect(this.config.databasePath)
      
      // Create or open table
      try {
        this.table = await db.openTable(this.config.tableName)
      } catch (error) {
        // Table doesn't exist, create it
        const schema = this.createSchema()
        this.table = await db.createTable(this.config.tableName, schema)
      }

      this.isInitialized = true
    } catch (error) {
      console.error('Failed to initialize LanceDB:', error)
      throw new Error(`LanceDB initialization failed: ${error.message}`)
    }
  }

  private createSchema(): any {
    return {
      id: 'string',
      vector: `vector(${this.config.vectorDimension})`,
      metadata: 'json',
      text: 'string',
      filePath: 'string',
      symbolName: 'string',
      createdAt: 'timestamp',
      updatedAt: 'timestamp'
    }
  }

  async addVector(record: Omit<VectorRecord, 'createdAt' | 'updatedAt'>): Promise<void> {
    await this.ensureInitialized()

    const vectorRecord: VectorRecord = {
      ...record,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    try {
      await this.table.add([vectorRecord])
    } catch (error) {
      console.error('Error adding vector to LanceDB:', error)
      throw new Error(`Failed to add vector: ${error.message}`)
    }
  }

  async addVectors(records: Omit<VectorRecord, 'createdAt' | 'updatedAt'>[]): Promise<void> {
    await this.ensureInitialized()

    const vectorRecords: VectorRecord[] = records.map(record => ({
      ...record,
      createdAt: new Date(),
      updatedAt: new Date()
    }))

    try {
      await this.table.add(vectorRecords)
    } catch (error) {
      console.error('Error adding vectors to LanceDB:', error)
      throw new Error(`Failed to add vectors: ${error.message}`)
    }
  }

  async searchSimilar(
    queryVector: number[],
    options: QueryOptions = {}
  ): Promise<SearchResult[]> {
    await this.ensureInitialized()

    const {
      limit = 10,
      filter = {},
      threshold = 0.7,
      includeMetadata = true,
      includeVectors = false
    } = options

    try {
      let query = this.table
        .search(queryVector)
        .limit(limit)

      // Apply filters
      if (Object.keys(filter).length > 0) {
        query = query.where(this.buildFilterExpression(filter))
      }

      const results = await query.toArray()

      return results.map(result => ({
        id: result.id,
        score: result._score || 0,
        vector: includeVectors ? result.vector : undefined,
        metadata: includeMetadata ? result.metadata : {} as VectorMetadata,
        text: result.text,
        distance: result._distance || 0,
        similarity: this.calculateSimilarity(result._distance || 0)
      })).filter(result => result.similarity >= threshold)
    } catch (error) {
      console.error('Error searching vectors:', error)
      throw new Error(`Vector search failed: ${error.message}`)
    }
  }

  async searchByText(
    text: string,
    embeddingVector: number[],
    options: QueryOptions = {}
  ): Promise<SearchResult[]> {
    return this.searchSimilar(embeddingVector, options)
  }

  async updateVector(id: string, updates: Partial<VectorRecord>): Promise<void> {
    await this.ensureInitialized()

    try {
      const updateRecord = {
        ...updates,
        updatedAt: new Date()
      }

      await this.table.update(id, updateRecord)
    } catch (error) {
      console.error('Error updating vector:', error)
      throw new Error(`Failed to update vector: ${error.message}`)
    }
  }

  async deleteVector(id: string): Promise<void> {
    await this.ensureInitialized()

    try {
      await this.table.delete(id)
    } catch (error) {
      console.error('Error deleting vector:', error)
      throw new Error(`Failed to delete vector: ${error.message}`)
    }
  }

  async deleteVectors(ids: string[]): Promise<void> {
    await this.ensureInitialized()

    try {
      await this.table.delete(ids)
    } catch (error) {
      console.error('Error deleting vectors:', error)
      throw new Error(`Failed to delete vectors: ${error.message}`)
    }
  }

  async getVector(id: string): Promise<VectorRecord | null> {
    await this.ensureInitialized()

    try {
      const results = await this.table
        .search({ id })
        .limit(1)
        .toArray()

      return results.length > 0 ? results[0] : null
    } catch (error) {
      console.error('Error getting vector:', error)
      return null
    }
  }

  async getVectorsByFile(filePath: string): Promise<VectorRecord[]> {
    await this.ensureInitialized()

    try {
      const results = await this.table
        .search({ filePath })
        .toArray()

      return results
    } catch (error) {
      console.error('Error getting vectors by file:', error)
      return []
    }
  }

  async getVectorsBySymbol(symbolName: string): Promise<VectorRecord[]> {
    await this.ensureInitialized()

    try {
      const results = await this.table
        .search({ symbolName })
        .toArray()

      return results
    } catch (error) {
      console.error('Error getting vectors by symbol:', error)
      return []
    }
  }

  async getIndexStats(): Promise<IndexStats> {
    await this.ensureInitialized()

    try {
      const stats = await this.table.schema()
      const count = await this.table.count()
      
      return {
        totalRecords: count,
        vectorDimension: this.config.vectorDimension,
        indexSize: 0, // LanceDB doesn't expose this directly
        memoryUsage: 0, // Would need system metrics
        diskUsage: await this.getDiskUsage(),
        lastUpdated: new Date()
      }
    } catch (error) {
      console.error('Error getting index stats:', error)
      return {
        totalRecords: 0,
        vectorDimension: this.config.vectorDimension,
        indexSize: 0,
        memoryUsage: 0,
        diskUsage: 0,
        lastUpdated: new Date()
      }
    }
  }

  async optimizeIndex(): Promise<void> {
    await this.ensureInitialized()

    try {
      // LanceDB handles optimization automatically
      // This method is here for future optimizations
      await this.table.optimize()
    } catch (error) {
      console.error('Error optimizing index:', error)
      throw new Error(`Failed to optimize index: ${error.message}`)
    }
  }

  async clearIndex(): Promise<void> {
    await this.ensureInitialized()

    try {
      await this.table.delete()
      
      // Recreate the table
      const db = await this.lanceDb.connect(this.config.databasePath)
      const schema = this.createSchema()
      this.table = await db.createTable(this.config.tableName, schema)
    } catch (error) {
      console.error('Error clearing index:', error)
      throw new Error(`Failed to clear index: ${error.message}`)
    }
  }

  async close(): Promise<void> {
    if (this.table) {
      try {
        await this.table.close()
      } catch (error) {
        console.error('Error closing LanceDB connection:', error)
      }
    }
    this.isInitialized = false
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }
  }

  private buildFilterExpression(filter: Record<string, any>): string {
    const expressions: string[] = []

    for (const [key, value] of Object.entries(filter)) {
      if (typeof value === 'string') {
        expressions.push(`${key} = '${value}'`)
      } else if (typeof value === 'number') {
        expressions.push(`${key} = ${value}`)
      } else if (Array.isArray(value)) {
        expressions.push(`${key} IN (${value.map(v => typeof v === 'string' ? `'${v}'` : v).join(', ')})`)
      } else if (typeof value === 'object' && value !== null) {
        // Handle range queries
        if (value.min !== undefined && value.max !== undefined) {
          expressions.push(`${key} BETWEEN ${value.min} AND ${value.max}`)
        } else if (value.min !== undefined) {
          expressions.push(`${key} >= ${value.min}`)
        } else if (value.max !== undefined) {
          expressions.push(`${key} <= ${value.max}`)
        }
      }
    }

    return expressions.join(' AND ')
  }

  private calculateSimilarity(distance: number): number {
    // Convert distance to similarity score (0-1)
    return Math.max(0, 1 - distance)
  }

  private async getDiskUsage(): Promise<number> {
    try {
      const stats = await this.fileSystem.stat(this.config.databasePath)
      return stats.size
    } catch (error) {
      return 0
    }
  }

  // Utility methods for batch operations
  async batchInsert(records: Omit<VectorRecord, 'createdAt' | 'updatedAt'>[], batchSize: number = 100): Promise<void> {
    await this.ensureInitialized()

    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize)
      await this.addVectors(batch)
    }
  }

  async batchUpdate(updates: Array<{ id: string; updates: Partial<VectorRecord> }>, batchSize: number = 100): Promise<void> {
    await this.ensureInitialized()

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize)
      await Promise.all(batch.map(({ id, updates }) => this.updateVector(id, updates)))
    }
  }

  async batchDelete(ids: string[], batchSize: number = 100): Promise<void> {
    await this.ensureInitialized()

    for (let i = 0; i < ids.length; i += batchSize) {
      const batch = ids.slice(i, i + batchSize)
      await this.deleteVectors(batch)
    }
  }
}