import { ConnectionManager } from '../database/connectionManager'
import { FileSystemService } from '../fileSystemService'

export interface FileRelationship {
  fromFile: string
  toFile: string
  relationshipType: 'imports' | 'exports' | 'references' | 'depends_on'
  strength: number // 0-1 indicating relationship strength
  lineNumber?: number
  importName?: string
  isCircular?: boolean
}

export interface DependencyGraph {
  nodes: FileNode[]
  edges: FileRelationship[]
  circularDependencies: string[][]
  orphanFiles: string[]
  entryPoints: string[]
}

export interface FileNode {
  path: string
  relativePath: string
  name: string
  language: string
  type: 'source' | 'config' | 'test' | 'documentation'
  imports: number
  exports: number
  dependents: number
  dependencies: number
  complexity: number
  centrality: number
}

export interface RelationshipAnalysis {
  totalFiles: number
  totalRelationships: number
  circularDependencies: number
  orphanFiles: number
  entryPoints: number
  averageDependencies: number
  maxDependencies: number
  mostConnectedFiles: FileNode[]
  criticalPaths: string[][]
}

export class RelationshipMapper {
  private db: ConnectionManager
  private fileSystem: FileSystemService
  private dependencyGraph: DependencyGraph | null = null

  constructor(db: ConnectionManager, fileSystem: FileSystemService) {
    this.db = db
    this.fileSystem = fileSystem
  }

  async buildDependencyGraph(projectRoot: string): Promise<DependencyGraph> {
    const nodes = await this.buildFileNodes(projectRoot)
    const edges = await this.buildFileRelationships(projectRoot)
    const circularDependencies = this.detectCircularDependencies(nodes, edges)
    const orphanFiles = this.findOrphanFiles(nodes, edges)
    const entryPoints = this.findEntryPoints(nodes, edges)

    this.dependencyGraph = {
      nodes,
      edges,
      circularDependencies,
      orphanFiles,
      entryPoints
    }

    return this.dependencyGraph
  }

  private async buildFileNodes(projectRoot: string): Promise<FileNode[]> {
    const files = await this.db.findMany(
      this.db.query()
        .select('f.*, COUNT(d.id) as import_count')
        .from('files f')
        .leftJoin('dependencies d', 'f.id = d.file_id')
        .groupBy('f.id')
    )

    const nodes: FileNode[] = []

    for (const file of files) {
      const dependents = await this.db.findMany(
        this.db.query()
          .select('COUNT(*) as count')
          .from('dependencies d')
          .innerJoin('files f', 'f.id = d.file_id')
          .where('d.dependency_path', '=', file.relative_path)
      )

      const exports = await this.db.findMany(
        this.db.query()
          .select('COUNT(*) as count')
          .from('symbols s')
          .where('s.file_id', '=', file.id)
          .where('s.is_exported', '=', 1)
      )

      const complexity = await this.calculateFileComplexity(file.id)
      const centrality = this.calculateCentrality(file, dependents[0]?.count || 0, file.import_count || 0)

      nodes.push({
        path: file.path,
        relativePath: file.relative_path,
        name: file.name,
        language: file.language || 'unknown',
        type: this.determineFileType(file.path, file.name),
        imports: file.import_count || 0,
        exports: exports[0]?.count || 0,
        dependents: dependents[0]?.count || 0,
        dependencies: file.import_count || 0,
        complexity,
        centrality
      })
    }

    return nodes
  }

  private async buildFileRelationships(projectRoot: string): Promise<FileRelationship[]> {
    const dependencies = await this.db.findMany(
      this.db.query()
        .select('d.*, f.path as from_file, f.relative_path as from_relative')
        .from('dependencies d')
        .innerJoin('files f', 'f.id = d.file_id')
        .where('d.is_local', '=', 1)
    )

    const relationships: FileRelationship[] = []

    for (const dep of dependencies) {
      const toFile = await this.resolveTargetFile(dep.dependency_path, projectRoot)
      if (toFile) {
        const strength = this.calculateRelationshipStrength(dep)
        relationships.push({
          fromFile: dep.from_file,
          toFile: toFile.path,
          relationshipType: 'imports',
          strength,
          lineNumber: dep.line_number,
          importName: dep.import_name,
          isCircular: false
        })
      }
    }

    return this.markCircularRelationships(relationships)
  }

  private async resolveTargetFile(dependencyPath: string, projectRoot: string): Promise<{ path: string } | null> {
    // First try exact match
    const exact = await this.db.findOne(
      this.db.query()
        .select('path')
        .from('files')
        .where('relative_path', '=', dependencyPath)
    )

    if (exact) return exact

    // Try with common extensions
    const extensions = ['.js', '.jsx', '.ts', '.tsx', '.json']
    for (const ext of extensions) {
      const withExt = await this.db.findOne(
        this.db.query()
          .select('path')
          .from('files')
          .where('relative_path', '=', dependencyPath + ext)
      )
      if (withExt) return withExt
    }

    // Try index files
    for (const ext of extensions) {
      const indexPath = dependencyPath + '/index' + ext
      const indexFile = await this.db.findOne(
        this.db.query()
          .select('path')
          .from('files')
          .where('relative_path', '=', indexPath)
      )
      if (indexFile) return indexFile
    }

    return null
  }

  private calculateRelationshipStrength(dependency: any): number {
    let strength = 0.5 // Base strength

    if (dependency.import_name) {
      const importCount = dependency.import_name.split(',').length
      strength += Math.min(importCount * 0.1, 0.3)
    }

    if (dependency.dependency_type === 'import') {
      strength += 0.1
    }

    return Math.min(strength, 1.0)
  }

  private markCircularRelationships(relationships: FileRelationship[]): FileRelationship[] {
    const visited = new Set<string>()
    const recStack = new Set<string>()
    const circularPairs = new Set<string>()

    const dfs = (file: string): boolean => {
      if (recStack.has(file)) {
        return true
      }

      if (visited.has(file)) {
        return false
      }

      visited.add(file)
      recStack.add(file)

      const outgoingRelationships = relationships.filter(r => r.fromFile === file)
      for (const rel of outgoingRelationships) {
        if (dfs(rel.toFile)) {
          circularPairs.add(`${file}->${rel.toFile}`)
          return true
        }
      }

      recStack.delete(file)
      return false
    }

    const allFiles = new Set([
      ...relationships.map(r => r.fromFile),
      ...relationships.map(r => r.toFile)
    ])

    for (const file of allFiles) {
      if (!visited.has(file)) {
        dfs(file)
      }
    }

    return relationships.map(rel => ({
      ...rel,
      isCircular: circularPairs.has(`${rel.fromFile}->${rel.toFile}`)
    }))
  }

  private detectCircularDependencies(nodes: FileNode[], edges: FileRelationship[]): string[][] {
    const cycles: string[][] = []
    const visited = new Set<string>()
    const recStack = new Set<string>()
    const path: string[] = []

    const dfs = (file: string): void => {
      if (recStack.has(file)) {
        const cycleStart = path.indexOf(file)
        if (cycleStart !== -1) {
          cycles.push([...path.slice(cycleStart), file])
        }
        return
      }

      if (visited.has(file)) {
        return
      }

      visited.add(file)
      recStack.add(file)
      path.push(file)

      const outgoingEdges = edges.filter(e => e.fromFile === file)
      for (const edge of outgoingEdges) {
        dfs(edge.toFile)
      }

      path.pop()
      recStack.delete(file)
    }

    for (const node of nodes) {
      if (!visited.has(node.path)) {
        dfs(node.path)
      }
    }

    return cycles
  }

  private findOrphanFiles(nodes: FileNode[], edges: FileRelationship[]): string[] {
    const connectedFiles = new Set([
      ...edges.map(e => e.fromFile),
      ...edges.map(e => e.toFile)
    ])

    return nodes
      .filter(node => !connectedFiles.has(node.path))
      .map(node => node.path)
  }

  private findEntryPoints(nodes: FileNode[], edges: FileRelationship[]): string[] {
    const hasIncomingEdge = new Set(edges.map(e => e.toFile))
    
    return nodes
      .filter(node => !hasIncomingEdge.has(node.path) && node.exports > 0)
      .map(node => node.path)
  }

  private async calculateFileComplexity(fileId: number): Promise<number> {
    const symbols = await this.db.findMany(
      this.db.query()
        .select('type')
        .from('symbols')
        .where('file_id', '=', fileId)
    )

    const content = await this.db.findOne(
      this.db.query()
        .select('line_count')
        .from('file_contents')
        .where('file_id', '=', fileId)
    )

    let complexity = 0
    complexity += symbols.length * 2
    complexity += (content?.line_count || 0) * 0.01
    complexity += symbols.filter(s => s.type === 'function').length * 3
    complexity += symbols.filter(s => s.type === 'class').length * 5

    return Math.min(complexity, 100)
  }

  private calculateCentrality(file: any, dependents: number, dependencies: number): number {
    const totalConnections = dependents + dependencies
    const balance = dependents > 0 ? dependents / (dependents + dependencies) : 0
    return totalConnections * 0.1 + balance * 0.5
  }

  private determineFileType(path: string, name: string): 'source' | 'config' | 'test' | 'documentation' {
    if (name.includes('test') || name.includes('spec') || path.includes('/test/') || path.includes('/__tests__/')) {
      return 'test'
    }

    if (name.includes('config') || name.includes('setup') || 
        ['.json', '.yml', '.yaml', '.toml', '.ini'].some(ext => name.endsWith(ext))) {
      return 'config'
    }

    if (['.md', '.txt', '.rst'].some(ext => name.endsWith(ext))) {
      return 'documentation'
    }

    return 'source'
  }

  async getRelationshipAnalysis(): Promise<RelationshipAnalysis> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    const { nodes, edges, circularDependencies, orphanFiles, entryPoints } = this.dependencyGraph

    const totalFiles = nodes.length
    const totalRelationships = edges.length
    const averageDependencies = totalFiles > 0 ? 
      nodes.reduce((sum, node) => sum + node.dependencies, 0) / totalFiles : 0
    const maxDependencies = Math.max(...nodes.map(node => node.dependencies))
    
    const mostConnectedFiles = [...nodes]
      .sort((a, b) => (b.dependents + b.dependencies) - (a.dependents + a.dependencies))
      .slice(0, 10)

    const criticalPaths = this.findCriticalPaths()

    return {
      totalFiles,
      totalRelationships,
      circularDependencies: circularDependencies.length,
      orphanFiles: orphanFiles.length,
      entryPoints: entryPoints.length,
      averageDependencies,
      maxDependencies,
      mostConnectedFiles,
      criticalPaths
    }
  }

  private findCriticalPaths(): string[][] {
    if (!this.dependencyGraph) return []

    const { nodes, edges } = this.dependencyGraph
    const criticalPaths: string[][] = []

    const highConnectivityNodes = nodes
      .filter(node => node.centrality > 0.7)
      .sort((a, b) => b.centrality - a.centrality)
      .slice(0, 5)

    for (const node of highConnectivityNodes) {
      const path = this.findLongestPath(node.path, edges)
      if (path.length > 3) {
        criticalPaths.push(path)
      }
    }

    return criticalPaths
  }

  private findLongestPath(startFile: string, edges: FileRelationship[]): string[] {
    const visited = new Set<string>()
    const path: string[] = []
    let longestPath: string[] = []

    const dfs = (file: string): void => {
      if (visited.has(file)) return

      visited.add(file)
      path.push(file)

      if (path.length > longestPath.length) {
        longestPath = [...path]
      }

      const outgoingEdges = edges.filter(e => e.fromFile === file)
      for (const edge of outgoingEdges) {
        dfs(edge.toFile)
      }

      path.pop()
      visited.delete(file)
    }

    dfs(startFile)
    return longestPath
  }

  async getFileRelationships(filePath: string): Promise<{
    dependencies: FileRelationship[]
    dependents: FileRelationship[]
  }> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    const dependencies = this.dependencyGraph.edges.filter(e => e.fromFile === filePath)
    const dependents = this.dependencyGraph.edges.filter(e => e.toFile === filePath)

    return { dependencies, dependents }
  }

  async getCircularDependencies(): Promise<string[][]> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    return this.dependencyGraph.circularDependencies
  }

  async getOrphanFiles(): Promise<string[]> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    return this.dependencyGraph.orphanFiles
  }

  async getEntryPoints(): Promise<string[]> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    return this.dependencyGraph.entryPoints
  }

  async saveRelationshipsToDB(): Promise<void> {
    if (!this.dependencyGraph) {
      throw new Error('Dependency graph not built. Call buildDependencyGraph first.')
    }

    await this.db.transaction(async (tx) => {
      // Clear existing relationships
      await tx.runSql('DELETE FROM file_relationships')

      // Insert new relationships
      for (const edge of this.dependencyGraph.edges) {
        await tx.execute(
          tx.insert()
            .into('file_relationships')
            .columns(['from_file', 'to_file', 'relationship_type', 'strength', 'line_number', 'import_name', 'is_circular'])
            .values([
              edge.fromFile,
              edge.toFile,
              edge.relationshipType,
              edge.strength,
              edge.lineNumber,
              edge.importName,
              edge.isCircular ? 1 : 0
            ])
        )
      }
    })
  }

  getDependencyGraph(): DependencyGraph | null {
    return this.dependencyGraph
  }
}