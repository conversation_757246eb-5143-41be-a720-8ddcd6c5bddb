import * as vscode from 'vscode'

// Minimal working version of the extension
export function activate(context: vscode.ExtensionContext) {
  console.log('Task Transformer extension is activating...')

  // Register the transform command with minimal implementation
  const transformCommand = vscode.commands.registerCommand('taskTransformer.transform', async () => {
    try {
      // Get user input
      const input = await vscode.window.showInputBox({
        prompt: 'Enter natural language description to transform into tasks',
        placeHolder: 'e.g., "Create a login form with validation"'
      })

      if (!input) {
        return
      }

      // Simple transformation (placeholder)
      const tasks = transformToTasks(input)
      
      // Show result in new document
      const doc = await vscode.workspace.openTextDocument({
        content: tasks,
        language: 'markdown'
      })
      
      await vscode.window.showTextDocument(doc)
      
      vscode.window.showInformationMessage('Tasks generated successfully!')
    } catch (error) {
      vscode.window.showErrorMessage(`Error: ${error}`)
    }
  })

  // Register the configure command
  const configureCommand = vscode.commands.registerCommand('taskTransformer.configure', async () => {
    const provider = await vscode.window.showQuickPick(['openai', 'claude'], {
      placeHolder: 'Select AI provider'
    })
    
    if (provider) {
      const apiKey = await vscode.window.showInputBox({
        prompt: `Enter your ${provider} API key`,
        password: true
      })
      
      if (apiKey) {
        // Store API key securely
        await context.secrets.store(`${provider}_api_key`, apiKey)
        vscode.window.showInformationMessage(`${provider} configured successfully!`)
      }
    }
  })

  // Register the suggest command
  const suggestCommand = vscode.commands.registerCommand('taskTransformer.suggest', async () => {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      vscode.window.showWarningMessage('No active editor found')
      return
    }

    const selection = editor.selection
    const selectedText = editor.document.getText(selection)
    
    if (!selectedText) {
      vscode.window.showWarningMessage('Please select some code first')
      return
    }

    // Simple suggestions (placeholder)
    const suggestions = generateSuggestions(selectedText)
    
    vscode.window.showInformationMessage(suggestions)
  })

  // Add commands to subscriptions
  context.subscriptions.push(transformCommand, configureCommand, suggestCommand)

  // Create status bar item
  const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100)
  statusBarItem.text = '$(brain) Task Transformer'
  statusBarItem.tooltip = 'Transform natural language to structured tasks'
  statusBarItem.command = 'taskTransformer.transform'
  statusBarItem.show()
  context.subscriptions.push(statusBarItem)

  console.log('Task Transformer extension activated successfully')
}

export function deactivate() {
  console.log('Task Transformer extension deactivated')
}

// Simple transformation function (placeholder)
function transformToTasks(input: string): string {
  const timestamp = new Date().toISOString()
  
  return `# Task Transformation Result

**Input:** ${input}
**Generated:** ${timestamp}

## Tasks:

1. **Analyze Requirements**
   - Break down the request: "${input}"
   - Identify key components and dependencies
   - Define acceptance criteria

2. **Design Implementation**
   - Create technical specifications
   - Plan architecture and data flow
   - Design user interface mockups

3. **Development**
   - Set up development environment
   - Implement core functionality
   - Add error handling and validation

4. **Testing**
   - Write unit tests
   - Perform integration testing
   - Conduct user acceptance testing

5. **Documentation**
   - Update technical documentation
   - Create user guides
   - Document API endpoints

## Next Steps:
- Review and prioritize tasks
- Assign team members
- Set timeline and milestones

---
*Generated by Task Transformer Extension*
`
}

// Simple suggestion function (placeholder)
function generateSuggestions(code: string): string {
  const suggestions = [
    'Consider adding error handling',
    'Add input validation',
    'Include unit tests',
    'Add documentation comments',
    'Consider performance optimization'
  ]
  
  const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)]
  return `Suggestion for selected code: ${randomSuggestion}`
}
