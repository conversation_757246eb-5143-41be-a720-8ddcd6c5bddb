import * as vscode from 'vscode'
import * as path from 'path'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'
import { IndexingService } from '../services/indexingService'
import { SemanticSearch } from '../services/embeddings/semanticSearch'
import { PreferenceTracker } from '../services/memory/preferenceTracker'

export interface IndexedFile {
  id: string
  path: string
  name: string
  type: FileType
  language: string
  size: number
  lastModified: Date
  lastIndexed: Date
  symbols: Symbol[]
  imports: Import[]
  exports: Export[]
  dependencies: string[]
  complexity: FileComplexity
  quality: FileQuality
  metadata: FileMetadata
  status: IndexStatus
}

export interface FileType {
  category: 'source' | 'test' | 'config' | 'documentation' | 'asset' | 'build' | 'other'
  extension: string
  mimeType: string
  isTextFile: boolean
  isBinary: boolean
  isExecutable: boolean
}

export interface Symbol {
  id: string
  name: string
  kind: vscode.SymbolKind
  range: vscode.Range
  selectionRange: vscode.Range
  detail: string
  documentation: string
  deprecated: boolean
  tags: vscode.SymbolTag[]
  children: Symbol[]
  containerName?: string
  signature?: string
  returnType?: string
  parameters?: Parameter[]
  modifiers: string[]
  accessibility: 'public' | 'private' | 'protected' | 'internal'
  complexity: number
  usageCount: number
}

export interface Parameter {
  name: string
  type: string
  optional: boolean
  defaultValue?: string
  description?: string
}

export interface Import {
  id: string
  module: string
  specifiers: ImportSpecifier[]
  source: string
  range: vscode.Range
  isTypeOnly: boolean
  isDefault: boolean
  resolved: boolean
  resolvedPath?: string
  external: boolean
  version?: string
}

export interface ImportSpecifier {
  name: string
  alias?: string
  isDefault: boolean
  isNamespace: boolean
}

export interface Export {
  id: string
  name: string
  type: string
  range: vscode.Range
  isDefault: boolean
  isTypeOnly: boolean
  documentation?: string
  signature?: string
}

export interface FileComplexity {
  cyclomatic: number
  cognitive: number
  lines: number
  functions: number
  classes: number
  dependencies: number
  maintainabilityIndex: number
  technicalDebt: number
  rating: 'A' | 'B' | 'C' | 'D' | 'F'
}

export interface FileQuality {
  score: number
  issues: QualityIssue[]
  suggestions: QualitySuggestion[]
  coverage: TestCoverage
  documentation: DocumentationQuality
  performance: PerformanceMetrics
}

export interface QualityIssue {
  id: string
  type: 'error' | 'warning' | 'info' | 'hint'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  range: vscode.Range
  source: string
  fixable: boolean
  fixes: QuickFix[]
}

export interface QuickFix {
  title: string
  description: string
  edits: vscode.TextEdit[]
  command?: vscode.Command
}

export interface QualitySuggestion {
  id: string
  title: string
  description: string
  category: string
  impact: 'low' | 'medium' | 'high'
  effort: 'low' | 'medium' | 'high'
  priority: number
}

export interface TestCoverage {
  lines: number
  branches: number
  functions: number
  statements: number
  uncoveredLines: number[]
  testFiles: string[]
}

export interface DocumentationQuality {
  score: number
  coverage: number
  completeness: number
  accuracy: number
  clarity: number
  examples: number
  missingDocs: string[]
}

export interface PerformanceMetrics {
  parseTime: number
  indexTime: number
  searchTime: number
  memoryUsage: number
  cacheHits: number
  cacheMisses: number
}

export interface FileMetadata {
  project: string
  framework: string
  version: string
  author: string
  license: string
  encoding: string
  lineEndings: string
  bom: boolean
  tags: string[]
  categories: string[]
  annotations: Annotation[]
}

export interface Annotation {
  type: string
  value: string
  range: vscode.Range
  metadata: Record<string, any>
}

export interface IndexStatus {
  indexed: boolean
  indexing: boolean
  error: boolean
  errorMessage?: string
  progress: number
  lastIndexed?: Date
  version: number
  dirty: boolean
}

export interface IndexBrowserState {
  selectedFile?: string
  expandedFolders: Set<string>
  sortBy: 'name' | 'type' | 'modified' | 'size' | 'complexity' | 'quality'
  sortDirection: 'asc' | 'desc'
  groupBy: 'none' | 'type' | 'language' | 'project' | 'quality'
  filterBy: IndexFilter[]
  searchQuery: string
  viewMode: 'tree' | 'list' | 'grid'
  showHidden: boolean
  showDetails: boolean
  refreshInterval: number
}

export interface IndexFilter {
  field: 'type' | 'language' | 'quality' | 'complexity' | 'size' | 'modified'
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'startsWith' | 'endsWith'
  value: any
  enabled: boolean
}

export interface IndexTreeItem extends vscode.TreeItem {
  file?: IndexedFile
  children?: IndexTreeItem[]
  type: 'file' | 'folder' | 'symbol' | 'group'
  metadata?: any
}

export interface SearchResult {
  file: IndexedFile
  matches: SearchMatch[]
  score: number
  context: string
}

export interface SearchMatch {
  range: vscode.Range
  text: string
  type: 'symbol' | 'content' | 'import' | 'comment'
  symbol?: Symbol
  context: string
}

export class IndexBrowserProvider implements vscode.TreeDataProvider<IndexTreeItem> {
  private _onDidChangeTreeData: vscode.EventEmitter<IndexTreeItem | undefined | null | void> = new vscode.EventEmitter<IndexTreeItem | undefined | null | void>()
  readonly onDidChangeTreeData: vscode.Event<IndexTreeItem | undefined | null | void> = this._onDidChangeTreeData.event

  private indexedFiles: Map<string, IndexedFile> = new Map()
  private state: IndexBrowserState
  private refreshTimer?: NodeJS.Timeout

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService,
    private readonly indexingService: IndexingService,
    private readonly semanticSearch: SemanticSearch,
    private readonly preferenceTracker: PreferenceTracker
  ) {
    this.state = {
      expandedFolders: new Set(),
      sortBy: 'name',
      sortDirection: 'asc',
      groupBy: 'type',
      filterBy: [],
      searchQuery: '',
      viewMode: 'tree',
      showHidden: false,
      showDetails: true,
      refreshInterval: 30000
    }

    this.loadState()
    this.setupEventListeners()
    this.startAutoRefresh()
  }

  private setupEventListeners(): void {
    // Listen for file system changes
    const watcher = vscode.workspace.createFileSystemWatcher('**/*')
    
    watcher.onDidCreate((uri) => {
      this.handleFileCreated(uri)
    })
    
    watcher.onDidChange((uri) => {
      this.handleFileChanged(uri)
    })
    
    watcher.onDidDelete((uri) => {
      this.handleFileDeleted(uri)
    })

    // Listen for indexing service events
    this.indexingService.onDidIndexFile((file) => {
      this.handleFileIndexed(file)
    })

    this.indexingService.onDidIndexingProgress((progress) => {
      this.handleIndexingProgress(progress)
    })

    this.indexingService.onDidIndexingComplete(() => {
      this.refreshAll()
    })
  }

  private startAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }

    if (this.state.refreshInterval > 0) {
      this.refreshTimer = setInterval(() => {
        this.refreshIndex()
      }, this.state.refreshInterval)
    }
  }

  // TreeDataProvider implementation
  getTreeItem(element: IndexTreeItem): vscode.TreeItem {
    return element
  }

  getChildren(element?: IndexTreeItem): Thenable<IndexTreeItem[]> {
    if (!element) {
      return this.getRootItems()
    }

    if (element.type === 'folder') {
      return this.getFolderChildren(element)
    }

    if (element.type === 'file' && element.file) {
      return this.getFileChildren(element.file)
    }

    if (element.type === 'group') {
      return this.getGroupChildren(element)
    }

    return Promise.resolve([])
  }

  private async getRootItems(): Promise<IndexTreeItem[]> {
    const files = await this.getFilteredFiles()
    
    if (this.state.groupBy === 'none') {
      return this.buildFileTree(files)
    } else {
      return this.buildGroupedTree(files)
    }
  }

  private async getFilteredFiles(): Promise<IndexedFile[]> {
    let files = Array.from(this.indexedFiles.values())

    // Apply search filter
    if (this.state.searchQuery) {
      files = await this.searchFiles(this.state.searchQuery)
    }

    // Apply custom filters
    for (const filter of this.state.filterBy.filter(f => f.enabled)) {
      files = files.filter(file => this.applyFilter(file, filter))
    }

    // Apply hidden files filter
    if (!this.state.showHidden) {
      files = files.filter(file => !file.name.startsWith('.'))
    }

    // Sort files
    files.sort((a, b) => this.compareFiles(a, b))

    return files
  }

  private buildFileTree(files: IndexedFile[]): IndexTreeItem[] {
    const tree: Map<string, IndexTreeItem> = new Map()
    const roots: IndexTreeItem[] = []

    for (const file of files) {
      const pathParts = file.path.split(path.sep)
      let currentPath = ''
      let currentParent: IndexTreeItem[] = roots

      for (let i = 0; i < pathParts.length; i++) {
        const part = pathParts[i]
        currentPath = currentPath ? path.join(currentPath, part) : part
        
        if (i === pathParts.length - 1) {
          // This is the file
          const fileItem = this.createFileItem(file)
          currentParent.push(fileItem)
        } else {
          // This is a folder
          let folderItem = tree.get(currentPath)
          
          if (!folderItem) {
            folderItem = this.createFolderItem(part, currentPath)
            tree.set(currentPath, folderItem)
            currentParent.push(folderItem)
          }
          
          if (!folderItem.children) {
            folderItem.children = []
          }
          currentParent = folderItem.children
        }
      }
    }

    return roots
  }

  private buildGroupedTree(files: IndexedFile[]): IndexTreeItem[] {
    const groups = this.groupFiles(files)
    const items: IndexTreeItem[] = []

    for (const [groupKey, groupFiles] of groups) {
      const groupItem = this.createGroupItem(groupKey, groupFiles)
      items.push(groupItem)
    }

    return items
  }

  private groupFiles(files: IndexedFile[]): Map<string, IndexedFile[]> {
    const groups = new Map<string, IndexedFile[]>()

    for (const file of files) {
      let groupKey: string

      switch (this.state.groupBy) {
        case 'type':
          groupKey = file.type.category
          break
        case 'language':
          groupKey = file.language
          break
        case 'project':
          groupKey = file.metadata.project || 'Unknown'
          break
        case 'quality':
          groupKey = file.quality.score >= 80 ? 'High Quality' :
                    file.quality.score >= 60 ? 'Medium Quality' : 'Low Quality'
          break
        default:
          groupKey = 'All Files'
      }

      if (!groups.has(groupKey)) {
        groups.set(groupKey, [])
      }
      groups.get(groupKey)!.push(file)
    }

    return groups
  }

  private createFileItem(file: IndexedFile): IndexTreeItem {
    const item = new IndexTreeItem(
      file.name,
      vscode.TreeItemCollapsibleState.Collapsed
    )

    item.file = file
    item.type = 'file'
    item.resourceUri = vscode.Uri.file(file.path)
    item.command = {
      command: 'vscode.open',
      title: 'Open File',
      arguments: [vscode.Uri.file(file.path)]
    }

    // Set icon based on file type
    item.iconPath = this.getFileIcon(file)

    // Set context value for commands
    item.contextValue = 'indexedFile'

    // Add tooltip with file details
    item.tooltip = this.createFileTooltip(file)

    // Add badges for quality/complexity
    if (this.state.showDetails) {
      item.description = this.createFileDescription(file)
    }

    return item
  }

  private createFolderItem(name: string, fullPath: string): IndexTreeItem {
    const item = new IndexTreeItem(
      name,
      this.state.expandedFolders.has(fullPath) 
        ? vscode.TreeItemCollapsibleState.Expanded 
        : vscode.TreeItemCollapsibleState.Collapsed
    )

    item.type = 'folder'
    item.contextValue = 'indexedFolder'
    item.iconPath = new vscode.ThemeIcon('folder')
    item.children = []

    return item
  }

  private createGroupItem(groupKey: string, files: IndexedFile[]): IndexTreeItem {
    const item = new IndexTreeItem(
      `${groupKey} (${files.length})`,
      vscode.TreeItemCollapsibleState.Expanded
    )

    item.type = 'group'
    item.contextValue = 'indexedGroup'
    item.iconPath = this.getGroupIcon(groupKey)
    item.metadata = { groupKey, files }

    return item
  }

  private createSymbolItem(symbol: Symbol, file: IndexedFile): IndexTreeItem {
    const item = new IndexTreeItem(
      symbol.name,
      symbol.children.length > 0 
        ? vscode.TreeItemCollapsibleState.Collapsed 
        : vscode.TreeItemCollapsibleState.None
    )

    item.type = 'symbol'
    item.contextValue = 'indexedSymbol'
    item.resourceUri = vscode.Uri.file(file.path)
    item.iconPath = new vscode.ThemeIcon(this.getSymbolIcon(symbol.kind))
    
    item.command = {
      command: 'vscode.open',
      title: 'Go to Symbol',
      arguments: [
        vscode.Uri.file(file.path),
        { selection: symbol.selectionRange }
      ]
    }

    item.tooltip = this.createSymbolTooltip(symbol)
    item.description = this.createSymbolDescription(symbol)

    return item
  }

  private async getFolderChildren(folder: IndexTreeItem): Promise<IndexTreeItem[]> {
    return folder.children || []
  }

  private async getFileChildren(file: IndexedFile): Promise<IndexTreeItem[]> {
    const children: IndexTreeItem[] = []

    // Add symbols
    for (const symbol of file.symbols) {
      const symbolItem = this.createSymbolItem(symbol, file)
      children.push(symbolItem)
    }

    // Add imports section
    if (file.imports.length > 0) {
      const importsGroup = new IndexTreeItem(
        `Imports (${file.imports.length})`,
        vscode.TreeItemCollapsibleState.Collapsed
      )
      importsGroup.type = 'group'
      importsGroup.iconPath = new vscode.ThemeIcon('references')
      children.push(importsGroup)
    }

    // Add exports section
    if (file.exports.length > 0) {
      const exportsGroup = new IndexTreeItem(
        `Exports (${file.exports.length})`,
        vscode.TreeItemCollapsibleState.Collapsed
      )
      exportsGroup.type = 'group'
      exportsGroup.iconPath = new vscode.ThemeIcon('export')
      children.push(exportsGroup)
    }

    // Add quality issues section
    if (file.quality.issues.length > 0) {
      const issuesGroup = new IndexTreeItem(
        `Issues (${file.quality.issues.length})`,
        vscode.TreeItemCollapsibleState.Collapsed
      )
      issuesGroup.type = 'group'
      issuesGroup.iconPath = new vscode.ThemeIcon('warning')
      children.push(issuesGroup)
    }

    return children
  }

  private async getGroupChildren(group: IndexTreeItem): Promise<IndexTreeItem[]> {
    if (!group.metadata?.files) {
      return []
    }

    const files: IndexedFile[] = group.metadata.files
    return files.map(file => this.createFileItem(file))
  }

  // Search functionality
  private async searchFiles(query: string): Promise<IndexedFile[]> {
    const results = await this.semanticSearch.searchFiles(query, {
      limit: 100,
      threshold: 0.5,
      includeContent: true,
      includeSymbols: true
    })

    return results.map(result => result.file)
  }

  // Filter functionality
  private applyFilter(file: IndexedFile, filter: IndexFilter): boolean {
    let value: any

    switch (filter.field) {
      case 'type':
        value = file.type.category
        break
      case 'language':
        value = file.language
        break
      case 'quality':
        value = file.quality.score
        break
      case 'complexity':
        value = file.complexity.cyclomatic
        break
      case 'size':
        value = file.size
        break
      case 'modified':
        value = file.lastModified.getTime()
        break
      default:
        return true
    }

    switch (filter.operator) {
      case 'eq':
        return value === filter.value
      case 'neq':
        return value !== filter.value
      case 'gt':
        return value > filter.value
      case 'gte':
        return value >= filter.value
      case 'lt':
        return value < filter.value
      case 'lte':
        return value <= filter.value
      case 'contains':
        return String(value).toLowerCase().includes(String(filter.value).toLowerCase())
      case 'startsWith':
        return String(value).toLowerCase().startsWith(String(filter.value).toLowerCase())
      case 'endsWith':
        return String(value).toLowerCase().endsWith(String(filter.value).toLowerCase())
      default:
        return true
    }
  }

  // Sort functionality
  private compareFiles(a: IndexedFile, b: IndexedFile): number {
    let result = 0

    switch (this.state.sortBy) {
      case 'name':
        result = a.name.localeCompare(b.name)
        break
      case 'type':
        result = a.type.category.localeCompare(b.type.category)
        break
      case 'modified':
        result = a.lastModified.getTime() - b.lastModified.getTime()
        break
      case 'size':
        result = a.size - b.size
        break
      case 'complexity':
        result = a.complexity.cyclomatic - b.complexity.cyclomatic
        break
      case 'quality':
        result = b.quality.score - a.quality.score // Higher quality first
        break
    }

    return this.state.sortDirection === 'desc' ? -result : result
  }

  // UI helper methods
  private getFileIcon(file: IndexedFile): vscode.ThemeIcon {
    const iconMap: Record<string, string> = {
      // Language specific icons
      'typescript': 'symbol-class',
      'javascript': 'symbol-function',
      'python': 'symbol-module',
      'java': 'symbol-class',
      'csharp': 'symbol-namespace',
      'cpp': 'symbol-struct',
      'rust': 'symbol-package',
      'go': 'symbol-interface',
      'php': 'symbol-function',
      'ruby': 'symbol-class',
      'swift': 'symbol-class',
      'kotlin': 'symbol-class',
      
      // File type icons
      'json': 'json',
      'yaml': 'settings',
      'xml': 'code',
      'html': 'browser',
      'css': 'symbol-color',
      'scss': 'symbol-color',
      'less': 'symbol-color',
      'markdown': 'preview',
      'sql': 'database',
      'dockerfile': 'vm',
      'makefile': 'tools',
      'gitignore': 'exclude'
    }

    const icon = iconMap[file.language] || iconMap[file.type.extension] || 'file'
    return new vscode.ThemeIcon(icon)
  }

  private getGroupIcon(groupKey: string): vscode.ThemeIcon {
    const iconMap: Record<string, string> = {
      'source': 'file-code',
      'test': 'beaker',
      'config': 'settings-gear',
      'documentation': 'book',
      'asset': 'file-media',
      'build': 'tools',
      'High Quality': 'star-full',
      'Medium Quality': 'star-half',
      'Low Quality': 'star-empty'
    }

    return new vscode.ThemeIcon(iconMap[groupKey] || 'folder')
  }

  private getSymbolIcon(kind: vscode.SymbolKind): string {
    const iconMap: Record<number, string> = {
      [vscode.SymbolKind.File]: 'file',
      [vscode.SymbolKind.Module]: 'package',
      [vscode.SymbolKind.Namespace]: 'symbol-namespace',
      [vscode.SymbolKind.Package]: 'package',
      [vscode.SymbolKind.Class]: 'symbol-class',
      [vscode.SymbolKind.Method]: 'symbol-method',
      [vscode.SymbolKind.Property]: 'symbol-property',
      [vscode.SymbolKind.Field]: 'symbol-field',
      [vscode.SymbolKind.Constructor]: 'symbol-constructor',
      [vscode.SymbolKind.Enum]: 'symbol-enum',
      [vscode.SymbolKind.Interface]: 'symbol-interface',
      [vscode.SymbolKind.Function]: 'symbol-function',
      [vscode.SymbolKind.Variable]: 'symbol-variable',
      [vscode.SymbolKind.Constant]: 'symbol-constant',
      [vscode.SymbolKind.String]: 'symbol-string',
      [vscode.SymbolKind.Number]: 'symbol-number',
      [vscode.SymbolKind.Boolean]: 'symbol-boolean',
      [vscode.SymbolKind.Array]: 'symbol-array',
      [vscode.SymbolKind.Object]: 'symbol-object',
      [vscode.SymbolKind.Key]: 'symbol-key',
      [vscode.SymbolKind.Null]: 'symbol-null',
      [vscode.SymbolKind.EnumMember]: 'symbol-enum-member',
      [vscode.SymbolKind.Struct]: 'symbol-struct',
      [vscode.SymbolKind.Event]: 'symbol-event',
      [vscode.SymbolKind.Operator]: 'symbol-operator',
      [vscode.SymbolKind.TypeParameter]: 'symbol-type-parameter'
    }

    return iconMap[kind] || 'symbol-misc'
  }

  private createFileTooltip(file: IndexedFile): string {
    return `
Path: ${file.path}
Type: ${file.type.category}
Language: ${file.language}
Size: ${this.formatBytes(file.size)}
Modified: ${file.lastModified.toLocaleDateString()}
Quality Score: ${file.quality.score}/100
Complexity: ${file.complexity.cyclomatic}
Symbols: ${file.symbols.length}
Dependencies: ${file.dependencies.length}
    `.trim()
  }

  private createSymbolTooltip(symbol: Symbol): string {
    return `
Name: ${symbol.name}
Kind: ${vscode.SymbolKind[symbol.kind]}
Signature: ${symbol.signature || 'N/A'}
Complexity: ${symbol.complexity}
Usage Count: ${symbol.usageCount}
${symbol.documentation ? `\nDocumentation: ${symbol.documentation}` : ''}
    `.trim()
  }

  private createFileDescription(file: IndexedFile): string {
    const parts: string[] = []

    // Quality indicator
    if (file.quality.score >= 80) {
      parts.push('🟢')
    } else if (file.quality.score >= 60) {
      parts.push('🟡')
    } else {
      parts.push('🔴')
    }

    // Complexity indicator
    if (file.complexity.cyclomatic > 10) {
      parts.push('🔺')
    }

    // Issue count
    if (file.quality.issues.length > 0) {
      parts.push(`⚠${file.quality.issues.length}`)
    }

    // Size
    parts.push(this.formatBytes(file.size))

    return parts.join(' ')
  }

  private createSymbolDescription(symbol: Symbol): string {
    const parts: string[] = []

    if (symbol.deprecated) {
      parts.push('deprecated')
    }

    if (symbol.accessibility !== 'public') {
      parts.push(symbol.accessibility)
    }

    if (symbol.returnType) {
      parts.push(`: ${symbol.returnType}`)
    }

    return parts.join(' ')
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // Event handlers
  private async handleFileCreated(uri: vscode.Uri): Promise<void> {
    try {
      await this.indexingService.indexFile(uri.fsPath)
      this.refreshAll()
    } catch (error) {
      this.logger.error('Error handling file creation:', error)
    }
  }

  private async handleFileChanged(uri: vscode.Uri): Promise<void> {
    try {
      const file = this.indexedFiles.get(uri.fsPath)
      if (file) {
        file.status.dirty = true
        await this.indexingService.reindexFile(uri.fsPath)
        this.refreshItem(file)
      }
    } catch (error) {
      this.logger.error('Error handling file change:', error)
    }
  }

  private async handleFileDeleted(uri: vscode.Uri): Promise<void> {
    try {
      this.indexedFiles.delete(uri.fsPath)
      this.refreshAll()
    } catch (error) {
      this.logger.error('Error handling file deletion:', error)
    }
  }

  private handleFileIndexed(file: IndexedFile): void {
    this.indexedFiles.set(file.path, file)
    this.refreshItem(file)
  }

  private handleIndexingProgress(progress: { file: string; progress: number }): void {
    const file = this.indexedFiles.get(progress.file)
    if (file) {
      file.status.progress = progress.progress
      this.refreshItem(file)
    }
  }

  // Public API methods
  public async refresh(): Promise<void> {
    try {
      const files = await this.indexingService.getAllIndexedFiles()
      this.indexedFiles.clear()
      
      for (const file of files) {
        this.indexedFiles.set(file.path, file)
      }
      
      this.refreshAll()
    } catch (error) {
      this.logger.error('Error refreshing index browser:', error)
      vscode.window.showErrorMessage('Failed to refresh index browser')
    }
  }

  public async refreshIndex(): Promise<void> {
    try {
      await this.indexingService.refreshIndex()
    } catch (error) {
      this.logger.error('Error refreshing index:', error)
    }
  }

  public setSearchQuery(query: string): void {
    this.state.searchQuery = query
    this.refreshAll()
    this.saveState()
  }

  public setSortBy(sortBy: IndexBrowserState['sortBy']): void {
    this.state.sortBy = sortBy
    this.refreshAll()
    this.saveState()
  }

  public setSortDirection(direction: 'asc' | 'desc'): void {
    this.state.sortDirection = direction
    this.refreshAll()
    this.saveState()
  }

  public setGroupBy(groupBy: IndexBrowserState['groupBy']): void {
    this.state.groupBy = groupBy
    this.refreshAll()
    this.saveState()
  }

  public setViewMode(viewMode: IndexBrowserState['viewMode']): void {
    this.state.viewMode = viewMode
    this.refreshAll()
    this.saveState()
  }

  public addFilter(filter: IndexFilter): void {
    this.state.filterBy.push(filter)
    this.refreshAll()
    this.saveState()
  }

  public removeFilter(index: number): void {
    this.state.filterBy.splice(index, 1)
    this.refreshAll()
    this.saveState()
  }

  public toggleShowHidden(): void {
    this.state.showHidden = !this.state.showHidden
    this.refreshAll()
    this.saveState()
  }

  public toggleShowDetails(): void {
    this.state.showDetails = !this.state.showDetails
    this.refreshAll()
    this.saveState()
  }

  public expandFolder(folderPath: string): void {
    this.state.expandedFolders.add(folderPath)
    this.saveState()
  }

  public collapseFolder(folderPath: string): void {
    this.state.expandedFolders.delete(folderPath)
    this.saveState()
  }

  public getSelectedFile(): IndexedFile | undefined {
    return this.state.selectedFile ? this.indexedFiles.get(this.state.selectedFile) : undefined
  }

  public setSelectedFile(filePath: string): void {
    this.state.selectedFile = filePath
    this.saveState()
  }

  public getState(): IndexBrowserState {
    return { ...this.state }
  }

  // Private helper methods
  private refreshAll(): void {
    this._onDidChangeTreeData.fire()
  }

  private refreshItem(file: IndexedFile): void {
    this._onDidChangeTreeData.fire()
  }

  private async loadState(): Promise<void> {
    try {
      const saved = await this.storageService.get<Partial<IndexBrowserState>>('indexBrowserState')
      if (saved) {
        this.state = {
          ...this.state,
          ...saved,
          expandedFolders: new Set(saved.expandedFolders || [])
        }
      }
    } catch (error) {
      this.logger.error('Error loading index browser state:', error)
    }
  }

  private async saveState(): Promise<void> {
    try {
      const stateToSave = {
        ...this.state,
        expandedFolders: Array.from(this.state.expandedFolders)
      }
      await this.storageService.set('indexBrowserState', stateToSave)
    } catch (error) {
      this.logger.error('Error saving index browser state:', error)
    }
  }

  public dispose(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
  }
}

export class IndexBrowser {
  private treeView: vscode.TreeView<IndexTreeItem>
  private provider: IndexBrowserProvider

  constructor(
    context: vscode.ExtensionContext,
    logger: Logger,
    storageService: StorageService,
    indexingService: IndexingService,
    semanticSearch: SemanticSearch,
    preferenceTracker: PreferenceTracker
  ) {
    this.provider = new IndexBrowserProvider(
      context,
      logger,
      storageService,
      indexingService,
      semanticSearch,
      preferenceTracker
    )

    this.treeView = vscode.window.createTreeView('indexBrowser', {
      treeDataProvider: this.provider,
      showCollapseAll: true,
      canSelectMany: false
    })

    this.setupCommands(context)
    this.setupEventListeners()
  }

  private setupCommands(context: vscode.ExtensionContext): void {
    const commands = [
      vscode.commands.registerCommand('indexBrowser.refresh', () => {
        this.provider.refresh()
      }),
      
      vscode.commands.registerCommand('indexBrowser.refreshIndex', () => {
        this.provider.refreshIndex()
      }),
      
      vscode.commands.registerCommand('indexBrowser.search', async () => {
        const query = await vscode.window.showInputBox({
          prompt: 'Search files and symbols',
          placeHolder: 'Enter search query...'
        })
        
        if (query) {
          this.provider.setSearchQuery(query)
        }
      }),
      
      vscode.commands.registerCommand('indexBrowser.clearSearch', () => {
        this.provider.setSearchQuery('')
      }),
      
      vscode.commands.registerCommand('indexBrowser.sortBy', async () => {
        const options = [
          { label: 'Name', value: 'name' },
          { label: 'Type', value: 'type' },
          { label: 'Modified', value: 'modified' },
          { label: 'Size', value: 'size' },
          { label: 'Complexity', value: 'complexity' },
          { label: 'Quality', value: 'quality' }
        ]
        
        const selected = await vscode.window.showQuickPick(options, {
          placeHolder: 'Sort files by...'
        })
        
        if (selected) {
          this.provider.setSortBy(selected.value as any)
        }
      }),
      
      vscode.commands.registerCommand('indexBrowser.groupBy', async () => {
        const options = [
          { label: 'None', value: 'none' },
          { label: 'Type', value: 'type' },
          { label: 'Language', value: 'language' },
          { label: 'Project', value: 'project' },
          { label: 'Quality', value: 'quality' }
        ]
        
        const selected = await vscode.window.showQuickPick(options, {
          placeHolder: 'Group files by...'
        })
        
        if (selected) {
          this.provider.setGroupBy(selected.value as any)
        }
      }),
      
      vscode.commands.registerCommand('indexBrowser.toggleShowHidden', () => {
        this.provider.toggleShowHidden()
      }),
      
      vscode.commands.registerCommand('indexBrowser.toggleShowDetails', () => {
        this.provider.toggleShowDetails()
      }),
      
      vscode.commands.registerCommand('indexBrowser.openFile', (item: IndexTreeItem) => {
        if (item.file) {
          vscode.commands.executeCommand('vscode.open', vscode.Uri.file(item.file.path))
        }
      }),
      
      vscode.commands.registerCommand('indexBrowser.revealInExplorer', (item: IndexTreeItem) => {
        if (item.file) {
          vscode.commands.executeCommand('revealFileInOS', vscode.Uri.file(item.file.path))
        }
      }),
      
      vscode.commands.registerCommand('indexBrowser.copyPath', (item: IndexTreeItem) => {
        if (item.file) {
          vscode.env.clipboard.writeText(item.file.path)
        }
      })
    ]

    context.subscriptions.push(...commands)
  }

  private setupEventListeners(): void {
    this.treeView.onDidChangeSelection((e) => {
      if (e.selection.length > 0 && e.selection[0].file) {
        this.provider.setSelectedFile(e.selection[0].file.path)
      }
    })

    this.treeView.onDidExpandElement((e) => {
      if (e.element.type === 'folder') {
        this.provider.expandFolder(e.element.label as string)
      }
    })

    this.treeView.onDidCollapseElement((e) => {
      if (e.element.type === 'folder') {
        this.provider.collapseFolder(e.element.label as string)
      }
    })
  }

  public reveal(filePath: string): void {
    // Implementation to reveal a specific file in the tree
    const file = this.provider['indexedFiles'].get(filePath)
    if (file) {
      const item = this.provider['createFileItem'](file)
      this.treeView.reveal(item, { expand: true, focus: true, select: true })
    }
  }

  public dispose(): void {
    this.treeView.dispose()
    this.provider.dispose()
  }
}