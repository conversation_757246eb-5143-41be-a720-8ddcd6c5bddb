import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { TypeScriptImport, TypeScriptExport } from './typescriptAnalyzer'

export interface ResolvedImport {
  originalPath: string
  resolvedPath: string
  isExternal: boolean
  isTypeOnly: boolean
  moduleType: 'es6' | 'commonjs' | 'amd' | 'umd' | 'system' | 'unknown'
  exports: ExportInfo[]
  importedSymbols: string[]
  line: number
  column: number
}

export interface ExportInfo {
  name: string
  type: 'named' | 'default' | 'namespace'
  isReexport: boolean
  originalModule?: string
  signature?: string
  documentation?: string
}

export interface ModuleInfo {
  filePath: string
  relativePath: string
  moduleType: 'es6' | 'commonjs' | 'amd' | 'umd' | 'system' | 'unknown'
  exports: ExportInfo[]
  imports: ResolvedImport[]
  isExternal: boolean
  packageName?: string
  version?: string
}

export interface ImportGraph {
  modules: Map<string, ModuleInfo>
  dependencies: Map<string, string[]>
  reverseDependencies: Map<string, string[]>
  externalModules: Set<string>
  unresolvedImports: Map<string, string[]>
}

export interface ResolutionOptions {
  projectRoot: string
  nodeModulesPath?: string
  extensions?: string[]
  resolveTypeDefinitions?: boolean
  followSymlinks?: boolean
  cacheResults?: boolean
}

export class ImportResolver {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private cache: Map<string, ResolvedImport> = new Map()
  private moduleInfoCache: Map<string, ModuleInfo> = new Map()
  private options: Required<ResolutionOptions>

  constructor(fileSystem: FileSystemService, db: ConnectionManager) {
    this.fileSystem = fileSystem
    this.db = db
    this.options = {
      projectRoot: '',
      nodeModulesPath: 'node_modules',
      extensions: ['.ts', '.tsx', '.js', '.jsx', '.json', '.d.ts'],
      resolveTypeDefinitions: true,
      followSymlinks: false,
      cacheResults: true
    }
  }

  async resolveImport(
    fromFile: string,
    importPath: string,
    options?: Partial<ResolutionOptions>
  ): Promise<ResolvedImport | null> {
    const resolveOptions = { ...this.options, ...options }
    const cacheKey = `${fromFile}:${importPath}`

    if (resolveOptions.cacheResults && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    const resolved = await this.performResolution(fromFile, importPath, resolveOptions)
    
    if (resolved && resolveOptions.cacheResults) {
      this.cache.set(cacheKey, resolved)
    }

    return resolved
  }

  private async performResolution(
    fromFile: string,
    importPath: string,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport | null> {
    // Check if it's a relative import
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      return await this.resolveRelativeImport(fromFile, importPath, options)
    }

    // Check if it's an absolute import
    if (importPath.startsWith('/')) {
      return await this.resolveAbsoluteImport(importPath, options)
    }

    // Check if it's a module import
    return await this.resolveModuleImport(fromFile, importPath, options)
  }

  private async resolveRelativeImport(
    fromFile: string,
    importPath: string,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport | null> {
    const fromDir = this.fileSystem.dirname(fromFile)
    const basePath = this.fileSystem.resolve(fromDir, importPath)
    
    // Try to resolve with different extensions
    for (const ext of options.extensions) {
      const fullPath = basePath + ext
      if (await this.fileSystem.exists(fullPath)) {
        return await this.createResolvedImport(importPath, fullPath, false, options)
      }
    }

    // Try index files
    for (const ext of options.extensions) {
      const indexPath = this.fileSystem.joinPath(basePath, `index${ext}`)
      if (await this.fileSystem.exists(indexPath)) {
        return await this.createResolvedImport(importPath, indexPath, false, options)
      }
    }

    return null
  }

  private async resolveAbsoluteImport(
    importPath: string,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport | null> {
    const fullPath = this.fileSystem.resolve(options.projectRoot, importPath.substring(1))
    
    if (await this.fileSystem.exists(fullPath)) {
      return await this.createResolvedImport(importPath, fullPath, false, options)
    }

    return null
  }

  private async resolveModuleImport(
    fromFile: string,
    importPath: string,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport | null> {
    // Check if it's a scoped package
    const isScoped = importPath.startsWith('@')
    const packageName = isScoped ? 
      importPath.split('/').slice(0, 2).join('/') : 
      importPath.split('/')[0]

    // Try to find in node_modules
    const nodeModulesPath = this.fileSystem.joinPath(options.projectRoot, options.nodeModulesPath)
    const packagePath = this.fileSystem.joinPath(nodeModulesPath, packageName)

    if (await this.fileSystem.exists(packagePath)) {
      const resolvedPath = await this.resolvePackageImport(packagePath, importPath, packageName, options)
      if (resolvedPath) {
        return await this.createResolvedImport(importPath, resolvedPath, true, options)
      }
    }

    // Try to resolve as built-in module
    if (this.isBuiltInModule(packageName)) {
      return await this.createResolvedImport(importPath, importPath, true, options)
    }

    return null
  }

  private async resolvePackageImport(
    packagePath: string,
    importPath: string,
    packageName: string,
    options: Required<ResolutionOptions>
  ): Promise<string | null> {
    const packageJsonPath = this.fileSystem.joinPath(packagePath, 'package.json')
    
    if (await this.fileSystem.exists(packageJsonPath)) {
      const packageJson = JSON.parse(await this.fileSystem.readFile(packageJsonPath))
      
      // Check if importing a specific file from the package
      if (importPath !== packageName) {
        const subPath = importPath.substring(packageName.length + 1)
        const fullPath = this.fileSystem.joinPath(packagePath, subPath)
        
        for (const ext of options.extensions) {
          const pathWithExt = fullPath + ext
          if (await this.fileSystem.exists(pathWithExt)) {
            return pathWithExt
          }
        }
      }

      // Check main entry points
      const mainFields = ['main', 'module', 'browser', 'types', 'typings']
      for (const field of mainFields) {
        if (packageJson[field]) {
          const mainPath = this.fileSystem.joinPath(packagePath, packageJson[field])
          if (await this.fileSystem.exists(mainPath)) {
            return mainPath
          }
        }
      }

      // Try index files
      for (const ext of options.extensions) {
        const indexPath = this.fileSystem.joinPath(packagePath, `index${ext}`)
        if (await this.fileSystem.exists(indexPath)) {
          return indexPath
        }
      }
    }

    return null
  }

  private async createResolvedImport(
    originalPath: string,
    resolvedPath: string,
    isExternal: boolean,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport> {
    const exports = await this.getModuleExports(resolvedPath)
    const moduleType = await this.detectModuleType(resolvedPath)

    return {
      originalPath,
      resolvedPath,
      isExternal,
      isTypeOnly: false,
      moduleType,
      exports,
      importedSymbols: [],
      line: 0,
      column: 0
    }
  }

  private async getModuleExports(filePath: string): Promise<ExportInfo[]> {
    if (!await this.fileSystem.exists(filePath)) {
      return []
    }

    // Check if it's a TypeScript/JavaScript file
    const ext = this.fileSystem.extname(filePath)
    if (!['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
      return []
    }

    try {
      const content = await this.fileSystem.readFile(filePath)
      return this.parseExports(content)
    } catch {
      return []
    }
  }

  private parseExports(content: string): ExportInfo[] {
    const exports: ExportInfo[] = []
    const lines = content.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // Named exports
      const namedExportMatch = line.match(/^export\s+\{([^}]+)\}/)
      if (namedExportMatch) {
        const exportNames = namedExportMatch[1]
          .split(',')
          .map(name => name.trim())
          .filter(name => name.length > 0)
        
        exportNames.forEach(name => {
          exports.push({
            name,
            type: 'named',
            isReexport: false
          })
        })
      }

      // Default export
      if (line.startsWith('export default')) {
        exports.push({
          name: 'default',
          type: 'default',
          isReexport: false
        })
      }

      // Function/class/interface exports
      const declarationMatch = line.match(/^export\s+(function|class|interface|type|const|let|var)\s+(\w+)/)
      if (declarationMatch) {
        exports.push({
          name: declarationMatch[2],
          type: 'named',
          isReexport: false
        })
      }

      // Re-exports
      const reexportMatch = line.match(/^export\s+(.+?)\s+from\s+['"](.+?)['"]/)
      if (reexportMatch) {
        const exportClause = reexportMatch[1]
        const fromModule = reexportMatch[2]
        
        if (exportClause === '*') {
          exports.push({
            name: '*',
            type: 'namespace',
            isReexport: true,
            originalModule: fromModule
          })
        } else {
          const namedReexports = exportClause.match(/\{([^}]+)\}/)
          if (namedReexports) {
            const names = namedReexports[1]
              .split(',')
              .map(name => name.trim())
              .filter(name => name.length > 0)
            
            names.forEach(name => {
              exports.push({
                name,
                type: 'named',
                isReexport: true,
                originalModule: fromModule
              })
            })
          }
        }
      }
    }

    return exports
  }

  private async detectModuleType(filePath: string): Promise<ModuleInfo['moduleType']> {
    if (!await this.fileSystem.exists(filePath)) {
      return 'unknown'
    }

    try {
      const content = await this.fileSystem.readFile(filePath)
      
      // Check for ES6 imports/exports
      if (content.includes('import ') || content.includes('export ')) {
        return 'es6'
      }

      // Check for CommonJS
      if (content.includes('require(') || content.includes('module.exports') || content.includes('exports.')) {
        return 'commonjs'
      }

      // Check for AMD
      if (content.includes('define(') && content.includes('require')) {
        return 'amd'
      }

      // Check for UMD
      if (content.includes('(function (root, factory)') || content.includes('typeof exports')) {
        return 'umd'
      }

      return 'unknown'
    } catch {
      return 'unknown'
    }
  }

  private isBuiltInModule(moduleName: string): boolean {
    const builtInModules = [
      'fs', 'path', 'os', 'util', 'crypto', 'http', 'https', 'url', 'querystring',
      'events', 'stream', 'buffer', 'child_process', 'cluster', 'dgram', 'dns',
      'net', 'tls', 'readline', 'repl', 'vm', 'zlib', 'assert', 'console',
      'constants', 'domain', 'module', 'process', 'punycode', 'string_decoder',
      'sys', 'timers', 'tty', 'worker_threads'
    ]

    return builtInModules.includes(moduleName)
  }

  async buildImportGraph(
    projectRoot: string,
    filePaths: string[],
    options?: Partial<ResolutionOptions>
  ): Promise<ImportGraph> {
    const resolveOptions = { ...this.options, projectRoot, ...options }
    
    const graph: ImportGraph = {
      modules: new Map(),
      dependencies: new Map(),
      reverseDependencies: new Map(),
      externalModules: new Set(),
      unresolvedImports: new Map()
    }

    for (const filePath of filePaths) {
      const moduleInfo = await this.getModuleInfo(filePath, resolveOptions)
      graph.modules.set(filePath, moduleInfo)

      const dependencies: string[] = []
      const unresolvedImports: string[] = []

      for (const importInfo of moduleInfo.imports) {
        if (importInfo.resolvedPath) {
          dependencies.push(importInfo.resolvedPath)
          
          if (importInfo.isExternal) {
            graph.externalModules.add(importInfo.resolvedPath)
          }

          // Update reverse dependencies
          if (!graph.reverseDependencies.has(importInfo.resolvedPath)) {
            graph.reverseDependencies.set(importInfo.resolvedPath, [])
          }
          graph.reverseDependencies.get(importInfo.resolvedPath)!.push(filePath)
        } else {
          unresolvedImports.push(importInfo.originalPath)
        }
      }

      graph.dependencies.set(filePath, dependencies)
      
      if (unresolvedImports.length > 0) {
        graph.unresolvedImports.set(filePath, unresolvedImports)
      }
    }

    return graph
  }

  private async getModuleInfo(
    filePath: string,
    options: Required<ResolutionOptions>
  ): Promise<ModuleInfo> {
    const cacheKey = filePath
    
    if (options.cacheResults && this.moduleInfoCache.has(cacheKey)) {
      return this.moduleInfoCache.get(cacheKey)!
    }

    const relativePath = this.fileSystem.relative(options.projectRoot, filePath)
    const moduleType = await this.detectModuleType(filePath)
    const exports = await this.getModuleExports(filePath)
    const imports = await this.getModuleImports(filePath, options)
    const isExternal = !filePath.startsWith(options.projectRoot)

    const moduleInfo: ModuleInfo = {
      filePath,
      relativePath,
      moduleType,
      exports,
      imports,
      isExternal
    }

    if (options.cacheResults) {
      this.moduleInfoCache.set(cacheKey, moduleInfo)
    }

    return moduleInfo
  }

  private async getModuleImports(
    filePath: string,
    options: Required<ResolutionOptions>
  ): Promise<ResolvedImport[]> {
    if (!await this.fileSystem.exists(filePath)) {
      return []
    }

    const ext = this.fileSystem.extname(filePath)
    if (!['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
      return []
    }

    try {
      const content = await this.fileSystem.readFile(filePath)
      const importStatements = this.parseImports(content)
      const resolvedImports: ResolvedImport[] = []

      for (const importStatement of importStatements) {
        const resolved = await this.resolveImport(filePath, importStatement.path, options)
        if (resolved) {
          resolved.importedSymbols = importStatement.symbols
          resolved.line = importStatement.line
          resolved.column = importStatement.column
          resolved.isTypeOnly = importStatement.isTypeOnly
          resolvedImports.push(resolved)
        }
      }

      return resolvedImports
    } catch {
      return []
    }
  }

  private parseImports(content: string): Array<{
    path: string
    symbols: string[]
    line: number
    column: number
    isTypeOnly: boolean
  }> {
    const imports: Array<{
      path: string
      symbols: string[]
      line: number
      column: number
      isTypeOnly: boolean
    }> = []

    const lines = content.split('\n')

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      
      // ES6 imports
      const importMatch = line.match(/^import\s+(.+?)\s+from\s+['"](.+?)['"]/)
      if (importMatch) {
        const importClause = importMatch[1]
        const modulePath = importMatch[2]
        const symbols = this.parseImportClause(importClause)
        const isTypeOnly = line.includes('import type')

        imports.push({
          path: modulePath,
          symbols,
          line: i + 1,
          column: 0,
          isTypeOnly
        })
      }

      // CommonJS require
      const requireMatch = line.match(/(?:const|let|var)\s+(.+?)\s*=\s*require\(['"](.+?)['"]\)/)
      if (requireMatch) {
        const importClause = requireMatch[1]
        const modulePath = requireMatch[2]
        const symbols = [importClause.replace(/[{}]/g, '').trim()]

        imports.push({
          path: modulePath,
          symbols,
          line: i + 1,
          column: 0,
          isTypeOnly: false
        })
      }
    }

    return imports
  }

  private parseImportClause(clause: string): string[] {
    const symbols: string[] = []
    
    if (clause.includes('{')) {
      const namedImports = clause.match(/\{([^}]+)\}/)
      if (namedImports) {
        const names = namedImports[1]
          .split(',')
          .map(name => name.trim())
          .filter(name => name.length > 0)
        symbols.push(...names)
      }
    } else if (clause.includes('* as ')) {
      const namespaceMatch = clause.match(/\*\s+as\s+(\w+)/)
      if (namespaceMatch) {
        symbols.push(namespaceMatch[1])
      }
    } else {
      symbols.push(clause.trim())
    }

    return symbols
  }

  clearCache(): void {
    this.cache.clear()
    this.moduleInfoCache.clear()
  }

  getImportGraph(): ImportGraph | null {
    // This would return the last built import graph
    // Implementation depends on how you want to store the graph
    return null
  }
}