import * as vscode from 'vscode'

// Core Selection Analysis Types
export interface SelectionAnalysis {
  selection: SelectionInfo
  content: ContentAnalysis
  structure: StructuralAnalysis
  semantics: SemanticAnalysis
  intent: IntentAnalysis
  suggestions: SelectionSuggestion[]
  metrics: SelectionMetrics
  relationships: RelationshipAnalysis
  quality: QualityAnalysis
  opportunities: OpportunityAnalysis
}

// Selection Info Types
export interface SelectionInfo {
  range: vscode.Range
  text: string
  language: string
  filePath: string
  size: SelectionSize
  boundaries: SelectionBoundaries
  completeness: CompletenessAnalysis
  context: SelectionContext
}

export interface SelectionSize {
  characters: number
  lines: number
  tokens: number
  bytes: number
  complexity: number
  depth: number
}

export interface SelectionBoundaries {
  type: 'complete' | 'partial' | 'cross_boundary' | 'invalid'
  startContext: BoundaryContext
  endContext: BoundaryContext
  suggestedExpansion?: vscode.Range
  confidence: number
}

export interface BoundaryContext {
  tokenType: string
  syntaxElement: string
  indentLevel: number
  isLineStart: boolean
  isLineEnd: boolean
  isBlockBoundary: boolean
}

export interface CompletenessAnalysis {
  isSyntacticallyComplete: boolean
  isSemanticallyComplete: boolean
  isLogicallyComplete: boolean
  missingElements: string[]
  confidence: number
}

export interface SelectionContext {
  parentElement?: CodeElement
  siblingElements: CodeElement[]
  childElements: CodeElement[]
  scopeChain: ScopeInfo[]
  nearbyCode: string
}

export interface CodeElement {
  type: 'function' | 'class' | 'method' | 'variable' | 'statement' | 'expression' | 'block' | 'comment'
  name?: string
  range: vscode.Range
  signature?: string
  visibility?: 'public' | 'private' | 'protected'
  isExported?: boolean
  complexity: number
}

export interface ScopeInfo {
  type: 'global' | 'module' | 'class' | 'function' | 'block' | 'loop' | 'conditional'
  name?: string
  range: vscode.Range
  variables: VariableInfo[]
  level: number
}

export interface VariableInfo {
  name: string
  type?: string
  range: vscode.Range
  isParameter: boolean
  isConstant: boolean
  usageCount: number
  lastUsage: vscode.Position
}

// Content Analysis Types
export interface ContentAnalysis {
  type: ContentType
  categories: ContentCategory[]
  keywords: KeywordAnalysis
  literals: LiteralAnalysis
  identifiers: IdentifierAnalysis
  operators: OperatorAnalysis
  comments: CommentAnalysis
}

export interface ContentType {
  primary: 'code' | 'comment' | 'string' | 'documentation' | 'data' | 'mixed'
  secondary: string[]
  confidence: number
  reasoning: string[]
}

export interface ContentCategory {
  category: string
  percentage: number
  elements: string[]
  confidence: number
}

export interface KeywordAnalysis {
  keywords: string[]
  languageKeywords: string[]
  customKeywords: string[]
  frequency: Record<string, number>
  context: Record<string, string[]>
}

export interface LiteralAnalysis {
  strings: StringLiteral[]
  numbers: NumberLiteral[]
  booleans: BooleanLiteral[]
  nulls: NullLiteral[]
  patterns: LiteralPattern[]
}

export interface StringLiteral {
  value: string
  type: 'single' | 'double' | 'template' | 'regex'
  range: vscode.Range
  isMultiline: boolean
  containsInterpolation: boolean
}

export interface NumberLiteral {
  value: number | string
  type: 'integer' | 'float' | 'scientific' | 'hex' | 'binary' | 'octal'
  range: vscode.Range
  isConstant: boolean
}

export interface BooleanLiteral {
  value: boolean
  range: vscode.Range
  context: string
}

export interface NullLiteral {
  type: 'null' | 'undefined' | 'None' | 'nil'
  range: vscode.Range
  context: string
}

export interface LiteralPattern {
  pattern: string
  occurrences: number
  type: 'magic_number' | 'hardcoded_string' | 'repeated_value'
  severity: 'low' | 'medium' | 'high'
}

export interface IdentifierAnalysis {
  variables: IdentifierInfo[]
  functions: IdentifierInfo[]
  classes: IdentifierInfo[]
  modules: IdentifierInfo[]
  namingConventions: NamingConventionAnalysis
  undefinedReferences: string[]
}

export interface IdentifierInfo {
  name: string
  type: string
  range: vscode.Range
  definition?: vscode.Location
  references: vscode.Location[]
  scope: string
  isDefinition: boolean
  isUsage: boolean
}

export interface NamingConventionAnalysis {
  camelCase: number
  snakeCase: number
  pascalCase: number
  kebabCase: number
  consistent: boolean
  violations: string[]
}

export interface OperatorAnalysis {
  arithmeticOperators: OperatorInfo[]
  comparisonOperators: OperatorInfo[]
  logicalOperators: OperatorInfo[]
  assignmentOperators: OperatorInfo[]
  specialOperators: OperatorInfo[]
  operatorComplexity: number
}

export interface OperatorInfo {
  operator: string
  occurrences: number
  contexts: string[]
  complexity: number
}

export interface CommentAnalysis {
  singleLineComments: CommentInfo[]
  multiLineComments: CommentInfo[]
  docComments: CommentInfo[]
  todoComments: TodoComment[]
  commentDensity: number
  documentationCoverage: number
}

export interface CommentInfo {
  text: string
  range: vscode.Range
  type: 'single' | 'multi' | 'doc'
  language?: string
  tags?: string[]
}

export interface TodoComment {
  type: 'TODO' | 'FIXME' | 'HACK' | 'NOTE' | 'WARNING'
  text: string
  range: vscode.Range
  priority?: 'low' | 'medium' | 'high'
  assignee?: string
}

// Structural Analysis Types
export interface StructuralAnalysis {
  elements: StructuralElement[]
  hierarchy: HierarchyNode
  complexity: ComplexityAnalysis
  patterns: StructuralPattern[]
  dependencies: DependencyInfo[]
  metrics: StructuralMetrics
}

export interface StructuralElement {
  type: ElementType
  name?: string
  range: vscode.Range
  children: StructuralElement[]
  parent?: string
  attributes: Record<string, any>
  visibility?: 'public' | 'private' | 'protected'
  modifiers: string[]
}

export type ElementType = 
  | 'class' | 'interface' | 'function' | 'method' | 'property'
  | 'variable' | 'parameter' | 'block' | 'statement' | 'expression'
  | 'import' | 'export' | 'decorator' | 'annotation'

export interface HierarchyNode {
  element: StructuralElement
  children: HierarchyNode[]
  level: number
  path: string[]
}

export interface ComplexityAnalysis {
  cyclomaticComplexity: number
  cognitiveComplexity: number
  nestingDepth: number
  parameterCount: number
  lineCount: number
  statementCount: number
  branchCount: number
}

export interface StructuralPattern {
  pattern: string
  type: 'design' | 'anti' | 'code_smell'
  occurrences: number
  locations: vscode.Range[]
  severity: 'low' | 'medium' | 'high'
  description: string
}

export interface DependencyInfo {
  source: string
  target: string
  type: 'import' | 'inheritance' | 'composition' | 'reference'
  strength: 'strong' | 'weak'
  location: vscode.Range
}

export interface StructuralMetrics {
  cohesion: number
  coupling: number
  abstractness: number
  instability: number
  maintenanceIndex: number
}

// Semantic Analysis Types
export interface SemanticAnalysis {
  purpose: PurposeAnalysis
  dataFlow: DataFlowAnalysis
  controlFlow: ControlFlowAnalysis
  sideEffects: SideEffectAnalysis
  invariants: InvariantAnalysis
  contracts: ContractAnalysis
}

export interface PurposeAnalysis {
  primaryPurpose: string
  secondaryPurposes: string[]
  domain: string
  confidence: number
  evidence: string[]
}

export interface DataFlowAnalysis {
  inputs: DataFlowNode[]
  outputs: DataFlowNode[]
  transformations: DataTransformation[]
  dataTypes: DataTypeInfo[]
  flowComplexity: number
}

export interface DataFlowNode {
  name: string
  type: string
  source: 'parameter' | 'variable' | 'property' | 'return' | 'external'
  usage: 'read' | 'write' | 'read-write'
  locations: vscode.Range[]
}

export interface DataTransformation {
  from: DataFlowNode
  to: DataFlowNode
  operation: string
  complexity: number
  location: vscode.Range
}

export interface DataTypeInfo {
  variable: string
  declaredType?: string
  inferredType?: string
  usages: TypeUsage[]
  mutations: number
}

export interface TypeUsage {
  location: vscode.Range
  context: string
  operation: string
}

export interface ControlFlowAnalysis {
  entryPoints: vscode.Range[]
  exitPoints: vscode.Range[]
  branches: BranchInfo[]
  loops: LoopInfo[]
  exceptions: ExceptionInfo[]
  flowGraph: FlowNode[]
}

export interface BranchInfo {
  type: 'if' | 'switch' | 'ternary'
  condition: string
  trueBranch: vscode.Range
  falseBranch?: vscode.Range
  complexity: number
}

export interface LoopInfo {
  type: 'for' | 'while' | 'do-while' | 'foreach'
  condition?: string
  body: vscode.Range
  iterationCount?: number
  complexity: number
}

export interface ExceptionInfo {
  type: 'try-catch' | 'throw' | 'finally'
  exception?: string
  handler?: vscode.Range
  location: vscode.Range
}

export interface FlowNode {
  id: string
  type: 'start' | 'end' | 'process' | 'decision' | 'merge'
  label: string
  location?: vscode.Range
  connections: string[]
}

export interface SideEffectAnalysis {
  hasSideEffects: boolean
  effects: SideEffect[]
  purity: number
  idempotent: boolean
  deterministic: boolean
}

export interface SideEffect {
  type: 'mutation' | 'io' | 'network' | 'database' | 'file' | 'console' | 'dom'
  target: string
  operation: string
  location: vscode.Range
  severity: 'low' | 'medium' | 'high'
}

export interface InvariantAnalysis {
  invariants: Invariant[]
  assumptions: Assumption[]
  validations: Validation[]
  confidence: number
}

export interface Invariant {
  condition: string
  type: 'precondition' | 'postcondition' | 'loop_invariant'
  location: vscode.Range
  strength: 'strong' | 'weak'
}

export interface Assumption {
  statement: string
  basis: string
  confidence: number
  risks: string[]
}

export interface Validation {
  type: 'input' | 'output' | 'state'
  condition: string
  location: vscode.Range
  enforced: boolean
}

export interface ContractAnalysis {
  interfaces: InterfaceContract[]
  behaviors: BehaviorContract[]
  constraints: Constraint[]
  guarantees: Guarantee[]
}

export interface InterfaceContract {
  name: string
  inputs: ParameterContract[]
  outputs: ReturnContract
  exceptions: string[]
}

export interface ParameterContract {
  name: string
  type: string
  constraints: string[]
  optional: boolean
  defaultValue?: any
}

export interface ReturnContract {
  type: string
  constraints: string[]
  nullable: boolean
}

export interface BehaviorContract {
  name: string
  description: string
  preconditions: string[]
  postconditions: string[]
  invariants: string[]
}

export interface Constraint {
  type: 'type' | 'range' | 'format' | 'relationship'
  expression: string
  severity: 'error' | 'warning'
  location: vscode.Range
}

export interface Guarantee {
  type: 'performance' | 'memory' | 'correctness' | 'security'
  statement: string
  confidence: number
  evidence: string[]
}

// Intent Analysis Types
export interface IntentAnalysis {
  primaryIntent: DeveloperIntent
  secondaryIntents: DeveloperIntent[]
  taskType: TaskType
  problemDomain: ProblemDomain
  implementationStrategy: ImplementationStrategy
  confidence: number
}

export interface DeveloperIntent {
  action: string
  target: string
  purpose: string
  confidence: number
  evidence: string[]
}

export interface TaskType {
  category: 'feature' | 'bugfix' | 'refactor' | 'optimization' | 'documentation' | 'test'
  subcategory: string
  complexity: 'simple' | 'moderate' | 'complex'
  estimatedEffort: number
}

export interface ProblemDomain {
  domain: string
  subdomains: string[]
  technologies: string[]
  patterns: string[]
  constraints: string[]
}

export interface ImplementationStrategy {
  approach: string
  patterns: string[]
  alternatives: string[]
  tradeoffs: Tradeoff[]
  recommendations: string[]
}

export interface Tradeoff {
  option: string
  pros: string[]
  cons: string[]
  recommendation: string
}

// Suggestion Types
export interface SelectionSuggestion {
  type: SuggestionType
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  category: SuggestionCategory
  impact: ImpactAnalysis
  implementation: ImplementationGuide
  alternatives: AlternativeSuggestion[]
  references: Reference[]
}

export type SuggestionType = 
  | 'refactor' | 'optimize' | 'simplify' | 'extract' | 'inline'
  | 'rename' | 'reorder' | 'combine' | 'split' | 'remove'
  | 'add_validation' | 'add_error_handling' | 'add_logging'
  | 'improve_naming' | 'improve_structure' | 'improve_performance'
  | 'fix_bug' | 'fix_style' | 'fix_security' | 'fix_accessibility'

export type SuggestionCategory = 
  | 'code_quality' | 'performance' | 'security' | 'maintainability'
  | 'readability' | 'testability' | 'accessibility' | 'best_practice'
  | 'design_pattern' | 'architecture' | 'documentation' | 'style'

export interface ImpactAnalysis {
  scope: 'local' | 'file' | 'module' | 'project'
  affectedElements: string[]
  benefits: string[]
  risks: string[]
  effort: 'low' | 'medium' | 'high'
}

export interface ImplementationGuide {
  steps: ImplementationStep[]
  codeChanges: CodeChange[]
  testingStrategy: string
  rollbackPlan: string
}

export interface ImplementationStep {
  order: number
  description: string
  action: string
  automated: boolean
  validation: string
}

export interface CodeChange {
  file: string
  range: vscode.Range
  oldCode: string
  newCode: string
  description: string
}

export interface AlternativeSuggestion {
  title: string
  description: string
  tradeoffs: string[]
  whenToUse: string
}

export interface Reference {
  title: string
  url: string
  type: 'documentation' | 'article' | 'tutorial' | 'specification'
  relevance: number
}

// Metrics Types
export interface SelectionMetrics {
  analysisTime: number
  cacheHit: boolean
  accuracy: number
  completeness: number
  relevance: number
  performance: PerformanceMetrics
}

export interface PerformanceMetrics {
  tokenCount: number
  parseTime: number
  analysisTime: number
  suggestionTime: number
  totalTime: number
}

// Relationship Analysis Types
export interface RelationshipAnalysis {
  dependencies: DependencyRelation[]
  associations: AssociationRelation[]
  hierarchies: HierarchyRelation[]
  dataFlows: DataFlowRelation[]
  callGraphs: CallGraphRelation[]
}

export interface DependencyRelation {
  from: string
  to: string
  type: 'uses' | 'extends' | 'implements' | 'imports'
  strength: number
  location: vscode.Range
}

export interface AssociationRelation {
  entity1: string
  entity2: string
  type: 'composition' | 'aggregation' | 'reference'
  multiplicity: string
  bidirectional: boolean
}

export interface HierarchyRelation {
  parent: string
  child: string
  type: 'inheritance' | 'composition' | 'nesting'
  depth: number
  location: vscode.Range
}

export interface DataFlowRelation {
  source: string
  destination: string
  dataType: string
  transformation?: string
  location: vscode.Range
}

export interface CallGraphRelation {
  caller: string
  callee: string
  parameters: string[]
  returnType?: string
  location: vscode.Range
}

// Quality Analysis Types
export interface QualityAnalysis {
  scores: QualityScores
  issues: QualityIssue[]
  suggestions: QualityImprovement[]
  benchmarks: QualityBenchmark[]
  trends: QualityTrend[]
}

export interface QualityScores {
  overall: number
  readability: number
  maintainability: number
  testability: number
  performance: number
  security: number
  documentation: number
}

export interface QualityIssue {
  type: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  message: string
  location: vscode.Range
  rule: string
  fixable: boolean
  fix?: string
}

export interface QualityImprovement {
  area: string
  currentScore: number
  targetScore: number
  actions: string[]
  priority: number
  effort: number
}

export interface QualityBenchmark {
  metric: string
  value: number
  benchmark: number
  percentile: number
  trend: 'improving' | 'stable' | 'declining'
}

export interface QualityTrend {
  metric: string
  history: Array<{ timestamp: number; value: number }>
  forecast: number
  confidence: number
}

// Opportunity Analysis Types
export interface OpportunityAnalysis {
  refactoring: RefactoringOpportunity[]
  optimization: OptimizationOpportunity[]
  modernization: ModernizationOpportunity[]
  learning: LearningOpportunity[]
}

export interface RefactoringOpportunity {
  type: string
  description: string
  benefit: string
  effort: 'low' | 'medium' | 'high'
  risk: 'low' | 'medium' | 'high'
  location: vscode.Range
  preview?: string
}

export interface OptimizationOpportunity {
  type: 'performance' | 'memory' | 'size' | 'complexity'
  description: string
  currentValue: number
  potentialValue: number
  improvement: number
  technique: string
}

export interface ModernizationOpportunity {
  feature: string
  currentApproach: string
  modernApproach: string
  benefits: string[]
  migrationPath: string[]
  compatibility: string[]
}

export interface LearningOpportunity {
  topic: string
  reason: string
  resources: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  relevance: number
}