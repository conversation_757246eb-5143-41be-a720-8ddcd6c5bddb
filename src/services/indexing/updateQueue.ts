import { EventEmitter } from 'events'
import { ConnectionManager } from '../database/connectionManager'

export interface QueueItem {
  id: string
  type: 'index' | 'reindex' | 'delete' | 'update'
  filePath: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  createdAt: number
  attempts: number
  maxAttempts: number
  lastError?: string
  metadata?: any
}

export interface QueueStats {
  totalItems: number
  pendingItems: number
  processingItems: number
  failedItems: number
  completedItems: number
  averageProcessingTime: number
  lastProcessedAt: number
}

export interface QueueOptions {
  maxConcurrent?: number
  maxRetries?: number
  retryDelay?: number
  processTimeout?: number
  batchSize?: number
  priorityLevels?: Record<string, number>
}

export class UpdateQueue extends EventEmitter {
  private db: ConnectionManager
  private queue: QueueItem[] = []
  private processing = new Set<string>()
  private isRunning = false
  private processingStats: Map<string, number> = new Map()
  private options: Required<QueueOptions>

  private readonly defaultOptions: Required<QueueOptions> = {
    maxConcurrent: 5,
    maxRetries: 3,
    retryDelay: 1000,
    processTimeout: 30000,
    batchSize: 10,
    priorityLevels: {
      urgent: 4,
      high: 3,
      medium: 2,
      low: 1
    }
  }

  constructor(db: ConnectionManager, options: QueueOptions = {}) {
    super()
    this.db = db
    this.options = { ...this.defaultOptions, ...options }
  }

  async initialize(): Promise<void> {
    await this.createQueueTable()
    await this.loadPersistedQueue()
  }

  private async createQueueTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS index_queue (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        file_path TEXT NOT NULL,
        priority TEXT NOT NULL,
        created_at INTEGER NOT NULL,
        attempts INTEGER NOT NULL DEFAULT 0,
        max_attempts INTEGER NOT NULL DEFAULT 3,
        last_error TEXT,
        metadata TEXT,
        status TEXT NOT NULL DEFAULT 'pending',
        processed_at INTEGER
      )
    `
    await this.db.runSql(sql)

    await this.db.runSql('CREATE INDEX IF NOT EXISTS idx_queue_status ON index_queue(status)')
    await this.db.runSql('CREATE INDEX IF NOT EXISTS idx_queue_priority ON index_queue(priority)')
    await this.db.runSql('CREATE INDEX IF NOT EXISTS idx_queue_created_at ON index_queue(created_at)')
  }

  private async loadPersistedQueue(): Promise<void> {
    const persistedItems = await this.db.allSql(
      'SELECT * FROM index_queue WHERE status = ? OR status = ? ORDER BY created_at',
      ['pending', 'processing']
    )

    for (const item of persistedItems) {
      this.queue.push({
        id: item.id,
        type: item.type,
        filePath: item.file_path,
        priority: item.priority,
        createdAt: item.created_at,
        attempts: item.attempts,
        maxAttempts: item.max_attempts,
        lastError: item.last_error,
        metadata: item.metadata ? JSON.parse(item.metadata) : undefined
      })
    }

    // Reset processing items to pending
    await this.db.runSql('UPDATE index_queue SET status = ? WHERE status = ?', ['pending', 'processing'])
  }

  async add(item: Omit<QueueItem, 'id' | 'createdAt' | 'attempts'>): Promise<string> {
    const id = this.generateId()
    const queueItem: QueueItem = {
      id,
      createdAt: Date.now(),
      attempts: 0,
      maxAttempts: this.options.maxRetries,
      ...item
    }

    this.queue.push(queueItem)
    await this.persistQueueItem(queueItem)

    this.emit('itemAdded', queueItem)
    
    if (this.isRunning) {
      this.processQueue()
    }

    return id
  }

  async addBatch(items: Omit<QueueItem, 'id' | 'createdAt' | 'attempts'>[]): Promise<string[]> {
    const ids: string[] = []
    const queueItems: QueueItem[] = []

    for (const item of items) {
      const id = this.generateId()
      const queueItem: QueueItem = {
        id,
        createdAt: Date.now(),
        attempts: 0,
        maxAttempts: this.options.maxRetries,
        ...item
      }
      queueItems.push(queueItem)
      ids.push(id)
    }

    this.queue.push(...queueItems)
    await this.persistQueueItems(queueItems)

    this.emit('batchAdded', queueItems)
    
    if (this.isRunning) {
      this.processQueue()
    }

    return ids
  }

  async remove(id: string): Promise<boolean> {
    const index = this.queue.findIndex(item => item.id === id)
    if (index === -1) {
      return false
    }

    this.queue.splice(index, 1)
    await this.db.runSql('DELETE FROM index_queue WHERE id = ?', [id])
    
    this.emit('itemRemoved', id)
    return true
  }

  async clear(): Promise<void> {
    this.queue = []
    await this.db.runSql('DELETE FROM index_queue')
    this.emit('queueCleared')
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    this.emit('started')
    await this.processQueue()
  }

  async stop(): Promise<void> {
    this.isRunning = false
    
    // Wait for current processing to complete
    while (this.processing.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    this.emit('stopped')
  }

  private async processQueue(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    const availableSlots = this.options.maxConcurrent - this.processing.size
    if (availableSlots <= 0) {
      return
    }

    const itemsToProcess = this.getNextItems(availableSlots)
    if (itemsToProcess.length === 0) {
      return
    }

    const processPromises = itemsToProcess.map(item => this.processItem(item))
    await Promise.allSettled(processPromises)

    // Continue processing if there are more items
    setImmediate(() => this.processQueue())
  }

  private getNextItems(count: number): QueueItem[] {
    const availableItems = this.queue.filter(item => !this.processing.has(item.id))
    
    // Sort by priority and creation time
    availableItems.sort((a, b) => {
      const priorityA = this.options.priorityLevels[a.priority] || 0
      const priorityB = this.options.priorityLevels[b.priority] || 0
      
      if (priorityA !== priorityB) {
        return priorityB - priorityA
      }
      
      return a.createdAt - b.createdAt
    })

    return availableItems.slice(0, count)
  }

  private async processItem(item: QueueItem): Promise<void> {
    this.processing.add(item.id)
    
    try {
      await this.updateItemStatus(item.id, 'processing')
      
      const startTime = Date.now()
      this.emit('itemProcessing', item)

      await this.executeItem(item)

      const processingTime = Date.now() - startTime
      this.processingStats.set(item.id, processingTime)

      await this.updateItemStatus(item.id, 'completed')
      this.removeFromQueue(item.id)
      
      this.emit('itemCompleted', item, processingTime)
    } catch (error) {
      await this.handleItemError(item, error)
    } finally {
      this.processing.delete(item.id)
    }
  }

  private async executeItem(item: QueueItem): Promise<void> {
    const timeout = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Processing timeout')), this.options.processTimeout)
    })

    const work = this.performWork(item)
    
    await Promise.race([work, timeout])
  }

  private async performWork(item: QueueItem): Promise<void> {
    switch (item.type) {
      case 'index':
        await this.handleIndexFile(item)
        break
      case 'reindex':
        await this.handleReindexFile(item)
        break
      case 'delete':
        await this.handleDeleteFile(item)
        break
      case 'update':
        await this.handleUpdateFile(item)
        break
      default:
        throw new Error(`Unknown queue item type: ${item.type}`)
    }
  }

  private async handleIndexFile(item: QueueItem): Promise<void> {
    this.emit('indexFile', item.filePath)
  }

  private async handleReindexFile(item: QueueItem): Promise<void> {
    this.emit('reindexFile', item.filePath)
  }

  private async handleDeleteFile(item: QueueItem): Promise<void> {
    this.emit('deleteFile', item.filePath)
  }

  private async handleUpdateFile(item: QueueItem): Promise<void> {
    this.emit('updateFile', item.filePath, item.metadata)
  }

  private async handleItemError(item: QueueItem, error: any): Promise<void> {
    item.attempts++
    item.lastError = error.toString()

    if (item.attempts >= item.maxAttempts) {
      await this.updateItemStatus(item.id, 'failed')
      this.emit('itemFailed', item, error)
    } else {
      await this.updateItemStatus(item.id, 'pending')
      this.emit('itemRetry', item, error)
      
      // Schedule retry with exponential backoff
      const delay = this.options.retryDelay * Math.pow(2, item.attempts - 1)
      setTimeout(() => {
        if (this.isRunning) {
          this.processQueue()
        }
      }, delay)
    }
  }

  private async persistQueueItem(item: QueueItem): Promise<void> {
    await this.db.runSql(
      `INSERT OR REPLACE INTO index_queue 
       (id, type, file_path, priority, created_at, attempts, max_attempts, last_error, metadata, status) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        item.id,
        item.type,
        item.filePath,
        item.priority,
        item.createdAt,
        item.attempts,
        item.maxAttempts,
        item.lastError,
        item.metadata ? JSON.stringify(item.metadata) : null,
        'pending'
      ]
    )
  }

  private async persistQueueItems(items: QueueItem[]): Promise<void> {
    await this.db.transaction(async (tx) => {
      for (const item of items) {
        await tx.runSql(
          `INSERT OR REPLACE INTO index_queue 
           (id, type, file_path, priority, created_at, attempts, max_attempts, last_error, metadata, status) 
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            item.id,
            item.type,
            item.filePath,
            item.priority,
            item.createdAt,
            item.attempts,
            item.maxAttempts,
            item.lastError,
            item.metadata ? JSON.stringify(item.metadata) : null,
            'pending'
          ]
        )
      }
    })
  }

  private async updateItemStatus(id: string, status: string): Promise<void> {
    await this.db.runSql(
      'UPDATE index_queue SET status = ?, processed_at = ? WHERE id = ?',
      [status, Date.now(), id]
    )
  }

  private removeFromQueue(id: string): void {
    const index = this.queue.findIndex(item => item.id === id)
    if (index !== -1) {
      this.queue.splice(index, 1)
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  async getStats(): Promise<QueueStats> {
    const totalItems = await this.db.getSql('SELECT COUNT(*) as count FROM index_queue')
    const pendingItems = await this.db.getSql('SELECT COUNT(*) as count FROM index_queue WHERE status = ?', ['pending'])
    const processingItems = await this.db.getSql('SELECT COUNT(*) as count FROM index_queue WHERE status = ?', ['processing'])
    const failedItems = await this.db.getSql('SELECT COUNT(*) as count FROM index_queue WHERE status = ?', ['failed'])
    const completedItems = await this.db.getSql('SELECT COUNT(*) as count FROM index_queue WHERE status = ?', ['completed'])

    const avgProcessingTime = await this.db.getSql(
      'SELECT AVG(processed_at - created_at) as avg_time FROM index_queue WHERE status = ? AND processed_at IS NOT NULL',
      ['completed']
    )

    const lastProcessed = await this.db.getSql(
      'SELECT MAX(processed_at) as last_processed FROM index_queue WHERE status = ?',
      ['completed']
    )

    return {
      totalItems: totalItems?.count || 0,
      pendingItems: pendingItems?.count || 0,
      processingItems: processingItems?.count || 0,
      failedItems: failedItems?.count || 0,
      completedItems: completedItems?.count || 0,
      averageProcessingTime: avgProcessingTime?.avg_time || 0,
      lastProcessedAt: lastProcessed?.last_processed || 0
    }
  }

  async getQueueItems(status?: string, limit?: number): Promise<QueueItem[]> {
    let sql = 'SELECT * FROM index_queue'
    const params: any[] = []

    if (status) {
      sql += ' WHERE status = ?'
      params.push(status)
    }

    sql += ' ORDER BY created_at DESC'

    if (limit) {
      sql += ' LIMIT ?'
      params.push(limit)
    }

    const rows = await this.db.allSql(sql, params)
    
    return rows.map(row => ({
      id: row.id,
      type: row.type,
      filePath: row.file_path,
      priority: row.priority,
      createdAt: row.created_at,
      attempts: row.attempts,
      maxAttempts: row.max_attempts,
      lastError: row.last_error,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    }))
  }

  async retryFailed(): Promise<void> {
    const failedItems = await this.getQueueItems('failed')
    
    for (const item of failedItems) {
      item.attempts = 0
      item.lastError = undefined
      await this.updateItemStatus(item.id, 'pending')
    }

    if (this.isRunning) {
      this.processQueue()
    }
  }

  async cleanupCompleted(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<number> {
    const cutoffTime = Date.now() - olderThanMs
    const result = await this.db.runSql(
      'DELETE FROM index_queue WHERE status = ? AND processed_at < ?',
      ['completed', cutoffTime]
    )

    return result.changes
  }

  size(): number {
    return this.queue.length
  }

  isProcessing(): boolean {
    return this.processing.size > 0
  }

  isRunning(): boolean {
    return this.isRunning
  }

  getProcessingCount(): number {
    return this.processing.size
  }
}