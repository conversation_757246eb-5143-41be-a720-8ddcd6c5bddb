import * as vscode from 'vscode'
import * as os from 'os'
import { logger } from './logger'
import { ErrorCode } from './errorHandler'

export interface SystemInfo {
  platform: string
  arch: string
  nodeVersion: string
  vscodeVersion: string
  extensionVersion: string
  totalMemory: number
  freeMemory: number
  cpus: number
}

export interface PerformanceMetrics {
  memoryUsage: NodeJS.MemoryUsage
  uptime: number
  activeHandles: number
  activeRequests: number
}

export interface DiagnosticReport {
  timestamp: Date
  systemInfo: SystemInfo
  performanceMetrics: PerformanceMetrics
  errorCounts: Record<ErrorCode, number>
  recentErrors: string[]
  extensionHealth: 'healthy' | 'warning' | 'error'
}

export class DiagnosticsService {
  private static instance: DiagnosticsService
  private errorCounts: Record<ErrorCode, number> = {} as Record<ErrorCode, number>
  private recentErrors: string[] = []
  private maxRecentErrors = 10

  static getInstance(): DiagnosticsService {
    if (!DiagnosticsService.instance) {
      DiagnosticsService.instance = new DiagnosticsService()
    }
    return DiagnosticsService.instance
  }

  recordError(error: Error, errorCode: ErrorCode): void {
    if (!this.errorCounts[errorCode]) {
      this.errorCounts[errorCode] = 0
    }
    this.errorCounts[errorCode]++

    this.recentErrors.push(`${new Date().toISOString()}: ${error.message}`)
    if (this.recentErrors.length > this.maxRecentErrors) {
      this.recentErrors.shift()
    }
  }

  getSystemInfo(): SystemInfo {
    const packageJson = require('../../../package.json')
    
    return {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      vscodeVersion: vscode.version,
      extensionVersion: packageJson.version,
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus().length
    }
  }

  getPerformanceMetrics(): PerformanceMetrics {
    return {
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      activeHandles: (process as any)._getActiveHandles().length,
      activeRequests: (process as any)._getActiveRequests().length
    }
  }

  generateDiagnosticReport(): DiagnosticReport {
    const systemInfo = this.getSystemInfo()
    const performanceMetrics = this.getPerformanceMetrics()
    const extensionHealth = this.assessExtensionHealth(performanceMetrics)

    return {
      timestamp: new Date(),
      systemInfo,
      performanceMetrics,
      errorCounts: { ...this.errorCounts },
      recentErrors: [...this.recentErrors],
      extensionHealth
    }
  }

  private assessExtensionHealth(metrics: PerformanceMetrics): 'healthy' | 'warning' | 'error' {
    const memoryUsageMB = metrics.memoryUsage.heapUsed / 1024 / 1024
    const totalErrors = Object.values(this.errorCounts).reduce((sum, count) => sum + count, 0)

    if (memoryUsageMB > 500 || totalErrors > 50) {
      return 'error'
    }
    
    if (memoryUsageMB > 200 || totalErrors > 10) {
      return 'warning'
    }

    return 'healthy'
  }

  exportDiagnostics(): string {
    const report = this.generateDiagnosticReport()
    return JSON.stringify(report, null, 2)
  }

  async saveDiagnosticsToFile(): Promise<void> {
    try {
      const report = this.generateDiagnosticReport()
      const filename = `diagnostics-${new Date().toISOString().replace(/[:.]/g, '-')}.json`
      
      const uri = await vscode.window.showSaveDialog({
        defaultUri: vscode.Uri.file(filename),
        filters: {
          'JSON files': ['json']
        }
      })

      if (uri) {
        await vscode.workspace.fs.writeFile(uri, Buffer.from(JSON.stringify(report, null, 2)))
        vscode.window.showInformationMessage(`Diagnostics saved to ${uri.fsPath}`)
      }
    } catch (error) {
      logger.error('Failed to save diagnostics', error as Error)
      vscode.window.showErrorMessage('Failed to save diagnostics file')
    }
  }

  showDiagnosticsPanel(): void {
    const report = this.generateDiagnosticReport()
    const panel = vscode.window.createWebviewPanel(
      'diagnostics',
      'Extension Diagnostics',
      vscode.ViewColumn.One,
      {
        enableScripts: false
      }
    )

    panel.webview.html = this.generateDiagnosticsHtml(report)
  }

  private generateDiagnosticsHtml(report: DiagnosticReport): string {
    const healthColor = report.extensionHealth === 'healthy' ? 'green' : 
                       report.extensionHealth === 'warning' ? 'orange' : 'red'

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Extension Diagnostics</title>
          <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
            .header { border-bottom: 1px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
            .health { color: ${healthColor}; font-weight: bold; }
            .section { margin-bottom: 20px; }
            .metric { margin: 5px 0; }
            .error-list { max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Extension Diagnostics</h1>
            <p>Generated: ${report.timestamp.toLocaleString()}</p>
            <p>Health Status: <span class="health">${report.extensionHealth.toUpperCase()}</span></p>
          </div>

          <div class="section">
            <h2>System Information</h2>
            <div class="metric">Platform: ${report.systemInfo.platform} (${report.systemInfo.arch})</div>
            <div class="metric">Node.js: ${report.systemInfo.nodeVersion}</div>
            <div class="metric">VS Code: ${report.systemInfo.vscodeVersion}</div>
            <div class="metric">Extension: ${report.systemInfo.extensionVersion}</div>
            <div class="metric">CPUs: ${report.systemInfo.cpus}</div>
            <div class="metric">Memory: ${(report.systemInfo.freeMemory / 1024 / 1024 / 1024).toFixed(1)}GB free / ${(report.systemInfo.totalMemory / 1024 / 1024 / 1024).toFixed(1)}GB total</div>
          </div>

          <div class="section">
            <h2>Performance Metrics</h2>
            <div class="metric">Uptime: ${(report.performanceMetrics.uptime / 60).toFixed(1)} minutes</div>
            <div class="metric">Heap Used: ${(report.performanceMetrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB</div>
            <div class="metric">Heap Total: ${(report.performanceMetrics.memoryUsage.heapTotal / 1024 / 1024).toFixed(1)}MB</div>
            <div class="metric">Active Handles: ${report.performanceMetrics.activeHandles}</div>
            <div class="metric">Active Requests: ${report.performanceMetrics.activeRequests}</div>
          </div>

          <div class="section">
            <h2>Error Counts</h2>
            <pre>${JSON.stringify(report.errorCounts, null, 2)}</pre>
          </div>

          <div class="section">
            <h2>Recent Errors</h2>
            <div class="error-list">
              ${report.recentErrors.map(error => `<div>${error}</div>`).join('')}
            </div>
          </div>
        </body>
      </html>
    `
  }

  clearDiagnostics(): void {
    this.errorCounts = {} as Record<ErrorCode, number>
    this.recentErrors = []
    logger.info('Diagnostics cleared')
  }
}

export const diagnostics = DiagnosticsService.getInstance()