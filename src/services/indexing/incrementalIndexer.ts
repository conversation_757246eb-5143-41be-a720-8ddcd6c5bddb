import { EventEmitter } from 'events'
import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { MetadataExtractor, FileMetadata, ImportExportMetadata } from './metadataExtractor'
import { FileWatcherService, FileWatcherEvent } from '../fileWatcher'
import { FileFilter } from '../../utils/fileFilter'

export interface IndexingProgress {
  totalFiles: number
  processedFiles: number
  currentFile: string
  startTime: number
  estimatedTimeRemaining: number
  phase: 'discovery' | 'indexing' | 'relationships' | 'optimization'
}

export interface IndexingResult {
  success: boolean
  filesProcessed: number
  filesSkipped: number
  filesErrored: number
  duration: number
  errors: string[]
}

export interface IndexingOptions {
  projectRoot: string
  batchSize?: number
  maxConcurrency?: number
  skipUnchanged?: boolean
  includeContent?: boolean
  watchForChanges?: boolean
  fileFilter?: FileFilter
}

export class IncrementalIndexer extends EventEmitter {
  private db: ConnectionManager
  private fileSystem: FileSystemService
  private metadataExtractor: MetadataExtractor
  private fileWatcher?: FileWatcherService
  private isIndexing = false
  private indexingQueue: string[] = []
  private processingSet = new Set<string>()

  constructor(
    db: ConnectionManager,
    fileSystem: FileSystemService,
    metadataExtractor: MetadataExtractor
  ) {
    super()
    this.db = db
    this.fileSystem = fileSystem
    this.metadataExtractor = metadataExtractor
  }

  async startIndexing(options: IndexingOptions): Promise<IndexingResult> {
    if (this.isIndexing) {
      throw new Error('Indexing already in progress')
    }

    this.isIndexing = true
    const startTime = Date.now()
    let filesProcessed = 0
    let filesSkipped = 0
    let filesErrored = 0
    const errors: string[] = []

    try {
      this.emit('progress', {
        totalFiles: 0,
        processedFiles: 0,
        currentFile: '',
        startTime,
        estimatedTimeRemaining: 0,
        phase: 'discovery'
      } as IndexingProgress)

      const projectFiles = await this.discoverFiles(options)
      const filesToProcess = options.skipUnchanged ? 
        await this.filterUnchangedFiles(projectFiles, options.projectRoot) : 
        projectFiles

      this.emit('progress', {
        totalFiles: filesToProcess.length,
        processedFiles: 0,
        currentFile: '',
        startTime,
        estimatedTimeRemaining: 0,
        phase: 'indexing'
      } as IndexingProgress)

      const batchSize = options.batchSize || 50
      const maxConcurrency = options.maxConcurrency || 5

      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize)
        const batchPromises = batch.map(async (filePath) => {
          try {
            await this.processFile(filePath, options)
            filesProcessed++
            
            const progress: IndexingProgress = {
              totalFiles: filesToProcess.length,
              processedFiles: filesProcessed + filesSkipped,
              currentFile: filePath,
              startTime,
              estimatedTimeRemaining: this.calculateEstimatedTime(
                startTime, filesProcessed + filesSkipped, filesToProcess.length
              ),
              phase: 'indexing'
            }
            this.emit('progress', progress)
          } catch (error) {
            filesErrored++
            errors.push(`Error processing ${filePath}: ${error}`)
            this.emit('error', error)
          }
        })

        await this.limitConcurrency(batchPromises, maxConcurrency)
      }

      this.emit('progress', {
        totalFiles: filesToProcess.length,
        processedFiles: filesProcessed + filesSkipped,
        currentFile: '',
        startTime,
        estimatedTimeRemaining: 0,
        phase: 'relationships'
      } as IndexingProgress)

      await this.buildRelationships(options.projectRoot)

      this.emit('progress', {
        totalFiles: filesToProcess.length,
        processedFiles: filesProcessed + filesSkipped,
        currentFile: '',
        startTime,
        estimatedTimeRemaining: 0,
        phase: 'optimization'
      } as IndexingProgress)

      await this.optimizeDatabase()

      if (options.watchForChanges) {
        await this.setupFileWatcher(options)
      }

      const duration = Date.now() - startTime
      const result: IndexingResult = {
        success: true,
        filesProcessed,
        filesSkipped,
        filesErrored,
        duration,
        errors
      }

      this.emit('complete', result)
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const result: IndexingResult = {
        success: false,
        filesProcessed,
        filesSkipped,
        filesErrored,
        duration,
        errors: [...errors, `Indexing failed: ${error}`]
      }

      this.emit('complete', result)
      return result
    } finally {
      this.isIndexing = false
    }
  }

  async stopIndexing(): Promise<void> {
    this.isIndexing = false
    if (this.fileWatcher) {
      await this.fileWatcher.stopAll()
    }
  }

  private async discoverFiles(options: IndexingOptions): Promise<string[]> {
    const files: string[] = []
    await this.scanDirectory(options.projectRoot, options.projectRoot, files, options.fileFilter)
    return files
  }

  private async scanDirectory(
    dirPath: string,
    projectRoot: string,
    files: string[],
    fileFilter?: FileFilter
  ): Promise<void> {
    try {
      const items = await this.fileSystem.readdir(dirPath)
      
      for (const item of items) {
        const itemPath = this.fileSystem.joinPath(dirPath, item)
        const relativePath = this.fileSystem.relative(projectRoot, itemPath)

        if (fileFilter && !fileFilter.shouldInclude(relativePath)) {
          continue
        }

        const isDirectory = await this.fileSystem.isDirectory(itemPath)
        
        if (isDirectory) {
          await this.scanDirectory(itemPath, projectRoot, files, fileFilter)
        } else {
          const shouldInclude = !fileFilter || await fileFilter.shouldIncludeFile(itemPath)
          if (shouldInclude) {
            files.push(itemPath)
          }
        }
      }
    } catch (error) {
      this.emit('error', `Error scanning directory ${dirPath}: ${error}`)
    }
  }

  private async filterUnchangedFiles(files: string[], projectRoot: string): Promise<string[]> {
    const filesToProcess: string[] = []
    
    for (const filePath of files) {
      try {
        const relativePath = this.fileSystem.relative(projectRoot, filePath)
        const stats = await this.fileSystem.stat(filePath)
        
        const existingFile = await this.db.findOne(
          this.db.query()
            .select('modified_time, content_hash')
            .from('files')
            .where('path', '=', filePath)
        )

        if (!existingFile || 
            existingFile.modified_time !== stats.mtime.getTime() ||
            !existingFile.content_hash) {
          filesToProcess.push(filePath)
        }
      } catch (error) {
        filesToProcess.push(filePath)
      }
    }

    return filesToProcess
  }

  private async processFile(filePath: string, options: IndexingOptions): Promise<void> {
    if (this.processingSet.has(filePath)) {
      return
    }

    this.processingSet.add(filePath)

    try {
      const metadata = await this.metadataExtractor.extractFileMetadata(filePath, options.projectRoot)
      await this.saveFileMetadata(metadata)

      if (options.includeContent && metadata.isText) {
        await this.processFileContent(filePath, metadata)
      }

      await this.processImportExports(filePath, metadata)
    } finally {
      this.processingSet.delete(filePath)
    }
  }

  private async saveFileMetadata(metadata: FileMetadata): Promise<void> {
    await this.db.upsert('files', {
      path: metadata.path,
      relative_path: metadata.relativePath,
      name: metadata.name,
      extension: metadata.extension,
      size: metadata.size,
      modified_time: metadata.modifiedTime.getTime(),
      created_time: metadata.createdTime.getTime(),
      language: metadata.language,
      is_text: metadata.isText ? 1 : 0,
      content_hash: metadata.contentHash,
      last_indexed: Date.now()
    }, ['path'])
  }

  private async processFileContent(filePath: string, metadata: FileMetadata): Promise<void> {
    try {
      const content = await this.fileSystem.readFile(filePath)
      const contentMetadata = this.metadataExtractor.extractContentMetadata(content)
      
      const fileResult = await this.db.findOne(
        this.db.query()
          .select('id')
          .from('files')
          .where('path', '=', filePath)
      )

      if (fileResult) {
        await this.db.execute(
          this.db.delete()
            .from('file_contents')
            .where('file_id', '=', fileResult.id)
        )

        await this.db.execute(
          this.db.insert()
            .into('file_contents')
            .columns(['file_id', 'content', 'line_count', 'char_count'])
            .values([fileResult.id, content, contentMetadata.lineCount, contentMetadata.characterCount])
        )
      }
    } catch (error) {
      this.emit('error', `Error processing content for ${filePath}: ${error}`)
    }
  }

  private async processImportExports(filePath: string, metadata: FileMetadata): Promise<void> {
    if (!metadata.isText) {
      return
    }

    try {
      const content = await this.fileSystem.readFile(filePath)
      const importExportMetadata = await this.metadataExtractor.extractImportExportMetadata(filePath, content)
      
      const fileResult = await this.db.findOne(
        this.db.query()
          .select('id')
          .from('files')
          .where('path', '=', filePath)
      )

      if (fileResult) {
        await this.db.execute(
          this.db.delete()
            .from('dependencies')
            .where('file_id', '=', fileResult.id)
        )

        for (const importInfo of importExportMetadata.imports) {
          await this.db.execute(
            this.db.insert()
              .into('dependencies')
              .columns(['file_id', 'dependency_path', 'dependency_type', 'import_name', 'is_local', 'line_number'])
              .values([
                fileResult.id,
                importInfo.module,
                importInfo.importType,
                importInfo.importedNames.join(', '),
                importInfo.isLocal ? 1 : 0,
                importInfo.lineNumber
              ])
          )
        }
      }
    } catch (error) {
      this.emit('error', `Error processing imports/exports for ${filePath}: ${error}`)
    }
  }

  private async buildRelationships(projectRoot: string): Promise<void> {
    // Build file relationship graph based on imports/exports
    const dependencies = await this.db.findMany(
      this.db.query()
        .select('d.*, f.path as file_path')
        .from('dependencies d')
        .innerJoin('files f', 'f.id = d.file_id')
        .where('d.is_local', '=', 1)
    )

    // Process relationships and update database
    for (const dependency of dependencies) {
      const resolvedPath = this.resolveImportPath(dependency.file_path, dependency.dependency_path, projectRoot)
      if (resolvedPath) {
        // Update dependency with resolved path
        await this.db.execute(
          this.db.update()
            .table('dependencies')
            .set('dependency_path', resolvedPath)
            .where('id', '=', dependency.id)
        )
      }
    }
  }

  private resolveImportPath(filePath: string, importPath: string, projectRoot: string): string | null {
    try {
      const fileDir = this.fileSystem.dirname(filePath)
      const resolvedPath = this.fileSystem.resolve(fileDir, importPath)
      
      // Try different extensions
      const extensions = ['.js', '.jsx', '.ts', '.tsx', '.json']
      for (const ext of extensions) {
        const pathWithExt = resolvedPath + ext
        if (this.fileSystem.exists(pathWithExt)) {
          return this.fileSystem.relative(projectRoot, pathWithExt)
        }
      }

      // Try index files
      for (const ext of extensions) {
        const indexPath = this.fileSystem.joinPath(resolvedPath, `index${ext}`)
        if (this.fileSystem.exists(indexPath)) {
          return this.fileSystem.relative(projectRoot, indexPath)
        }
      }

      return null
    } catch {
      return null
    }
  }

  private async optimizeDatabase(): Promise<void> {
    await this.db.analyze()
    await this.db.vacuum()
  }

  private async setupFileWatcher(options: IndexingOptions): Promise<void> {
    this.fileWatcher = new FileWatcherService({
      ignored: options.fileFilter?.getPatterns().exclude || [],
      ignoreInitial: true,
      followSymlinks: false,
      awaitWriteFinish: {
        stabilityThreshold: 1000,
        pollInterval: 100
      }
    })

    this.fileWatcher.on('fileEvent', (event: FileWatcherEvent) => {
      this.handleFileChange(event, options)
    })

    await this.fileWatcher.watch(options.projectRoot)
  }

  private async handleFileChange(event: FileWatcherEvent, options: IndexingOptions): Promise<void> {
    switch (event.type) {
      case 'add':
      case 'change':
        this.indexingQueue.push(event.path)
        this.processQueuedFiles(options)
        break
      case 'unlink':
        await this.removeFileFromIndex(event.path)
        break
    }
  }

  private async processQueuedFiles(options: IndexingOptions): Promise<void> {
    if (this.indexingQueue.length === 0) {
      return
    }

    const filesToProcess = [...this.indexingQueue]
    this.indexingQueue = []

    for (const filePath of filesToProcess) {
      try {
        await this.processFile(filePath, options)
      } catch (error) {
        this.emit('error', `Error processing queued file ${filePath}: ${error}`)
      }
    }
  }

  private async removeFileFromIndex(filePath: string): Promise<void> {
    await this.db.execute(
      this.db.delete()
        .from('files')
        .where('path', '=', filePath)
    )
  }

  private calculateEstimatedTime(startTime: number, processed: number, total: number): number {
    if (processed === 0) return 0
    const elapsed = Date.now() - startTime
    const rate = processed / elapsed
    const remaining = total - processed
    return remaining / rate
  }

  private async limitConcurrency<T>(promises: Promise<T>[], maxConcurrency: number): Promise<T[]> {
    const results: T[] = []
    const executing: Promise<any>[] = []

    for (const promise of promises) {
      const p = promise.then((result) => {
        executing.splice(executing.indexOf(p), 1)
        return result
      })
      results.push(p)
      executing.push(p)

      if (executing.length >= maxConcurrency) {
        await Promise.race(executing)
      }
    }

    return Promise.all(results)
  }

  isIndexing(): boolean {
    return this.isIndexing
  }

  getQueueSize(): number {
    return this.indexingQueue.length
  }
}