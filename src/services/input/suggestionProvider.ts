import * as vscode from 'vscode'
import { In<PERSON><PERSON><PERSON><PERSON>, InputEvent, InputPattern } from './inputHandler'
import { Selection<PERSON>nalyzer, SelectionAnalysis } from './selectionAnalyzer'
import { PreferenceTracker } from '../memory/preferenceTracker'
import { <PERSON><PERSON>R<PERSON>ognizer } from '../memory/patternRecognizer'
import { LearningEngine } from '../memory/learningEngine'
import { ContextAnalyzer } from '../context/contextAnalyzer'
import { SemanticSearch } from '../embeddings/semanticSearch'

export interface Suggestion {
  id: string
  type: SuggestionType
  content: SuggestionContent
  trigger: SuggestionTrigger
  presentation: SuggestionPresentation
  actions: SuggestionAction[]
  metadata: SuggestionMetadata
  timestamp: Date
}

export interface SuggestionType {
  category: 'code_completion' | 'refactoring' | 'quick_fix' | 'context_action' | 'snippet' | 'template' | 'navigation' | 'optimization'
  priority: 'low' | 'medium' | 'high' | 'critical'
  scope: 'character' | 'word' | 'line' | 'selection' | 'function' | 'file' | 'project'
  urgency: 'immediate' | 'contextual' | 'background' | 'on_demand'
}

export interface SuggestionContent {
  title: string
  description: string
  preview?: string
  code?: string
  replacementRange?: vscode.Range
  insertText?: string
  additionalEdits?: vscode.WorkspaceEdit[]
  explanation?: string
  benefits?: string[]
  risks?: string[]
}

export interface SuggestionTrigger {
  event: string
  conditions: TriggerCondition[]
  context: TriggerContext
  confidence: number
  probability: number
}

export interface TriggerCondition {
  type: 'cursor_position' | 'text_content' | 'file_type' | 'selection_state' | 'recent_actions' | 'time_based'
  operator: 'equals' | 'contains' | 'matches' | 'within' | 'after' | 'before'
  value: any
  weight: number
}

export interface TriggerContext {
  document: vscode.TextDocument
  position: vscode.Position
  selection: vscode.Selection
  nearbyCode: string
  language: string
  recentEvents: InputEvent[]
  userIntention: string
}

export interface SuggestionPresentation {
  display: 'inline' | 'hover' | 'lightbulb' | 'completion' | 'diagnostic' | 'codelens' | 'statusbar'
  position: vscode.Position
  style: PresentationStyle
  duration: number
  dismissible: boolean
  shortcut?: string
}

export interface PresentationStyle {
  color?: string
  backgroundColor?: string
  icon?: string
  emphasis?: 'none' | 'bold' | 'italic' | 'underline' | 'highlight'
  animation?: 'none' | 'fade' | 'slide' | 'bounce' | 'pulse'
}

export interface SuggestionAction {
  id: string
  title: string
  description: string
  type: 'apply' | 'dismiss' | 'preview' | 'learn_more' | 'customize' | 'feedback'
  command: string
  arguments: any[]
  shortcut?: string
  destructive?: boolean
}

export interface SuggestionMetadata {
  source: string
  confidence: number
  relevance: number
  learningFactor: number
  performanceImpact: number
  userPreferenceScore: number
  contextualScore: number
  originalEvent?: InputEvent
  relatedSuggestions?: string[]
}

export interface SuggestionRule {
  id: string
  name: string
  description: string
  conditions: SuggestionCondition[]
  generator: SuggestionGenerator
  priority: number
  enabled: boolean
  success_rate: number
  usage_count: number
}

export interface SuggestionCondition {
  field: string
  operator: 'equals' | 'contains' | 'matches' | 'greater_than' | 'less_than' | 'in' | 'not_in'
  value: any
  weight: number
}

export interface SuggestionGenerator {
  type: 'static' | 'dynamic' | 'ml_based' | 'template' | 'pattern_match'
  source: string
  parameters: Record<string, any>
  cacheKey?: string
}

export interface SuggestionFeedback {
  suggestionId: string
  action: 'accepted' | 'rejected' | 'dismissed' | 'modified' | 'delayed'
  reason?: string
  improvement?: string
  timestamp: Date
  context: any
}

export interface SuggestionStats {
  totalSuggestions: number
  acceptedSuggestions: number
  rejectedSuggestions: number
  dismissedSuggestions: number
  acceptanceRate: number
  avgResponseTime: number
  userSatisfaction: number
  topSuggestionTypes: string[]
}

export interface SuggestionStrategy {
  name: string
  description: string
  targetScenarios: string[]
  adaptationRules: AdaptationRule[]
  enabled: boolean
  priority: number
}

export interface AdaptationRule {
  condition: string
  adjustment: string
  impact: number
}

export class SuggestionProvider {
  private inputHandler: InputHandler
  private selectionAnalyzer: SelectionAnalyzer
  private preferenceTracker: PreferenceTracker
  private patternRecognizer: PatternRecognizer
  private learningEngine: LearningEngine
  private contextAnalyzer: ContextAnalyzer
  private semanticSearch: SemanticSearch
  
  private suggestions: Map<string, Suggestion>
  private activeSuggestions: Map<string, Suggestion>
  private suggestionRules: SuggestionRule[]
  private suggestionHistory: Suggestion[]
  private feedback: SuggestionFeedback[]
  private stats: SuggestionStats
  private strategies: SuggestionStrategy[]
  
  private decorationTypes: Map<string, vscode.TextEditorDecorationType>
  private disposables: vscode.Disposable[]
  private currentContext: TriggerContext | null
  private suggestionCache: Map<string, Suggestion[]>
  private isEnabled: boolean

  constructor(
    inputHandler: InputHandler,
    selectionAnalyzer: SelectionAnalyzer,
    preferenceTracker: PreferenceTracker,
    patternRecognizer: PatternRecognizer,
    learningEngine: LearningEngine,
    contextAnalyzer: ContextAnalyzer,
    semanticSearch: SemanticSearch
  ) {
    this.inputHandler = inputHandler
    this.selectionAnalyzer = selectionAnalyzer
    this.preferenceTracker = preferenceTracker
    this.patternRecognizer = patternRecognizer
    this.learningEngine = learningEngine
    this.contextAnalyzer = contextAnalyzer
    this.semanticSearch = semanticSearch
    
    this.suggestions = new Map()
    this.activeSuggestions = new Map()
    this.suggestionRules = []
    this.suggestionHistory = []
    this.feedback = []
    this.stats = this.initializeStats()
    this.strategies = []
    
    this.decorationTypes = new Map()
    this.disposables = []
    this.currentContext = null
    this.suggestionCache = new Map()
    this.isEnabled = true
    
    this.initializeSuggestionRules()
    this.initializeSuggestionStrategies()
    this.registerEventListeners()
  }

  private initializeStats(): SuggestionStats {
    return {
      totalSuggestions: 0,
      acceptedSuggestions: 0,
      rejectedSuggestions: 0,
      dismissedSuggestions: 0,
      acceptanceRate: 0,
      avgResponseTime: 0,
      userSatisfaction: 0,
      topSuggestionTypes: []
    }
  }

  private initializeSuggestionRules(): void {
    this.suggestionRules = [
      {
        id: 'auto_import',
        name: 'Auto Import Suggestions',
        description: 'Suggest imports for undefined symbols',
        conditions: [
          {
            field: 'error_type',
            operator: 'equals',
            value: 'undefined_symbol',
            weight: 1.0
          },
          {
            field: 'language',
            operator: 'in',
            value: ['typescript', 'javascript', 'python'],
            weight: 0.8
          }
        ],
        generator: {
          type: 'dynamic',
          source: 'symbol_resolver',
          parameters: { includeNodeModules: true }
        },
        priority: 1,
        enabled: true,
        success_rate: 0.85,
        usage_count: 0
      },
      {
        id: 'refactor_extract_function',
        name: 'Extract Function Refactoring',
        description: 'Suggest extracting selected code into a function',
        conditions: [
          {
            field: 'selection_length',
            operator: 'greater_than',
            value: 50,
            weight: 0.8
          },
          {
            field: 'selection_complexity',
            operator: 'greater_than',
            value: 3,
            weight: 0.9
          },
          {
            field: 'repeated_pattern',
            operator: 'equals',
            value: true,
            weight: 0.7
          }
        ],
        generator: {
          type: 'template',
          source: 'refactoring_templates',
          parameters: { type: 'extract_function' }
        },
        priority: 2,
        enabled: true,
        success_rate: 0.72,
        usage_count: 0
      },
      {
        id: 'code_completion_smart',
        name: 'Smart Code Completion',
        description: 'Context-aware code completion suggestions',
        conditions: [
          {
            field: 'cursor_context',
            operator: 'matches',
            value: 'incomplete_statement',
            weight: 1.0
          },
          {
            field: 'typing_pattern',
            operator: 'equals',
            value: 'active',
            weight: 0.6
          }
        ],
        generator: {
          type: 'ml_based',
          source: 'completion_model',
          parameters: { context_window: 100 }
        },
        priority: 3,
        enabled: true,
        success_rate: 0.68,
        usage_count: 0
      },
      {
        id: 'performance_optimization',
        name: 'Performance Optimization',
        description: 'Suggest performance improvements',
        conditions: [
          {
            field: 'pattern_type',
            operator: 'in',
            value: ['inefficient_loop', 'memory_leak', 'blocking_operation'],
            weight: 0.9
          },
          {
            field: 'complexity_score',
            operator: 'greater_than',
            value: 8,
            weight: 0.8
          }
        ],
        generator: {
          type: 'pattern_match',
          source: 'optimization_patterns',
          parameters: { includeAsyncSuggestions: true }
        },
        priority: 1,
        enabled: true,
        success_rate: 0.79,
        usage_count: 0
      },
      {
        id: 'documentation_generation',
        name: 'Documentation Generation',
        description: 'Generate documentation for functions and classes',
        conditions: [
          {
            field: 'element_type',
            operator: 'in',
            value: ['function', 'class', 'method'],
            weight: 1.0
          },
          {
            field: 'has_documentation',
            operator: 'equals',
            value: false,
            weight: 0.9
          }
        ],
        generator: {
          type: 'template',
          source: 'documentation_templates',
          parameters: { includeExamples: true }
        },
        priority: 2,
        enabled: true,
        success_rate: 0.76,
        usage_count: 0
      }
    ]
  }

  private initializeSuggestionStrategies(): void {
    this.strategies = [
      {
        name: 'Adaptive Timing',
        description: 'Adjust suggestion timing based on user behavior',
        targetScenarios: ['typing', 'navigation', 'debugging'],
        adaptationRules: [
          {
            condition: 'user_typing_speed > 60_wpm',
            adjustment: 'delay_suggestions_by_200ms',
            impact: 0.3
          },
          {
            condition: 'frequent_dismissals',
            adjustment: 'reduce_suggestion_frequency',
            impact: 0.5
          }
        ],
        enabled: true,
        priority: 1
      },
      {
        name: 'Context-Aware Filtering',
        description: 'Filter suggestions based on current context',
        targetScenarios: ['code_editing', 'debugging', 'reviewing'],
        adaptationRules: [
          {
            condition: 'in_debug_mode',
            adjustment: 'prioritize_debug_suggestions',
            impact: 0.8
          },
          {
            condition: 'reviewing_code',
            adjustment: 'show_quality_suggestions',
            impact: 0.7
          }
        ],
        enabled: true,
        priority: 2
      },
      {
        name: 'Learning-Based Refinement',
        description: 'Refine suggestions based on user feedback',
        targetScenarios: ['all'],
        adaptationRules: [
          {
            condition: 'low_acceptance_rate',
            adjustment: 'retrain_suggestion_model',
            impact: 0.9
          },
          {
            condition: 'user_preference_change',
            adjustment: 'update_preference_weights',
            impact: 0.6
          }
        ],
        enabled: true,
        priority: 3
      }
    ]
  }

  private registerEventListeners(): void {
    // Listen to input events
    this.disposables.push(
      this.inputHandler.onInputEvent(event => {
        this.handleInputEvent(event)
      })
    )

    // Listen to text document changes
    this.disposables.push(
      vscode.workspace.onDidChangeTextDocument(event => {
        this.handleTextDocumentChange(event)
      })
    )

    // Listen to selection changes
    this.disposables.push(
      vscode.window.onDidChangeTextEditorSelection(event => {
        this.handleSelectionChange(event)
      })
    )

    // Listen to cursor position changes
    this.disposables.push(
      vscode.window.onDidChangeTextEditorCursorPosition(event => {
        this.handleCursorPositionChange(event)
      })
    )

    // Listen to active editor changes
    this.disposables.push(
      vscode.window.onDidChangeActiveTextEditor(editor => {
        this.handleActiveEditorChange(editor)
      })
    )
  }

  private async handleInputEvent(event: InputEvent): Promise<void> {
    if (!this.isEnabled) return

    try {
      // Update current context
      this.currentContext = await this.buildTriggerContext(event)
      
      // Generate suggestions based on the input event
      const suggestions = await this.generateSuggestions(event, this.currentContext)
      
      // Filter and rank suggestions
      const filteredSuggestions = await this.filterAndRankSuggestions(suggestions)
      
      // Present suggestions
      await this.presentSuggestions(filteredSuggestions)
      
    } catch (error) {
      console.error('Error handling input event for suggestions:', error)
    }
  }

  private async handleTextDocumentChange(event: vscode.TextDocumentChangeEvent): Promise<void> {
    // Clear cache for changed document
    const documentKey = event.document.uri.toString()
    this.suggestionCache.delete(documentKey)
    
    // Check for suggestion triggers
    await this.checkTriggersForDocumentChange(event)
  }

  private async handleSelectionChange(event: vscode.TextEditorSelectionChangeEvent): Promise<void> {
    // Analyze selection and generate context-aware suggestions
    const analysis = await this.selectionAnalyzer.analyzeSelection(event.selections[0])
    await this.generateSelectionBasedSuggestions(analysis, event.textEditor)
  }

  private async handleCursorPositionChange(event: vscode.TextEditorCursorPositionChangeEvent): Promise<void> {
    // Generate position-based suggestions
    await this.generatePositionBasedSuggestions(event.textEditor, event.selections[0].active)
  }

  private async handleActiveEditorChange(editor: vscode.TextEditor | undefined): Promise<void> {
    // Clear active suggestions when editor changes
    this.clearActiveSuggestions()
    
    if (editor) {
      // Generate editor-specific suggestions
      await this.generateEditorSuggestions(editor)
    }
  }

  private async buildTriggerContext(event: InputEvent): Promise<TriggerContext> {
    const editor = vscode.window.activeTextEditor
    if (!editor) {
      throw new Error('No active editor')
    }

    const document = editor.document
    const position = editor.selection.active
    const selection = editor.selection
    const nearbyCode = this.getNearbyCode(document, position, 100)
    const recentEvents = this.inputHandler.getEventStream().slice(-5)
    const userIntention = await this.inferUserIntention(event, recentEvents)

    return {
      document,
      position,
      selection,
      nearbyCode,
      language: document.languageId,
      recentEvents,
      userIntention
    }
  }

  private async generateSuggestions(event: InputEvent, context: TriggerContext): Promise<Suggestion[]> {
    const suggestions: Suggestion[] = []
    const cacheKey = this.generateCacheKey(event, context)
    
    // Check cache first
    if (this.suggestionCache.has(cacheKey)) {
      return this.suggestionCache.get(cacheKey)!
    }

    // Generate suggestions using different strategies
    const ruleBased = await this.generateRuleBasedSuggestions(context)
    const mlBased = await this.generateMLBasedSuggestions(context)
    const patternBased = await this.generatePatternBasedSuggestions(context)
    const contextBased = await this.generateContextBasedSuggestions(context)

    suggestions.push(...ruleBased, ...mlBased, ...patternBased, ...contextBased)

    // Cache the results
    this.suggestionCache.set(cacheKey, suggestions)
    
    return suggestions
  }

  private async generateRuleBasedSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    const suggestions: Suggestion[] = []
    
    for (const rule of this.suggestionRules) {
      if (!rule.enabled) continue
      
      const matches = await this.evaluateRule(rule, context)
      if (matches) {
        const suggestion = await this.generateSuggestionFromRule(rule, context)
        if (suggestion) {
          suggestions.push(suggestion)
        }
      }
    }
    
    return suggestions
  }

  private async generateMLBasedSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    const suggestions: Suggestion[] = []
    
    try {
      // Use learning engine to predict suggestions
      const prediction = await this.learningEngine.predict('code_completion', {
        context: context.nearbyCode,
        language: context.language,
        position: context.position,
        recentEvents: context.recentEvents
      })
      
      if (prediction.confidence > 0.6) {
        const suggestion = this.createSuggestionFromPrediction(prediction, context)
        suggestions.push(suggestion)
      }
    } catch (error) {
      console.error('Error generating ML-based suggestions:', error)
    }
    
    return suggestions
  }

  private async generatePatternBasedSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    const suggestions: Suggestion[] = []
    
    try {
      // Use pattern recognizer to find applicable patterns
      const patterns = await this.patternRecognizer.analyzeCode(context.document.uri.fsPath)
      
      for (const pattern of patterns) {
        if (pattern.confidence > 0.7) {
          const suggestion = this.createPatternSuggestion(pattern, context)
          suggestions.push(suggestion)
        }
      }
    } catch (error) {
      console.error('Error generating pattern-based suggestions:', error)
    }
    
    return suggestions
  }

  private async generateContextBasedSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    const suggestions: Suggestion[] = []
    
    try {
      // Use context analyzer to understand current context
      const analysis = await this.contextAnalyzer.analyzeContext(context.document, context.position)
      
      // Generate context-specific suggestions
      if (analysis.intent === 'debugging') {
        suggestions.push(...await this.generateDebuggingSuggestions(context))
      } else if (analysis.intent === 'refactoring') {
        suggestions.push(...await this.generateRefactoringSuggestions(context))
      } else if (analysis.intent === 'testing') {
        suggestions.push(...await this.generateTestingSuggestions(context))
      }
    } catch (error) {
      console.error('Error generating context-based suggestions:', error)
    }
    
    return suggestions
  }

  private async generateSelectionBasedSuggestions(analysis: SelectionAnalysis, editor: vscode.TextEditor): Promise<void> {
    const suggestions: Suggestion[] = []
    
    // Generate suggestions based on selection analysis
    if (analysis.opportunities.refactoringOpportunities.length > 0) {
      for (const opportunity of analysis.opportunities.refactoringOpportunities) {
        const suggestion = this.createRefactoringSuggestion(opportunity, analysis, editor)
        suggestions.push(suggestion)
      }
    }
    
    if (analysis.opportunities.optimizationOpportunities.length > 0) {
      for (const opportunity of analysis.opportunities.optimizationOpportunities) {
        const suggestion = this.createOptimizationSuggestion(opportunity, analysis, editor)
        suggestions.push(suggestion)
      }
    }
    
    // Present suggestions
    await this.presentSuggestions(suggestions)
  }

  private async generatePositionBasedSuggestions(editor: vscode.TextEditor, position: vscode.Position): Promise<void> {
    const suggestions: Suggestion[] = []
    const document = editor.document
    const line = document.lineAt(position.line)
    
    // Check for common completion scenarios
    if (line.text.endsWith('.')) {
      // Member access completion
      const memberSuggestions = await this.generateMemberAccessSuggestions(document, position)
      suggestions.push(...memberSuggestions)
    }
    
    if (line.text.includes('import ')) {
      // Import suggestions
      const importSuggestions = await this.generateImportSuggestions(document, position)
      suggestions.push(...importSuggestions)
    }
    
    // Present suggestions
    await this.presentSuggestions(suggestions)
  }

  private async generateEditorSuggestions(editor: vscode.TextEditor): Promise<void> {
    const suggestions: Suggestion[] = []
    const document = editor.document
    
    // Generate file-level suggestions
    const fileSuggestions = await this.generateFileLevelSuggestions(document)
    suggestions.push(...fileSuggestions)
    
    // Present suggestions
    await this.presentSuggestions(suggestions)
  }

  private async filterAndRankSuggestions(suggestions: Suggestion[]): Promise<Suggestion[]> {
    // Filter suggestions based on user preferences
    const userPreferences = this.preferenceTracker.getProfile()
    const filtered = suggestions.filter(s => this.matchesUserPreferences(s, userPreferences))
    
    // Rank suggestions by relevance, confidence, and user preferences
    return filtered.sort((a, b) => {
      const scoreA = this.calculateSuggestionScore(a)
      const scoreB = this.calculateSuggestionScore(b)
      return scoreB - scoreA
    }).slice(0, 5) // Limit to top 5 suggestions
  }

  private async presentSuggestions(suggestions: Suggestion[]): Promise<void> {
    for (const suggestion of suggestions) {
      await this.presentSuggestion(suggestion)
    }
  }

  private async presentSuggestion(suggestion: Suggestion): Promise<void> {
    this.activeSuggestions.set(suggestion.id, suggestion)
    this.stats.totalSuggestions++
    
    switch (suggestion.presentation.display) {
      case 'inline':
        await this.presentInlineSuggestion(suggestion)
        break
      case 'hover':
        await this.presentHoverSuggestion(suggestion)
        break
      case 'lightbulb':
        await this.presentLightbulbSuggestion(suggestion)
        break
      case 'completion':
        await this.presentCompletionSuggestion(suggestion)
        break
      case 'diagnostic':
        await this.presentDiagnosticSuggestion(suggestion)
        break
      case 'codelens':
        await this.presentCodeLensSuggestion(suggestion)
        break
      case 'statusbar':
        await this.presentStatusBarSuggestion(suggestion)
        break
    }
  }

  private async presentInlineSuggestion(suggestion: Suggestion): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) return

    const decorationType = this.getOrCreateDecorationType(suggestion)
    const range = suggestion.content.replacementRange || new vscode.Range(
      suggestion.presentation.position,
      suggestion.presentation.position
    )

    const decoration: vscode.DecorationOptions = {
      range,
      renderOptions: {
        after: {
          contentText: suggestion.content.preview || suggestion.content.title,
          color: suggestion.presentation.style.color || '#888888',
          fontStyle: 'italic'
        }
      }
    }

    editor.setDecorations(decorationType, [decoration])
  }

  private async presentHoverSuggestion(suggestion: Suggestion): Promise<void> {
    // Would register hover provider for this suggestion
    console.log('Presenting hover suggestion:', suggestion.id)
  }

  private async presentLightbulbSuggestion(suggestion: Suggestion): Promise<void> {
    // Would register code action provider for this suggestion
    console.log('Presenting lightbulb suggestion:', suggestion.id)
  }

  private async presentCompletionSuggestion(suggestion: Suggestion): Promise<void> {
    // Would register completion provider for this suggestion
    console.log('Presenting completion suggestion:', suggestion.id)
  }

  private async presentDiagnosticSuggestion(suggestion: Suggestion): Promise<void> {
    // Would create diagnostic for this suggestion
    console.log('Presenting diagnostic suggestion:', suggestion.id)
  }

  private async presentCodeLensSuggestion(suggestion: Suggestion): Promise<void> {
    // Would register code lens provider for this suggestion
    console.log('Presenting code lens suggestion:', suggestion.id)
  }

  private async presentStatusBarSuggestion(suggestion: Suggestion): Promise<void> {
    // Would show in status bar
    console.log('Presenting status bar suggestion:', suggestion.id)
  }

  async acceptSuggestion(suggestionId: string): Promise<void> {
    const suggestion = this.activeSuggestions.get(suggestionId)
    if (!suggestion) return

    try {
      // Apply the suggestion
      await this.applySuggestion(suggestion)
      
      // Record feedback
      await this.recordFeedback(suggestionId, 'accepted')
      
      // Update statistics
      this.stats.acceptedSuggestions++
      this.updateAcceptanceRate()
      
      // Remove from active suggestions
      this.activeSuggestions.delete(suggestionId)
      
    } catch (error) {
      console.error('Error accepting suggestion:', error)
    }
  }

  async rejectSuggestion(suggestionId: string, reason?: string): Promise<void> {
    const suggestion = this.activeSuggestions.get(suggestionId)
    if (!suggestion) return

    // Record feedback
    await this.recordFeedback(suggestionId, 'rejected', reason)
    
    // Update statistics
    this.stats.rejectedSuggestions++
    this.updateAcceptanceRate()
    
    // Remove from active suggestions
    this.activeSuggestions.delete(suggestionId)
  }

  async dismissSuggestion(suggestionId: string): Promise<void> {
    const suggestion = this.activeSuggestions.get(suggestionId)
    if (!suggestion) return

    // Record feedback
    await this.recordFeedback(suggestionId, 'dismissed')
    
    // Update statistics
    this.stats.dismissedSuggestions++
    this.updateAcceptanceRate()
    
    // Remove from active suggestions
    this.activeSuggestions.delete(suggestionId)
  }

  private async applySuggestion(suggestion: Suggestion): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) return

    const edit = new vscode.WorkspaceEdit()
    
    if (suggestion.content.code && suggestion.content.replacementRange) {
      edit.replace(editor.document.uri, suggestion.content.replacementRange, suggestion.content.code)
    } else if (suggestion.content.insertText) {
      edit.insert(editor.document.uri, suggestion.presentation.position, suggestion.content.insertText)
    }
    
    if (suggestion.content.additionalEdits) {
      // Apply additional edits
      for (const additionalEdit of suggestion.content.additionalEdits) {
        additionalEdit.entries().forEach(([uri, edits]) => {
          edits.forEach(textEdit => {
            edit.replace(uri, textEdit.range, textEdit.newText)
          })
        })
      }
    }
    
    await vscode.workspace.applyEdit(edit)
  }

  private async recordFeedback(suggestionId: string, action: SuggestionFeedback['action'], reason?: string): Promise<void> {
    const feedback: SuggestionFeedback = {
      suggestionId,
      action,
      reason,
      timestamp: new Date(),
      context: this.currentContext
    }
    
    this.feedback.push(feedback)
    
    // Use feedback for learning
    await this.learningEngine.predict('preference_learning', {
      suggestionId,
      action,
      context: this.currentContext
    })
  }

  private updateAcceptanceRate(): void {
    const total = this.stats.acceptedSuggestions + this.stats.rejectedSuggestions + this.stats.dismissedSuggestions
    this.stats.acceptanceRate = total > 0 ? this.stats.acceptedSuggestions / total : 0
  }

  private clearActiveSuggestions(): void {
    // Clear all active suggestion decorations
    for (const [type, decorationType] of this.decorationTypes) {
      const editor = vscode.window.activeTextEditor
      if (editor) {
        editor.setDecorations(decorationType, [])
      }
    }
    
    this.activeSuggestions.clear()
  }

  private getOrCreateDecorationType(suggestion: Suggestion): vscode.TextEditorDecorationType {
    const key = `${suggestion.type.category}_${suggestion.presentation.display}`
    
    if (!this.decorationTypes.has(key)) {
      const decorationType = vscode.window.createTextEditorDecorationType({
        backgroundColor: suggestion.presentation.style.backgroundColor,
        color: suggestion.presentation.style.color,
        fontWeight: suggestion.presentation.style.emphasis === 'bold' ? 'bold' : 'normal',
        fontStyle: suggestion.presentation.style.emphasis === 'italic' ? 'italic' : 'normal',
        textDecoration: suggestion.presentation.style.emphasis === 'underline' ? 'underline' : 'none'
      })
      
      this.decorationTypes.set(key, decorationType)
    }
    
    return this.decorationTypes.get(key)!
  }

  // Helper methods
  private getNearbyCode(document: vscode.TextDocument, position: vscode.Position, length: number): string {
    const startLine = Math.max(0, position.line - 5)
    const endLine = Math.min(document.lineCount - 1, position.line + 5)
    return document.getText(new vscode.Range(startLine, 0, endLine, 0))
  }

  private async inferUserIntention(event: InputEvent, recentEvents: InputEvent[]): Promise<string> {
    // Analyze recent events to infer user intention
    const eventTypes = recentEvents.map(e => e.type.action)
    
    if (eventTypes.includes('text_change') && eventTypes.includes('selection_change')) {
      return 'editing'
    } else if (eventTypes.includes('cursor_movement')) {
      return 'navigation'
    } else if (eventTypes.includes('execute_command')) {
      return 'command_execution'
    }
    
    return 'general'
  }

  private generateCacheKey(event: InputEvent, context: TriggerContext): string {
    return `${event.type.category}_${event.type.action}_${context.position.line}_${context.position.character}`
  }

  private async evaluateRule(rule: SuggestionRule, context: TriggerContext): Promise<boolean> {
    let totalScore = 0
    let totalWeight = 0
    
    for (const condition of rule.conditions) {
      const value = this.extractContextValue(context, condition.field)
      const satisfied = this.evaluateCondition(condition, value)
      
      totalScore += satisfied ? condition.weight : 0
      totalWeight += condition.weight
    }
    
    return totalWeight > 0 && totalScore / totalWeight > 0.5
  }

  private extractContextValue(context: TriggerContext, field: string): any {
    switch (field) {
      case 'language':
        return context.language
      case 'selection_length':
        return context.selection.end.character - context.selection.start.character
      case 'cursor_context':
        return this.analyzeCursorContext(context)
      default:
        return null
    }
  }

  private evaluateCondition(condition: SuggestionCondition, value: any): boolean {
    switch (condition.operator) {
      case 'equals':
        return value === condition.value
      case 'contains':
        return typeof value === 'string' && value.includes(condition.value)
      case 'greater_than':
        return typeof value === 'number' && value > condition.value
      case 'less_than':
        return typeof value === 'number' && value < condition.value
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(value)
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(value)
      default:
        return false
    }
  }

  private analyzeCursorContext(context: TriggerContext): string {
    const line = context.document.lineAt(context.position.line)
    const textBefore = line.text.substring(0, context.position.character)
    const textAfter = line.text.substring(context.position.character)
    
    if (textBefore.endsWith('.')) {
      return 'member_access'
    } else if (textBefore.includes('import ')) {
      return 'import_statement'
    } else if (!textAfter.trim()) {
      return 'end_of_line'
    }
    
    return 'general'
  }

  private async generateSuggestionFromRule(rule: SuggestionRule, context: TriggerContext): Promise<Suggestion | null> {
    const suggestionId = `${rule.id}_${Date.now()}`
    
    return {
      id: suggestionId,
      type: {
        category: 'code_completion',
        priority: 'medium',
        scope: 'line',
        urgency: 'contextual'
      },
      content: {
        title: rule.name,
        description: rule.description,
        preview: 'Preview text...'
      },
      trigger: {
        event: 'rule_match',
        conditions: rule.conditions.map(c => ({
          type: 'text_content',
          operator: c.operator,
          value: c.value,
          weight: c.weight
        })),
        context,
        confidence: 0.8,
        probability: 0.7
      },
      presentation: {
        display: 'inline',
        position: context.position,
        style: {
          color: '#888888',
          emphasis: 'italic'
        },
        duration: 5000,
        dismissible: true
      },
      actions: [
        {
          id: 'accept',
          title: 'Accept',
          description: 'Accept this suggestion',
          type: 'apply',
          command: 'suggestion.accept',
          arguments: [suggestionId]
        },
        {
          id: 'dismiss',
          title: 'Dismiss',
          description: 'Dismiss this suggestion',
          type: 'dismiss',
          command: 'suggestion.dismiss',
          arguments: [suggestionId]
        }
      ],
      metadata: {
        source: 'rule_engine',
        confidence: 0.8,
        relevance: 0.7,
        learningFactor: 0.5,
        performanceImpact: 0.1,
        userPreferenceScore: 0.6,
        contextualScore: 0.8
      },
      timestamp: new Date()
    }
  }

  private createSuggestionFromPrediction(prediction: any, context: TriggerContext): Suggestion {
    const suggestionId = `ml_${Date.now()}`
    
    return {
      id: suggestionId,
      type: {
        category: 'code_completion',
        priority: 'medium',
        scope: 'word',
        urgency: 'immediate'
      },
      content: {
        title: 'AI Suggestion',
        description: 'Machine learning based suggestion',
        preview: prediction.result
      },
      trigger: {
        event: 'ml_prediction',
        conditions: [],
        context,
        confidence: prediction.confidence,
        probability: prediction.confidence
      },
      presentation: {
        display: 'inline',
        position: context.position,
        style: {
          color: '#4CAF50',
          emphasis: 'none'
        },
        duration: 3000,
        dismissible: true
      },
      actions: [
        {
          id: 'accept',
          title: 'Accept',
          description: 'Accept this suggestion',
          type: 'apply',
          command: 'suggestion.accept',
          arguments: [suggestionId]
        }
      ],
      metadata: {
        source: 'ml_engine',
        confidence: prediction.confidence,
        relevance: 0.8,
        learningFactor: 0.9,
        performanceImpact: 0.2,
        userPreferenceScore: 0.7,
        contextualScore: 0.9
      },
      timestamp: new Date()
    }
  }

  private createPatternSuggestion(pattern: any, context: TriggerContext): Suggestion {
    const suggestionId = `pattern_${Date.now()}`
    
    return {
      id: suggestionId,
      type: {
        category: 'refactoring',
        priority: 'medium',
        scope: 'selection',
        urgency: 'contextual'
      },
      content: {
        title: `Apply ${pattern.name} Pattern`,
        description: `Refactor code to use ${pattern.name} pattern`,
        preview: 'Pattern-based refactoring...'
      },
      trigger: {
        event: 'pattern_match',
        conditions: [],
        context,
        confidence: pattern.confidence,
        probability: pattern.confidence
      },
      presentation: {
        display: 'lightbulb',
        position: context.position,
        style: {
          color: '#FF9800',
          emphasis: 'none'
        },
        duration: 10000,
        dismissible: true
      },
      actions: [
        {
          id: 'apply',
          title: 'Apply Pattern',
          description: 'Apply this pattern to the code',
          type: 'apply',
          command: 'suggestion.apply',
          arguments: [suggestionId]
        }
      ],
      metadata: {
        source: 'pattern_recognizer',
        confidence: pattern.confidence,
        relevance: 0.9,
        learningFactor: 0.6,
        performanceImpact: 0.3,
        userPreferenceScore: 0.8,
        contextualScore: 0.7
      },
      timestamp: new Date()
    }
  }

  private createRefactoringSuggestion(opportunity: any, analysis: SelectionAnalysis, editor: vscode.TextEditor): Suggestion {
    const suggestionId = `refactor_${Date.now()}`
    
    return {
      id: suggestionId,
      type: {
        category: 'refactoring',
        priority: 'medium',
        scope: 'selection',
        urgency: 'contextual'
      },
      content: {
        title: opportunity.title,
        description: opportunity.description,
        preview: opportunity.preview
      },
      trigger: {
        event: 'selection_analysis',
        conditions: [],
        context: {} as TriggerContext,
        confidence: opportunity.confidence,
        probability: 0.8
      },
      presentation: {
        display: 'lightbulb',
        position: analysis.selection.range.start,
        style: {
          color: '#2196F3',
          emphasis: 'none'
        },
        duration: 15000,
        dismissible: true
      },
      actions: [
        {
          id: 'apply',
          title: 'Apply Refactoring',
          description: 'Apply this refactoring',
          type: 'apply',
          command: 'suggestion.apply',
          arguments: [suggestionId]
        }
      ],
      metadata: {
        source: 'selection_analyzer',
        confidence: opportunity.confidence,
        relevance: 0.9,
        learningFactor: 0.7,
        performanceImpact: 0.4,
        userPreferenceScore: 0.8,
        contextualScore: 0.9
      },
      timestamp: new Date()
    }
  }

  private createOptimizationSuggestion(opportunity: any, analysis: SelectionAnalysis, editor: vscode.TextEditor): Suggestion {
    const suggestionId = `optimize_${Date.now()}`
    
    return {
      id: suggestionId,
      type: {
        category: 'optimization',
        priority: 'high',
        scope: 'selection',
        urgency: 'contextual'
      },
      content: {
        title: opportunity.title,
        description: opportunity.description,
        preview: opportunity.preview,
        benefits: opportunity.benefits
      },
      trigger: {
        event: 'selection_analysis',
        conditions: [],
        context: {} as TriggerContext,
        confidence: opportunity.confidence,
        probability: 0.8
      },
      presentation: {
        display: 'lightbulb',
        position: analysis.selection.range.start,
        style: {
          color: '#FF5722',
          emphasis: 'bold'
        },
        duration: 20000,
        dismissible: true
      },
      actions: [
        {
          id: 'apply',
          title: 'Apply Optimization',
          description: 'Apply this optimization',
          type: 'apply',
          command: 'suggestion.apply',
          arguments: [suggestionId]
        }
      ],
      metadata: {
        source: 'selection_analyzer',
        confidence: opportunity.confidence,
        relevance: 0.95,
        learningFactor: 0.8,
        performanceImpact: 0.2,
        userPreferenceScore: 0.9,
        contextualScore: 0.95
      },
      timestamp: new Date()
    }
  }

  private async generateDebuggingSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    // Generate debugging-specific suggestions
    return []
  }

  private async generateRefactoringSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    // Generate refactoring-specific suggestions
    return []
  }

  private async generateTestingSuggestions(context: TriggerContext): Promise<Suggestion[]> {
    // Generate testing-specific suggestions
    return []
  }

  private async generateMemberAccessSuggestions(document: vscode.TextDocument, position: vscode.Position): Promise<Suggestion[]> {
    // Generate member access completion suggestions
    return []
  }

  private async generateImportSuggestions(document: vscode.TextDocument, position: vscode.Position): Promise<Suggestion[]> {
    // Generate import statement suggestions
    return []
  }

  private async generateFileLevelSuggestions(document: vscode.TextDocument): Promise<Suggestion[]> {
    // Generate file-level suggestions
    return []
  }

  private async checkTriggersForDocumentChange(event: vscode.TextDocumentChangeEvent): Promise<void> {
    // Check if document changes trigger any suggestions
  }

  private matchesUserPreferences(suggestion: Suggestion, preferences: any): boolean {
    // Check if suggestion matches user preferences
    return true
  }

  private calculateSuggestionScore(suggestion: Suggestion): number {
    const weights = {
      confidence: 0.3,
      relevance: 0.25,
      userPreference: 0.2,
      contextual: 0.15,
      learning: 0.1
    }
    
    return (
      suggestion.metadata.confidence * weights.confidence +
      suggestion.metadata.relevance * weights.relevance +
      suggestion.metadata.userPreferenceScore * weights.userPreference +
      suggestion.metadata.contextualScore * weights.contextual +
      suggestion.metadata.learningFactor * weights.learning
    )
  }

  // Public API
  getSuggestions(): Suggestion[] {
    return Array.from(this.suggestions.values())
  }

  getActiveSuggestions(): Suggestion[] {
    return Array.from(this.activeSuggestions.values())
  }

  getStats(): SuggestionStats {
    return { ...this.stats }
  }

  getFeedback(): SuggestionFeedback[] {
    return [...this.feedback]
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    if (!enabled) {
      this.clearActiveSuggestions()
    }
  }

  dispose(): void {
    this.clearActiveSuggestions()
    this.disposables.forEach(d => d.dispose())
    this.decorationTypes.forEach(d => d.dispose())
  }
}