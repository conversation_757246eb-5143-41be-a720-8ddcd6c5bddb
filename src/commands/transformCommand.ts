import * as vscode from 'vscode'
import { MarkdownFormatter } from '../services/output/markdownFormatter'
import { TaskGenerator, TaskBreakdown, GenerationContext } from '../services/output/taskGenerator'
import { ContextAnalyzer } from '../services/context/contextAnalyzer'
import { InputHandler } from '../services/input/inputHandler'
import { SelectionAnalyzer } from '../services/input/selectionAnalyzer'
import { SuggestionProvider } from '../services/input/suggestionProvider'
import { LearningEngine } from '../services/memory/learningEngine'
import { PatternRecognizer } from '../services/memory/patternRecognizer'
import { PreferenceTracker } from '../services/memory/preferenceTracker'
import { SemanticSearch } from '../services/embeddings/semanticSearch'

export interface TransformationRequest {
  input: string
  context: TransformationContext
  options: TransformationOptions
  source: TransformationSource
}

export interface TransformationContext {
  document?: vscode.TextDocument
  selection?: vscode.Selection
  position?: vscode.Position
  language?: string
  workspace?: string
  projectType?: string
  framework?: string
  currentFile?: string
  nearbyFiles?: string[]
  gitBranch?: string
  gitStatus?: string
  environment?: string
  userContext?: UserContext
}

export interface UserContext {
  experience: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  preferences: UserPreferences
  workingHours: [number, number]
  timezone: string
  teamSize: number
  methodology: 'agile' | 'waterfall' | 'kanban' | 'scrum'
}

export interface UserPreferences {
  detailLevel: 'minimal' | 'standard' | 'detailed' | 'comprehensive'
  outputFormat: 'markdown' | 'json' | 'yaml' | 'plain_text'
  includeTimelines: boolean
  includeResources: boolean
  includeDependencies: boolean
  includeTestCases: boolean
  includeDocumentation: boolean
  taskGranularity: 'high_level' | 'medium' | 'detailed' | 'granular'
  estimationMethod: 'time_based' | 'story_points' | 'complexity_based'
  templatePreference: string
  codeHighlighting: boolean
  interactiveMode: boolean
}

export interface TransformationOptions {
  outputFormat: 'markdown' | 'json' | 'yaml' | 'html' | 'pdf'
  includeMetadata: boolean
  includeTimestamp: boolean
  includeSource: boolean
  saveToFile: boolean
  openInEditor: boolean
  copyToClipboard: boolean
  showProgress: boolean
  enableSuggestions: boolean
  useAI: boolean
  template?: string
  customFields?: Record<string, any>
  validation?: ValidationOptions
  export?: ExportOptions
}

export interface ValidationOptions {
  checkCompleteness: boolean
  checkConsistency: boolean
  checkFeasibility: boolean
  checkDependencies: boolean
  checkResources: boolean
  checkTimelines: boolean
  suggestImprovements: boolean
}

export interface ExportOptions {
  format: 'markdown' | 'json' | 'yaml' | 'html' | 'pdf' | 'docx'
  destination: 'file' | 'clipboard' | 'email' | 'slack' | 'teams'
  template?: string
  styling?: string
  includeAttachments: boolean
  compress: boolean
}

export interface TransformationSource {
  type: 'command_palette' | 'context_menu' | 'keyboard_shortcut' | 'editor_action' | 'api' | 'automation'
  trigger: string
  location: vscode.Position
  selection?: vscode.Selection
  metadata: Record<string, any>
}

export interface TransformationResult {
  id: string
  request: TransformationRequest
  breakdown: TaskBreakdown
  output: TransformationOutput
  metadata: TransformationMetadata
  success: boolean
  errors: TransformationError[]
  warnings: TransformationWarning[]
  suggestions: TransformationSuggestion[]
}

export interface TransformationOutput {
  content: string
  format: string
  size: number
  lineCount: number
  sections: OutputSection[]
  attachments: OutputAttachment[]
  references: OutputReference[]
}

export interface OutputSection {
  id: string
  title: string
  content: string
  type: 'header' | 'paragraph' | 'list' | 'table' | 'code' | 'diagram'
  level: number
  metadata: SectionMetadata
}

export interface SectionMetadata {
  generated: boolean
  editable: boolean
  source: string
  confidence: number
  dependencies: string[]
  tags: string[]
}

export interface OutputAttachment {
  id: string
  name: string
  type: 'image' | 'file' | 'link' | 'code' | 'data'
  content: string
  metadata: AttachmentMetadata
}

export interface AttachmentMetadata {
  size: number
  format: string
  created: Date
  source: string
  description: string
}

export interface OutputReference {
  id: string
  title: string
  url: string
  type: 'documentation' | 'tutorial' | 'example' | 'tool' | 'library'
  relevance: number
  metadata: ReferenceMetadata
}

export interface ReferenceMetadata {
  description: string
  author: string
  lastUpdated: Date
  tags: string[]
  reliability: number
}

export interface TransformationMetadata {
  id: string
  created: Date
  duration: number
  version: string
  confidence: number
  complexity: number
  aiUsed: boolean
  templateUsed?: string
  userFeedback?: UserFeedback
  performance: PerformanceMetrics
}

export interface UserFeedback {
  rating: number
  comments: string
  usefulness: number
  accuracy: number
  completeness: number
  suggestions: string[]
  timestamp: Date
}

export interface PerformanceMetrics {
  processingTime: number
  memoryUsage: number
  apiCalls: number
  cacheHits: number
  cacheMisses: number
  errorRate: number
}

export interface TransformationError {
  code: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  context: string
  suggestions: string[]
}

export interface TransformationWarning {
  code: string
  message: string
  category: 'data_quality' | 'performance' | 'compatibility' | 'security'
  context: string
  recommendation: string
}

export interface TransformationSuggestion {
  id: string
  title: string
  description: string
  type: 'improvement' | 'alternative' | 'optimization' | 'extension'
  priority: 'low' | 'medium' | 'high'
  confidence: number
  implementation: string
  benefits: string[]
  risks: string[]
}

export class TransformCommand {
  private taskGenerator: TaskGenerator
  private markdownFormatter: MarkdownFormatter
  private contextAnalyzer: ContextAnalyzer
  private inputHandler: InputHandler
  private selectionAnalyzer: SelectionAnalyzer
  private suggestionProvider: SuggestionProvider
  private learningEngine: LearningEngine
  private patternRecognizer: PatternRecognizer
  private preferenceTracker: PreferenceTracker
  private semanticSearch: SemanticSearch
  
  private transformationHistory: TransformationResult[]
  private activeTransformations: Map<string, TransformationResult>
  private disposables: vscode.Disposable[]

  constructor(
    taskGenerator: TaskGenerator,
    markdownFormatter: MarkdownFormatter,
    contextAnalyzer: ContextAnalyzer,
    inputHandler: InputHandler,
    selectionAnalyzer: SelectionAnalyzer,
    suggestionProvider: SuggestionProvider,
    learningEngine: LearningEngine,
    patternRecognizer: PatternRecognizer,
    preferenceTracker: PreferenceTracker,
    semanticSearch: SemanticSearch
  ) {
    this.taskGenerator = taskGenerator
    this.markdownFormatter = markdownFormatter
    this.contextAnalyzer = contextAnalyzer
    this.inputHandler = inputHandler
    this.selectionAnalyzer = selectionAnalyzer
    this.suggestionProvider = suggestionProvider
    this.learningEngine = learningEngine
    this.patternRecognizer = patternRecognizer
    this.preferenceTracker = preferenceTracker
    this.semanticSearch = semanticSearch
    
    this.transformationHistory = []
    this.activeTransformations = new Map()
    this.disposables = []
    
    this.registerCommands()
  }

  private registerCommands(): void {
    this.disposables.push(
      vscode.commands.registerCommand('extension.transformToTasks', async (text?: string) => {
        await this.executeTransformCommand(text)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.transformSelectionToTasks', async () => {
        const editor = vscode.window.activeTextEditor
        if (editor && !editor.selection.isEmpty) {
          const selectedText = editor.document.getText(editor.selection)
          await this.executeTransformCommand(selectedText)
        }
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.quickTransform', async () => {
        await this.executeQuickTransform()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.interactiveTransform', async () => {
        await this.executeInteractiveTransform()
      })
    )
  }

  async executeTransformCommand(inputText?: string): Promise<void> {
    try {
      const input = inputText || await this.getInputText()
      if (!input) {
        vscode.window.showWarningMessage('No input provided for transformation')
        return
      }

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: 'Transforming to Task Breakdown',
        cancellable: true
      }, async (progress, token) => {
        progress.report({ increment: 0, message: 'Analyzing input...' })

        const request = await this.buildTransformationRequest(input)
        
        progress.report({ increment: 20, message: 'Generating task breakdown...' })
        
        const result = await this.executeTransformation(request, progress, token)
        
        progress.report({ increment: 80, message: 'Finalizing output...' })
        
        await this.handleTransformationResult(result)
        
        progress.report({ increment: 100, message: 'Transformation complete' })
      })

    } catch (error) {
      console.error('Error executing transform command:', error)
      vscode.window.showErrorMessage(`Transformation failed: ${error}`)
    }
  }

  async execute(...args: any[]): Promise<void> {
    await this.executeTransformCommand(args[0])
  }

  private async getInputText(): Promise<string | undefined> {
    const editor = vscode.window.activeTextEditor
    
    if (editor && !editor.selection.isEmpty) {
      return editor.document.getText(editor.selection)
    }
    
    return await vscode.window.showInputBox({
      prompt: 'Enter your requirements or description to transform into tasks',
      placeHolder: 'e.g., "Build a React dashboard with user authentication and data visualization"',
      ignoreFocusOut: true,
      validateInput: (value) => {
        if (!value.trim()) {
          return 'Please enter some text to transform'
        }
        if (value.length < 10) {
          return 'Please provide more detailed requirements'
        }
        return undefined
      }
    })
  }

  private async buildTransformationRequest(input: string): Promise<TransformationRequest> {
    const context = await this.buildTransformationContext()
    const options = await this.buildTransformationOptions()
    const source = this.buildTransformationSource()

    return {
      input,
      context,
      options,
      source
    }
  }

  private async buildTransformationContext(): Promise<TransformationContext> {
    const editor = vscode.window.activeTextEditor
    const workspace = vscode.workspace.rootPath
    
    return {
      document: editor?.document,
      selection: editor?.selection,
      position: editor?.selection.active,
      language: editor?.document.languageId,
      workspace,
      projectType: await this.detectProjectType(),
      framework: await this.detectFramework(),
      currentFile: editor?.document.fileName,
      nearbyFiles: await this.getNearbyFiles(),
      gitBranch: await this.getGitBranch(),
      gitStatus: await this.getGitStatus(),
      environment: await this.detectEnvironment(),
      userContext: {
        experience: this.getUserExperience(),
        preferences: this.buildUserPreferences(),
        workingHours: [9, 17],
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        teamSize: 1,
        methodology: 'agile'
      }
    }
  }

  private async buildTransformationOptions(): Promise<TransformationOptions> {
    const config = vscode.workspace.getConfiguration('taskTransformation')
    
    return {
      outputFormat: config.get('outputFormat', 'markdown'),
      includeMetadata: config.get('includeMetadata', true),
      includeTimestamp: config.get('includeTimestamp', true),
      includeSource: config.get('includeSource', false),
      saveToFile: config.get('saveToFile', false),
      openInEditor: config.get('openInEditor', true),
      copyToClipboard: config.get('copyToClipboard', false),
      showProgress: config.get('showProgress', true),
      enableSuggestions: config.get('enableSuggestions', true),
      useAI: config.get('useAI', true),
      template: config.get('defaultTemplate'),
      validation: {
        checkCompleteness: config.get('validation.checkCompleteness', true),
        checkConsistency: config.get('validation.checkConsistency', true),
        checkFeasibility: config.get('validation.checkFeasibility', false),
        checkDependencies: config.get('validation.checkDependencies', true),
        checkResources: config.get('validation.checkResources', false),
        checkTimelines: config.get('validation.checkTimelines', false),
        suggestImprovements: config.get('validation.suggestImprovements', true)
      },
      export: {
        format: config.get('export.format', 'markdown'),
        destination: config.get('export.destination', 'file'),
        includeAttachments: config.get('export.includeAttachments', false),
        compress: config.get('export.compress', false)
      }
    }
  }

  private buildTransformationSource(): TransformationSource {
    const editor = vscode.window.activeTextEditor
    
    return {
      type: 'command_palette',
      trigger: 'extension.transformToTasks',
      location: editor?.selection.active || new vscode.Position(0, 0),
      selection: editor?.selection,
      metadata: {
        editorOpen: !!editor,
        hasSelection: !!(editor && !editor.selection.isEmpty),
        language: editor?.document.languageId,
        timestamp: new Date().toISOString()
      }
    }
  }

  private async executeTransformation(
    request: TransformationRequest,
    progress: vscode.Progress<{ increment?: number; message?: string }>,
    token: vscode.CancellationToken
  ): Promise<TransformationResult> {
    const transformationId = this.generateTransformationId()
    const startTime = Date.now()
    
    try {
      if (token.isCancellationRequested) {
        throw new Error('Transformation cancelled by user')
      }

      const generationContext = this.buildGenerationContext(request)
      
      progress.report({ increment: 10, message: 'Building task structure...' })
      
      const breakdown = await this.taskGenerator.generateTaskBreakdown(request.input, generationContext)
      
      progress.report({ increment: 30, message: 'Formatting output...' })
      
      const output = await this.generateOutput(breakdown, request.options)
      
      progress.report({ increment: 20, message: 'Validating result...' })
      
      const validationResults = request.options.validation ? await this.validateResult(breakdown, request.options.validation) : { errors: [], warnings: [], suggestions: [] }
      
      progress.report({ increment: 10, message: 'Finalizing...' })
      
      const result: TransformationResult = {
        id: transformationId,
        request,
        breakdown,
        output,
        metadata: {
          id: transformationId,
          created: new Date(),
          duration: Date.now() - startTime,
          version: '1.0.0',
          confidence: this.calculateConfidence(breakdown),
          complexity: this.calculateComplexity(breakdown),
          aiUsed: request.options.useAI,
          templateUsed: request.options.template,
          performance: {
            processingTime: Date.now() - startTime,
            memoryUsage: process.memoryUsage().heapUsed,
            apiCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errorRate: 0
          }
        },
        success: true,
        errors: validationResults.errors,
        warnings: validationResults.warnings,
        suggestions: validationResults.suggestions
      }
      
      this.activeTransformations.set(transformationId, result)
      this.transformationHistory.push(result)
      
      return result
      
    } catch (error) {
      console.error('Error executing transformation:', error)
      
      const errorResult: TransformationResult = {
        id: transformationId,
        request,
        breakdown: {} as TaskBreakdown,
        output: {
          content: '',
          format: 'error',
          size: 0,
          lineCount: 0,
          sections: [],
          attachments: [],
          references: []
        },
        metadata: {
          id: transformationId,
          created: new Date(),
          duration: Date.now() - startTime,
          version: '1.0.0',
          confidence: 0,
          complexity: 0,
          aiUsed: request.options.useAI,
          performance: {
            processingTime: Date.now() - startTime,
            memoryUsage: process.memoryUsage().heapUsed,
            apiCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            errorRate: 1
          }
        },
        success: false,
        errors: [{
          code: 'TRANSFORMATION_ERROR',
          message: error.message,
          severity: 'high',
          context: 'transformation_execution',
          suggestions: ['Try simplifying the input', 'Check your configuration', 'Try again later']
        }],
        warnings: [],
        suggestions: []
      }
      
      return errorResult
    }
  }

  private buildGenerationContext(request: TransformationRequest): GenerationContext {
    return {
      input: request.input,
      language: request.context.language || 'unknown',
      framework: request.context.framework || 'unknown',
      projectType: request.context.projectType || 'unknown',
      complexity: 'moderate',
      timeline: 'weeks',
      resources: ['developer'],
      constraints: [],
      preferences: {
        detailLevel: request.context.userContext?.preferences.detailLevel || 'standard',
        methodology: request.context.userContext?.methodology || 'agile',
        estimationMethod: request.context.userContext?.preferences.estimationMethod || 'time_based',
        riskTolerance: 'medium',
        automationLevel: 'medium',
        documentationLevel: 'standard',
        testingStrategy: 'comprehensive'
      },
      codebase: {
        repository: '',
        branch: request.context.gitBranch || 'main',
        languages: [request.context.language || 'unknown'],
        frameworks: [request.context.framework || 'unknown'],
        architecture: 'unknown',
        patterns: [],
        conventions: [],
        coverage: 0
      },
      stakeholders: []
    }
  }

  private async generateOutput(breakdown: TaskBreakdown, options: TransformationOptions): Promise<TransformationOutput> {
    let content: string
    let format: string
    
    switch (options.outputFormat) {
      case 'markdown':
        content = await this.taskGenerator.generateMarkdown(breakdown)
        format = 'markdown'
        break
      case 'json':
        content = JSON.stringify(breakdown, null, 2)
        format = 'json'
        break
      case 'yaml':
        content = this.convertToYaml(breakdown)
        format = 'yaml'
        break
      default:
        content = await this.taskGenerator.generateMarkdown(breakdown)
        format = 'markdown'
    }
    
    const sections = this.extractSections(content, format)
    
    return {
      content,
      format,
      size: content.length,
      lineCount: content.split('\n').length,
      sections,
      attachments: [],
      references: []
    }
  }

  private async validateResult(breakdown: TaskBreakdown, validation: ValidationOptions): Promise<{ errors: TransformationError[]; warnings: TransformationWarning[]; suggestions: TransformationSuggestion[] }> {
    const errors: TransformationError[] = []
    const warnings: TransformationWarning[] = []
    const suggestions: TransformationSuggestion[] = []
    
    if (validation.checkCompleteness) {
      if (breakdown.tasks.length === 0) {
        errors.push({
          code: 'NO_TASKS',
          message: 'No tasks were generated',
          severity: 'high',
          context: 'task_generation',
          suggestions: ['Try providing more detailed requirements', 'Check if the input is clear and specific']
        })
      }
    }
    
    return { errors, warnings, suggestions }
  }

  private async handleTransformationResult(result: TransformationResult): Promise<void> {
    if (!result.success) {
      const message = result.errors.length > 0 ? result.errors[0].message : 'Unknown error occurred'
      vscode.window.showErrorMessage(`Transformation failed: ${message}`)
      return
    }
    
    const options = result.request.options
    
    if (options.copyToClipboard) {
      await vscode.env.clipboard.writeText(result.output.content)
    }
    
    if (options.openInEditor) {
      await this.openInEditor(result)
    }
    
    const successMessage = `Task breakdown generated successfully (${result.breakdown.tasks.length} tasks)`
    vscode.window.showInformationMessage(successMessage)
  }

  private async openInEditor(result: TransformationResult): Promise<void> {
    const document = await vscode.workspace.openTextDocument({
      content: result.output.content,
      language: result.output.format
    })
    await vscode.window.showTextDocument(document)
  }

  private async executeQuickTransform(): Promise<void> {
    const input = await vscode.window.showInputBox({
      prompt: 'Quick Transform: Enter your task description',
      placeHolder: 'e.g., "Add user authentication"'
    })
    
    if (input) {
      await this.executeTransformCommand(input)
    }
  }

  private async executeInteractiveTransform(): Promise<void> {
    const steps = [
      { key: 'description', prompt: 'What do you want to build?', placeholder: 'e.g., Web application' },
      { key: 'features', prompt: 'What features should it have?', placeholder: 'e.g., User login, dashboard, reports' },
      { key: 'timeline', prompt: 'What\'s your timeline?', placeholder: 'e.g., 2 weeks, 1 month' },
      { key: 'resources', prompt: 'What resources do you have?', placeholder: 'e.g., 2 developers, 1 designer' }
    ]
    
    const answers: Record<string, string> = {}
    
    for (const step of steps) {
      const answer = await vscode.window.showInputBox({
        prompt: step.prompt,
        placeHolder: step.placeholder
      })
      
      if (!answer) {
        return
      }
      
      answers[step.key] = answer
    }
    
    const input = `
Project: ${answers.description}
Features: ${answers.features}
Timeline: ${answers.timeline}
Resources: ${answers.resources}
`
    
    await this.executeTransformCommand(input)
  }

  // Helper methods
  private generateTransformationId(): string {
    return `transform_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private calculateConfidence(breakdown: TaskBreakdown): number {
    let confidence = 0.5
    
    if (breakdown.tasks.length > 0) confidence += 0.2
    if (breakdown.dependencies.length > 0) confidence += 0.1
    if (breakdown.timeline.duration > 0) confidence += 0.1
    if (breakdown.resources.length > 0) confidence += 0.1
    
    return Math.min(confidence, 1.0)
  }

  private calculateComplexity(breakdown: TaskBreakdown): number {
    return Math.min(breakdown.tasks.length / 10, 1.0)
  }

  private convertToYaml(obj: any): string {
    return JSON.stringify(obj, null, 2).replace(/"/g, '').replace(/,/g, '')
  }

  private extractSections(content: string, format: string): OutputSection[] {
    const sections: OutputSection[] = []
    
    if (format === 'markdown') {
      const lines = content.split('\n')
      let currentSection: OutputSection | null = null
      
      for (const line of lines) {
        const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
        if (headerMatch) {
          if (currentSection) {
            sections.push(currentSection)
          }
          
          currentSection = {
            id: `section_${sections.length}`,
            title: headerMatch[2],
            content: '',
            type: 'header',
            level: headerMatch[1].length,
            metadata: {
              generated: true,
              editable: true,
              source: 'markdown_parser',
              confidence: 1.0,
              dependencies: [],
              tags: ['header']
            }
          }
        } else if (currentSection) {
          currentSection.content += line + '\n'
        }
      }
      
      if (currentSection) {
        sections.push(currentSection)
      }
    }
    
    return sections
  }

  private async detectProjectType(): Promise<string> {
    const workspace = vscode.workspace.rootPath
    if (!workspace) return 'unknown'
    
    try {
      const packageJson = await vscode.workspace.fs.readFile(vscode.Uri.file(`${workspace}/package.json`))
      const pkg = JSON.parse(packageJson.toString())
      
      if (pkg.dependencies?.react) return 'react_app'
      if (pkg.dependencies?.vue) return 'vue_app'
      if (pkg.dependencies?.angular) return 'angular_app'
      if (pkg.dependencies?.express) return 'node_server'
      
      return 'javascript_project'
    } catch {
      return 'unknown'
    }
  }

  private async detectFramework(): Promise<string> {
    const workspace = vscode.workspace.rootPath
    if (!workspace) return 'unknown'
    
    try {
      const packageJson = await vscode.workspace.fs.readFile(vscode.Uri.file(`${workspace}/package.json`))
      const pkg = JSON.parse(packageJson.toString())
      
      if (pkg.dependencies?.react) return 'react'
      if (pkg.dependencies?.vue) return 'vue'
      if (pkg.dependencies?.angular) return 'angular'
      if (pkg.dependencies?.express) return 'express'
      if (pkg.dependencies?.fastify) return 'fastify'
      
      return 'unknown'
    } catch {
      return 'unknown'
    }
  }

  private async getNearbyFiles(): Promise<string[]> {
    const editor = vscode.window.activeTextEditor
    if (!editor) return []
    
    const currentDir = editor.document.uri.with({ path: editor.document.uri.path.split('/').slice(0, -1).join('/') })
    
    try {
      const files = await vscode.workspace.fs.readDirectory(currentDir)
      return files.map(([name]) => name)
    } catch {
      return []
    }
  }

  private async getGitBranch(): Promise<string> {
    try {
      const gitExtension = vscode.extensions.getExtension('vscode.git')
      if (gitExtension) {
        const git = gitExtension.exports.getAPI(1)
        const repo = git.repositories[0]
        if (repo) {
          return repo.state.HEAD?.name || 'main'
        }
      }
    } catch {
      // Ignore errors
    }
    
    return 'main'
  }

  private async getGitStatus(): Promise<string> {
    try {
      const gitExtension = vscode.extensions.getExtension('vscode.git')
      if (gitExtension) {
        const git = gitExtension.exports.getAPI(1)
        const repo = git.repositories[0]
        if (repo) {
          const changes = repo.state.workingTreeChanges.length
          const staged = repo.state.indexChanges.length
          return `${changes} changes, ${staged} staged`
        }
      }
    } catch {
      // Ignore errors
    }
    
    return 'clean'
  }

  private async detectEnvironment(): Promise<string> {
    const workspace = vscode.workspace.rootPath
    if (!workspace) return 'unknown'
    
    try {
      const envFile = await vscode.workspace.fs.readFile(vscode.Uri.file(`${workspace}/.env`))
      const content = envFile.toString()
      
      if (content.includes('NODE_ENV=production')) return 'production'
      if (content.includes('NODE_ENV=development')) return 'development'
      if (content.includes('NODE_ENV=test')) return 'test'
      
      return 'development'
    } catch {
      return 'development'
    }
  }

  private getUserExperience(): UserContext['experience'] {
    const config = vscode.workspace.getConfiguration('taskTransformation')
    return config.get('userExperience', 'intermediate')
  }

  private buildUserPreferences(): UserPreferences {
    const config = vscode.workspace.getConfiguration('taskTransformation')
    
    return {
      detailLevel: config.get('detailLevel', 'standard'),
      outputFormat: config.get('outputFormat', 'markdown'),
      includeTimelines: config.get('includeTimelines', true),
      includeResources: config.get('includeResources', true),
      includeDependencies: config.get('includeDependencies', true),
      includeTestCases: config.get('includeTestCases', true),
      includeDocumentation: config.get('includeDocumentation', true),
      taskGranularity: config.get('taskGranularity', 'medium'),
      estimationMethod: config.get('estimationMethod', 'time_based'),
      templatePreference: config.get('templatePreference', 'default'),
      codeHighlighting: config.get('codeHighlighting', true),
      interactiveMode: config.get('interactiveMode', false)
    }
  }

  getTransformationHistory(): TransformationResult[] {
    return [...this.transformationHistory]
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
  }
}
