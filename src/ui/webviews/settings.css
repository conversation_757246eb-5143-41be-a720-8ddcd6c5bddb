/* Task Transformation Settings Panel CSS */

:root {
    --primary-color: var(--vscode-button-background);
    --primary-hover: var(--vscode-button-hoverBackground);
    --danger-color: var(--vscode-errorForeground);
    --success-color: var(--vscode-charts-green);
    --warning-color: var(--vscode-warningForeground);
    --info-color: var(--vscode-charts-blue);
    --border-radius: 6px;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --transition: all 0.2s ease;
}

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--vscode-font-family, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif);
    font-size: var(--vscode-font-size, 13px);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Layout */
.settings-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

.settings-sidebar {
    width: 280px;
    background-color: var(--vscode-sideBar-background);
    border-right: 1px solid var(--vscode-sideBar-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--vscode-widget-border);
}

.sidebar-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    color: var(--vscode-sideBarTitle-foreground);
}

.search-container {
    position: relative;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 32px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: var(--border-radius);
    font-size: 13px;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.2);
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--vscode-input-placeholderForeground);
    font-size: 14px;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: var(--transition);
    border-left: 3px solid transparent;
    text-decoration: none;
    color: var(--vscode-sideBar-foreground);
}

.category-item:hover {
    background-color: var(--vscode-list-hoverBackground);
    color: var(--vscode-list-hoverForeground);
}

.category-item.active {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
    border-left-color: var(--vscode-focusBorder);
}

.category-icon {
    margin-right: 12px;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.category-name {
    font-weight: 500;
    font-size: 14px;
}

.settings-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.main-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--vscode-widget-border);
    background-color: var(--vscode-editor-background);
}

.main-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: var(--vscode-foreground);
}

.main-description {
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
    margin: 0;
}

.main-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 30px;
}

/* Sections */
.settings-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--vscode-widget-border);
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: var(--vscode-foreground);
}

.section-description {
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
    margin: 5px 0 0 0;
}

.section-toggle {
    background: none;
    border: none;
    color: var(--vscode-descriptionForeground);
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.section-toggle:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.section-content {
    display: grid;
    gap: 25px;
}

.section-content.collapsed {
    display: none;
}

/* Form fields */
.field-group {
    display: grid;
    gap: 8px;
}

.field-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.field-label {
    font-weight: 600;
    color: var(--vscode-foreground);
    font-size: 14px;
}

.field-required {
    color: var(--danger-color);
    font-size: 12px;
}

.field-tooltip {
    position: relative;
    cursor: help;
}

.tooltip-icon {
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
}

.tooltip-content {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--vscode-editorHoverWidget-background);
    color: var(--vscode-editorHoverWidget-foreground);
    border: 1px solid var(--vscode-editorHoverWidget-border);
    border-radius: var(--border-radius);
    padding: 10px;
    max-width: 300px;
    font-size: 12px;
    line-height: 1.4;
    z-index: 1000;
    box-shadow: var(--shadow);
    visibility: hidden;
    opacity: 0;
    transition: var(--transition);
    margin-top: 5px;
}

.field-tooltip:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
}

.field-description {
    color: var(--vscode-descriptionForeground);
    font-size: 13px;
    margin-top: 4px;
}

/* Input fields */
.field-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: var(--border-radius);
    font-size: 13px;
    transition: var(--transition);
    font-family: inherit;
}

.field-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.2);
}

.field-input.error {
    border-color: var(--danger-color);
}

.field-input.success {
    border-color: var(--success-color);
}

.field-textarea {
    min-height: 100px;
    resize: vertical;
    font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', monospace);
}

.field-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-dropdown-background);
    color: var(--vscode-dropdown-foreground);
    border-radius: var(--border-radius);
    font-size: 13px;
    cursor: pointer;
    transition: var(--transition);
}

.field-select:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.2);
}

/* Checkbox */
.field-checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.field-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    border: 2px solid var(--vscode-checkbox-border);
    background-color: var(--vscode-checkbox-background);
    border-radius: 3px;
    transition: var(--transition);
}

.field-checkbox.checked {
    background-color: var(--vscode-checkbox-selectBackground);
    border-color: var(--vscode-checkbox-selectBorder);
}

.field-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--vscode-checkbox-selectForeground);
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label {
    font-size: 14px;
    color: var(--vscode-foreground);
    user-select: none;
}

/* Range input */
.field-range-container {
    display: grid;
    gap: 10px;
}

.field-range {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background-color: var(--vscode-progressBar-background);
    outline: none;
    cursor: pointer;
}

.field-range::-webkit-slider-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: var(--vscode-button-background);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

.range-value {
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: var(--vscode-foreground);
}

/* Color input */
.field-color {
    width: 60px;
    height: 40px;
    border: 1px solid var(--vscode-input-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    background: transparent;
    outline: none;
    transition: var(--transition);
}

.field-color:hover {
    border-color: var(--vscode-focusBorder);
}

/* Array fields */
.field-array {
    display: grid;
    gap: 10px;
}

.array-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-input-border);
    border-radius: var(--border-radius);
}

.array-item-input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 4px;
    font-size: 13px;
}

.array-item-input:focus {
    outline: none;
    border-color: var(--vscode-focusBorder);
}

.array-item-remove {
    background-color: var(--danger-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.array-item-remove:hover {
    opacity: 0.8;
}

.array-add-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: 1px solid var(--vscode-button-border);
    border-radius: var(--border-radius);
    padding: 10px 16px;
    cursor: pointer;
    font-size: 13px;
    transition: var(--transition);
    justify-self: start;
}

.array-add-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

/* Field messages */
.field-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.field-warning {
    color: var(--warning-color);
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.field-success {
    color: var(--success-color);
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.field-info {
    color: var(--info-color);
    font-size: 12px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: 1px solid var(--vscode-button-border);
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.btn-primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.btn-primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.btn-secondary {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn-secondary:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    opacity: 0.9;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-large {
    padding: 12px 24px;
    font-size: 14px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
}

.status-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--vscode-progressBar-background);
    border-top: 2px solid var(--vscode-button-background);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.highlighted {
    background-color: rgba(255, 255, 0, 0.1);
    border-color: rgba(255, 255, 0, 0.3);
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Connection test */
.connection-test {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.connection-status {
    font-size: 12px;
    font-weight: 500;
}

/* Key bindings */
.key-binding-input {
    position: relative;
}

.key-binding-display {
    display: flex;
    align-items: center;
    gap: 5px;
    font-family: var(--vscode-editor-font-family, 'Consolas', 'Monaco', monospace);
    font-size: 13px;
}

.key {
    background-color: var(--vscode-keybindingLabel-background);
    color: var(--vscode-keybindingLabel-foreground);
    border: 1px solid var(--vscode-keybindingLabel-border);
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
}

/* Layout variations */
.field-group-horizontal {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.field-group-triple {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

.divider {
    height: 1px;
    background-color: var(--vscode-widget-border);
    margin: 30px 0;
}

/* Cards */
.card {
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-widget-border);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.card-description {
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
    margin: 5px 0 0 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .settings-container {
        flex-direction: column;
    }

    .settings-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--vscode-sideBar-border);
    }

    .main-content {
        padding: 20px;
    }

    .field-group-horizontal,
    .field-group-triple {
        grid-template-columns: 1fr;
    }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .field-input,
    .field-select,
    .btn {
        border-width: 2px;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}