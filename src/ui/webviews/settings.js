/**
 * Settings Panel WebView JavaScript
 * Handles all client-side interactions for the settings panel
 */

class SettingsManager {
    constructor() {
        this.vscode = acquireVsCodeApi()
        this.currentSettings = {}
        this.settingsCategories = []
        this.activeCategory = 'general'
        this.searchQuery = ''
        this.unsavedChanges = false
        this.validationErrors = new Map()
        
        this.initializeEventListeners()
        this.requestSettings()
    }

    initializeEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput')
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value.toLowerCase()
                this.filterAndRenderSettings()
            })
        }

        // Main action buttons
        this.setupMainActionButtons()

        // Window message listener
        window.addEventListener('message', (event) => {
            this.handleMessage(event.data)
        })

        // Prevent form submission and handle keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault()
                        this.saveSettings()
                        break
                    case 'f':
                        e.preventDefault()
                        document.getElementById('searchInput')?.focus()
                        break
                    case 'r':
                        e.preventDefault()
                        this.resetSettings()
                        break
                }
            }
        })

        // Warn about unsaved changes
        window.addEventListener('beforeunload', (e) => {
            if (this.unsavedChanges) {
                e.preventDefault()
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?'
                return e.returnValue
            }
        })
    }

    setupMainActionButtons() {
        const saveBtn = document.getElementById('saveBtn')
        const resetBtn = document.getElementById('resetBtn')
        const importBtn = document.getElementById('importBtn')
        const exportBtn = document.getElementById('exportBtn')

        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSettings())
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.showResetDialog())
        }

        if (importBtn) {
            importBtn.addEventListener('click', () => this.importSettings())
        }

        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportSettings())
        }
    }

    requestSettings() {
        this.vscode.postMessage({ command: 'getSettings' })
    }

    handleMessage(message) {
        switch (message.command) {
            case 'settingsLoaded':
                this.currentSettings = message.settings
                this.settingsCategories = message.categories
                this.renderSettings()
                break

            case 'settingUpdated':
                this.updateSettingValue(message.field, message.value)
                this.markAsChanged()
                break

            case 'validationResult':
                this.handleValidationResult(message.result)
                break

            case 'connectionTestResult':
                this.handleConnectionTestResult(message.result)
                break

            case 'settingsSaved':
                this.handleSettingsSaved(message.success, message.message)
                break

            case 'settingsReset':
                this.handleSettingsReset()
                break

            case 'settingsImported':
                this.handleSettingsImported(message.success, message.message)
                break

            default:
                console.warn('Unknown message command:', message.command)
        }
    }

    renderSettings() {
        this.renderSidebar()
        this.renderMainContent()
        this.updateMainHeader()
    }

    renderSidebar() {
        const categoriesList = document.getElementById('categoriesList')
        if (!categoriesList) return

        categoriesList.innerHTML = ''

        this.settingsCategories
            .sort((a, b) => a.order - b.order)
            .forEach(category => {
                const categoryElement = this.createCategoryElement(category)
                categoriesList.appendChild(categoryElement)
            })
    }

    createCategoryElement(category) {
        const element = document.createElement('a')
        element.className = `category-item ${category.id === this.activeCategory ? 'active' : ''}`
        element.href = '#'
        element.innerHTML = `
            <span class="category-icon">${category.icon}</span>
            <span class="category-name">${category.name}</span>
        `

        element.addEventListener('click', (e) => {
            e.preventDefault()
            this.setActiveCategory(category.id)
        })

        return element
    }

    setActiveCategory(categoryId) {
        this.activeCategory = categoryId
        this.renderSettings()
    }

    updateMainHeader() {
        const category = this.settingsCategories.find(c => c.id === this.activeCategory)
        if (!category) return

        const mainTitle = document.getElementById('mainTitle')
        const mainDescription = document.getElementById('mainDescription')

        if (mainTitle) {
            mainTitle.textContent = category.name
        }

        if (mainDescription) {
            mainDescription.textContent = category.description
        }
    }

    renderMainContent() {
        const settingsContent = document.getElementById('settingsContent')
        if (!settingsContent) return

        const category = this.settingsCategories.find(c => c.id === this.activeCategory)
        if (!category) {
            settingsContent.innerHTML = '<p>Category not found</p>'
            return
        }

        const filteredSections = this.filterSections(category.sections)
        
        settingsContent.innerHTML = filteredSections
            .map(section => this.renderSection(section))
            .join('')

        // Attach event listeners to form elements
        this.attachFieldEventListeners()
    }

    filterSections(sections) {
        if (!this.searchQuery) return sections

        return sections.filter(section => {
            const matchesSection = section.name.toLowerCase().includes(this.searchQuery) ||
                                 section.description.toLowerCase().includes(this.searchQuery)

            const hasMatchingFields = section.fields.some(field =>
                field.name.toLowerCase().includes(this.searchQuery) ||
                field.description.toLowerCase().includes(this.searchQuery)
            )

            return matchesSection || hasMatchingFields
        }).map(section => ({
            ...section,
            fields: section.fields.filter(field =>
                !this.searchQuery ||
                field.name.toLowerCase().includes(this.searchQuery) ||
                field.description.toLowerCase().includes(this.searchQuery) ||
                section.name.toLowerCase().includes(this.searchQuery) ||
                section.description.toLowerCase().includes(this.searchQuery)
            )
        }))
    }

    renderSection(section) {
        const isCollapsed = section.collapsed && !this.searchQuery
        
        return `
            <div class="settings-section">
                <div class="section-header" ${section.collapsible ? `onclick="settingsManager.toggleSection('${section.id}')"` : ''}>
                    <div>
                        <h3 class="section-title">${section.name}</h3>
                        <p class="section-description">${section.description}</p>
                    </div>
                    ${section.collapsible ? `
                        <button class="section-toggle" id="toggle-${section.id}">
                            ${isCollapsed ? '▶' : '▼'}
                        </button>
                    ` : ''}
                </div>
                <div class="section-content ${isCollapsed ? 'collapsed' : ''}" id="content-${section.id}">
                    ${section.fields.map(field => this.renderField(field)).join('')}
                </div>
            </div>
        `
    }

    renderField(field) {
        const value = this.getFieldValue(field.id)
        const isDisabled = field.readonly || field.hidden
        const hasError = this.validationErrors.has(field.id)
        const errorMessage = this.validationErrors.get(field.id)

        return `
            <div class="field-group ${field.hidden ? 'hidden' : ''}" data-field-id="${field.id}">
                <div class="field-header">
                    <label class="field-label" for="${field.id}">
                        ${field.name}
                        ${field.validation?.required ? '<span class="field-required">*</span>' : ''}
                    </label>
                    ${field.description ? `
                        <div class="field-tooltip">
                            <span class="tooltip-icon">ℹ</span>
                            <div class="tooltip-content">${field.description}</div>
                        </div>
                    ` : ''}
                </div>
                ${this.renderFieldInput(field, value, isDisabled)}
                ${hasError ? `<div class="field-error">⚠ ${errorMessage}</div>` : ''}
                ${this.renderFieldMessage(field, value)}
            </div>
        `
    }

    renderFieldInput(field, value, isDisabled) {
        const baseAttributes = `
            id="${field.id}" 
            ${isDisabled ? 'disabled' : ''}
            data-field-type="${field.type.type}"
        `

        switch (field.type.type) {
            case 'string':
                if (field.type.multiline) {
                    return `<textarea class="field-input field-textarea" ${baseAttributes} placeholder="${field.type.placeholder || ''}">${value || ''}</textarea>`
                } else if (field.type.format === 'password') {
                    return `<input type="password" class="field-input" ${baseAttributes} value="${value || ''}" placeholder="${field.type.placeholder || ''}">`
                } else {
                    return `<input type="text" class="field-input" ${baseAttributes} value="${value || ''}" placeholder="${field.type.placeholder || ''}">`
                }

            case 'number':
                return `<input type="number" class="field-input" ${baseAttributes} 
                        value="${value || ''}" 
                        min="${field.type.min || ''}" 
                        max="${field.type.max || ''}" 
                        step="${field.type.step || 1}">`

            case 'boolean':
                return `
                    <div class="field-checkbox-container" onclick="settingsManager.toggleCheckbox('${field.id}')">
                        <div class="field-checkbox ${value ? 'checked' : ''}" id="${field.id}-checkbox"></div>
                        <span class="checkbox-label">${field.name}</span>
                        <input type="hidden" ${baseAttributes} value="${value || false}">
                    </div>
                `

            case 'enum':
                return `
                    <select class="field-select" ${baseAttributes}>
                        ${field.options?.map(option => `
                            <option value="${option.value}" ${option.value === value ? 'selected' : ''}>
                                ${option.label}
                            </option>
                        `).join('') || ''}
                    </select>
                `

            case 'array':
                return this.renderArrayField(field, value || [])

            case 'color':
                return `<input type="color" class="field-color" ${baseAttributes} value="${value || '#000000'}">`

            case 'file':
                return `<input type="file" class="field-input" ${baseAttributes}>`

            case 'keyBinding':
                return this.renderKeyBindingField(field, value)

            default:
                return `<input type="text" class="field-input" ${baseAttributes} value="${value || ''}">`
        }
    }

    renderArrayField(field, values) {
        return `
            <div class="field-array" id="${field.id}-array">
                ${values.map((value, index) => `
                    <div class="array-item">
                        <input type="text" class="array-item-input" 
                               value="${value}" 
                               onchange="settingsManager.updateArrayItem('${field.id}', ${index}, this.value)">
                        <button class="array-item-remove" 
                                onclick="settingsManager.removeArrayItem('${field.id}', ${index})">
                            Remove
                        </button>
                    </div>
                `).join('')}
                <button class="array-add-button" onclick="settingsManager.addArrayItem('${field.id}')">
                    Add Item
                </button>
            </div>
        `
    }

    renderKeyBindingField(field, value) {
        const keys = this.parseKeyBinding(value)
        
        return `
            <div class="key-binding-input">
                <div class="key-binding-display">
                    ${keys.map(key => `<span class="key">${key}</span>`).join(' + ')}
                </div>
                <input type="hidden" id="${field.id}" value="${value || ''}">
                <button class="btn btn-small" onclick="settingsManager.recordKeyBinding('${field.id}')">
                    Record Shortcut
                </button>
            </div>
        `
    }

    renderFieldMessage(field, value) {
        // Custom validation messages or info
        if (field.id === 'ai.apiKey' && !value) {
            return '<div class="field-info">ℹ API key is required for AI functionality</div>'
        }
        
        if (field.id === 'performance.memoryLimit' && value < 256) {
            return '<div class="field-warning">⚠ Low memory limit may affect performance</div>'
        }

        return ''
    }

    attachFieldEventListeners() {
        // Text inputs, textareas, and selects
        document.querySelectorAll('.field-input, .field-select').forEach(element => {
            element.addEventListener('change', (e) => {
                const fieldId = e.target.id
                let value = e.target.value

                // Type conversion
                const fieldType = e.target.dataset.fieldType
                if (fieldType === 'number') {
                    value = parseFloat(value) || 0
                } else if (fieldType === 'boolean') {
                    value = e.target.checked
                }

                this.updateSetting(fieldId, value)
            })

            // Real-time validation for text inputs
            if (element.type === 'text' || element.type === 'password') {
                element.addEventListener('input', (e) => {
                    this.validateField(e.target.id, e.target.value)
                })
            }
        })

        // Connection test buttons
        document.querySelectorAll('[data-action="test-connection"]').forEach(button => {
            button.addEventListener('click', (e) => {
                const provider = e.target.dataset.provider
                this.testConnection(provider)
            })
        })
    }

    // Field interaction methods
    toggleCheckbox(fieldId) {
        const checkbox = document.getElementById(`${fieldId}-checkbox`)
        const hiddenInput = document.getElementById(fieldId)
        
        if (checkbox && hiddenInput) {
            const newValue = !checkbox.classList.contains('checked')
            
            checkbox.classList.toggle('checked', newValue)
            hiddenInput.value = newValue
            
            this.updateSetting(fieldId, newValue)
        }
    }

    updateArrayItem(fieldId, index, value) {
        const currentValue = this.getFieldValue(fieldId) || []
        currentValue[index] = value
        this.updateSetting(fieldId, currentValue)
    }

    removeArrayItem(fieldId, index) {
        const currentValue = this.getFieldValue(fieldId) || []
        currentValue.splice(index, 1)
        this.updateSetting(fieldId, currentValue)
        this.rerenderField(fieldId)
    }

    addArrayItem(fieldId) {
        const currentValue = this.getFieldValue(fieldId) || []
        currentValue.push('')
        this.updateSetting(fieldId, currentValue)
        this.rerenderField(fieldId)
    }

    recordKeyBinding(fieldId) {
        // Would implement key binding recording
        alert('Key binding recording not implemented in demo')
    }

    toggleSection(sectionId) {
        const content = document.getElementById(`content-${sectionId}`)
        const toggle = document.getElementById(`toggle-${sectionId}`)
        
        if (content && toggle) {
            const isCollapsed = content.classList.contains('collapsed')
            content.classList.toggle('collapsed')
            toggle.textContent = isCollapsed ? '▼' : '▶'
        }
    }

    // Data management methods
    getFieldValue(fieldId) {
        const parts = fieldId.split('.')
        let value = this.currentSettings
        
        for (const part of parts) {
            value = value?.[part]
        }
        
        return value
    }

    updateSetting(fieldId, value) {
        const parts = fieldId.split('.')
        let current = this.currentSettings
        
        for (let i = 0; i < parts.length - 1; i++) {
            if (!current[parts[i]]) {
                current[parts[i]] = {}
            }
            current = current[parts[i]]
        }
        
        current[parts[parts.length - 1]] = value
        
        this.vscode.postMessage({
            command: 'updateSetting',
            field: fieldId,
            value: value
        })
        
        this.markAsChanged()
        this.validateField(fieldId, value)
    }

    updateSettingValue(field, value) {
        const parts = field.split('.')
        let current = this.currentSettings
        
        for (let i = 0; i < parts.length - 1; i++) {
            if (!current[parts[i]]) {
                current[parts[i]] = {}
            }
            current = current[parts[i]]
        }
        
        current[parts[parts.length - 1]] = value
    }

    validateField(fieldId, value) {
        const field = this.findFieldById(fieldId)
        if (!field) return

        let isValid = true
        let errorMessage = ''

        // Required validation
        if (field.validation?.required && (!value || value === '')) {
            isValid = false
            errorMessage = `${field.name} is required`
        }

        // Pattern validation
        if (isValid && field.validation?.pattern && value) {
            const regex = new RegExp(field.validation.pattern)
            if (!regex.test(value)) {
                isValid = false
                errorMessage = field.validation.errorMessage || `${field.name} format is invalid`
            }
        }

        // Number range validation
        if (isValid && field.type.type === 'number' && value !== '') {
            const numValue = parseFloat(value)
            if (field.validation?.min !== undefined && numValue < field.validation.min) {
                isValid = false
                errorMessage = `${field.name} must be at least ${field.validation.min}`
            }
            if (field.validation?.max !== undefined && numValue > field.validation.max) {
                isValid = false
                errorMessage = `${field.name} must be at most ${field.validation.max}`
            }
        }

        // Update validation state
        if (isValid) {
            this.validationErrors.delete(fieldId)
        } else {
            this.validationErrors.set(fieldId, errorMessage)
        }

        // Update UI
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`)
        if (fieldElement) {
            const input = fieldElement.querySelector('.field-input, .field-select')
            if (input) {
                input.classList.toggle('error', !isValid)
                input.classList.toggle('success', isValid && value)
            }

            // Update error message
            let errorDiv = fieldElement.querySelector('.field-error')
            if (!isValid) {
                if (!errorDiv) {
                    errorDiv = document.createElement('div')
                    errorDiv.className = 'field-error'
                    fieldElement.appendChild(errorDiv)
                }
                errorDiv.innerHTML = `⚠ ${errorMessage}`
            } else if (errorDiv) {
                errorDiv.remove()
            }
        }

        return isValid
    }

    findFieldById(fieldId) {
        for (const category of this.settingsCategories) {
            for (const section of category.sections) {
                const field = section.fields.find(f => f.id === fieldId)
                if (field) return field
            }
        }
        return null
    }

    rerenderField(fieldId) {
        const fieldElement = document.querySelector(`[data-field-id="${fieldId}"]`)
        if (!fieldElement) return

        const field = this.findFieldById(fieldId)
        if (!field) return

        const value = this.getFieldValue(fieldId)
        const newFieldHtml = this.renderField(field)
        
        fieldElement.outerHTML = newFieldHtml
        this.attachFieldEventListeners()
    }

    markAsChanged() {
        this.unsavedChanges = true
        const saveBtn = document.getElementById('saveBtn')
        if (saveBtn) {
            saveBtn.textContent = '✓ Save Settings *'
            saveBtn.classList.add('btn-warning')
        }
    }

    markAsSaved() {
        this.unsavedChanges = false
        const saveBtn = document.getElementById('saveBtn')
        if (saveBtn) {
            saveBtn.textContent = '✓ Save Settings'
            saveBtn.classList.remove('btn-warning')
        }
    }

    // Action methods
    saveSettings() {
        // Validate all fields before saving
        let hasErrors = false
        
        for (const category of this.settingsCategories) {
            for (const section of category.sections) {
                for (const field of section.fields) {
                    const value = this.getFieldValue(field.id)
                    if (!this.validateField(field.id, value)) {
                        hasErrors = true
                    }
                }
            }
        }

        if (hasErrors) {
            alert('Please fix validation errors before saving')
            return
        }

        this.vscode.postMessage({
            command: 'saveSettings',
            settings: this.currentSettings
        })
    }

    showResetDialog() {
        if (confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
            this.vscode.postMessage({
                command: 'resetSettings',
                category: this.activeCategory
            })
        }
    }

    importSettings() {
        const input = document.createElement('input')
        input.type = 'file'
        input.accept = '.json,.yaml,.yml'
        
        input.onchange = (e) => {
            const file = e.target.files[0]
            if (!file) return

            const reader = new FileReader()
            reader.onload = (e) => {
                try {
                    const data = JSON.parse(e.target.result)
                    this.vscode.postMessage({
                        command: 'importSettings',
                        data: data
                    })
                } catch (error) {
                    alert('Invalid settings file: ' + error.message)
                }
            }
            reader.readAsText(file)
        }
        
        input.click()
    }

    exportSettings() {
        this.vscode.postMessage({
            command: 'exportSettings',
            options: {
                format: 'json',
                includeSecrets: false,
                includeUserData: true
            }
        })
    }

    testConnection(provider) {
        const providerSettings = this.currentSettings.ai
        
        this.vscode.postMessage({
            command: 'testConnection',
            provider: provider,
            settings: providerSettings
        })

        // Show loading state
        const button = document.querySelector(`[data-provider="${provider}"]`)
        if (button) {
            button.innerHTML = '<span class="loading-spinner"></span> Testing...'
            button.disabled = true
        }
    }

    filterAndRenderSettings() {
        this.renderMainContent()
    }

    // Utility methods
    parseKeyBinding(keyBinding) {
        if (!keyBinding) return ['None']
        
        return keyBinding.split('+').map(key => key.trim())
    }

    showNotification(message, type = 'info') {
        // Would implement notification system
        console.log(`[${type.toUpperCase()}] ${message}`)
    }

    // Message handlers
    handleValidationResult(result) {
        if (!result.valid) {
            result.errors.forEach(error => {
                this.validationErrors.set(error.field, error.message)
            })
            this.renderMainContent()
        }
    }

    handleConnectionTestResult(result) {
        const button = document.querySelector('[data-action="test-connection"]')
        if (button) {
            button.disabled = false
            
            if (result.success) {
                button.innerHTML = '✓ Connection Successful'
                button.classList.add('btn-success')
                setTimeout(() => {
                    button.innerHTML = 'Test Connection'
                    button.classList.remove('btn-success')
                }, 3000)
            } else {
                button.innerHTML = '✗ Connection Failed'
                button.classList.add('btn-danger')
                setTimeout(() => {
                    button.innerHTML = 'Test Connection'
                    button.classList.remove('btn-danger')
                }, 3000)
            }
        }

        this.showNotification(result.message, result.success ? 'success' : 'error')
    }

    handleSettingsSaved(success, message) {
        if (success) {
            this.markAsSaved()
            this.showNotification('Settings saved successfully', 'success')
        } else {
            this.showNotification(`Failed to save settings: ${message}`, 'error')
        }
    }

    handleSettingsReset() {
        this.requestSettings()
        this.showNotification('Settings reset to defaults', 'info')
    }

    handleSettingsImported(success, message) {
        if (success) {
            this.requestSettings()
            this.showNotification('Settings imported successfully', 'success')
        } else {
            this.showNotification(`Failed to import settings: ${message}`, 'error')
        }
    }
}

// Initialize when DOM is ready
let settingsManager

document.addEventListener('DOMContentLoaded', () => {
    settingsManager = new SettingsManager()
})

// Global functions for inline event handlers
window.settingsManager = {
    toggleSection: (sectionId) => settingsManager?.toggleSection(sectionId),
    toggleCheckbox: (fieldId) => settingsManager?.toggleCheckbox(fieldId),
    updateArrayItem: (fieldId, index, value) => settingsManager?.updateArrayItem(fieldId, index, value),
    removeArrayItem: (fieldId, index) => settingsManager?.removeArrayItem(fieldId, index),
    addArrayItem: (fieldId) => settingsManager?.addArrayItem(fieldId),
    recordKeyBinding: (fieldId) => settingsManager?.recordKeyBinding(fieldId)
}