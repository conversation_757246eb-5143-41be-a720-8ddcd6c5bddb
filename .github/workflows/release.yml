name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        default: 'v1.0.0'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Compile TypeScript
        run: npm run compile

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install VSCE
        run: npm install -g @vscode/vsce

      - name: Compile TypeScript
        run: npm run compile

      - name: Package extension
        run: vsce package

      - name: Upload VSIX artifact
        uses: actions/upload-artifact@v4
        with:
          name: vsix-package
          path: '*.vsix'

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install VSCE
        run: npm install -g @vscode/vsce

      - name: Compile TypeScript
        run: npm run compile

      - name: Package extension
        run: vsce package

      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ steps.get_version.outputs.VERSION }}
          body: |
            ## Changes in this release
            
            See [CHANGELOG.md](https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md) for detailed changes.
            
            ## Installation
            
            1. Download the `.vsix` file from the assets below
            2. Open VS Code
            3. Go to Extensions view (Ctrl+Shift+X)
            4. Click "..." menu and select "Install from VSIX..."
            5. Select the downloaded `.vsix` file
            
            Or install from the marketplace:
            ```
            code --install-extension natural-language-task-transformer
            ```
            
            ## What's New
            
            - Bug fixes and improvements
            - Performance optimizations
            - Enhanced security features
            - Updated documentation
            
            ## Requirements
            
            - VS Code 1.82.0 or later
            - OpenAI or Anthropic API key
            
            ## Support
            
            - [Documentation](https://github.com/${{ github.repository }}/blob/main/docs/README.md)
            - [Issues](https://github.com/${{ github.repository }}/issues)
            - [Discussions](https://github.com/${{ github.repository }}/discussions)
          draft: false
          prerelease: false

      - name: Upload VSIX to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./natural-language-task-transformer-${{ steps.get_version.outputs.VERSION }}.vsix
          asset_name: natural-language-task-transformer-${{ steps.get_version.outputs.VERSION }}.vsix
          asset_content_type: application/octet-stream

  publish:
    needs: release
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install VSCE
        run: npm install -g @vscode/vsce

      - name: Compile TypeScript
        run: npm run compile

      - name: Publish to VS Code Marketplace
        run: vsce publish -p ${{ secrets.VSCE_PAT }}
        env:
          VSCE_PAT: ${{ secrets.VSCE_PAT }}

  notify:
    needs: [publish]
    runs-on: ubuntu-latest
    if: success()
    steps:
      - name: Notify success
        run: |
          echo "✅ Release completed successfully!"
          echo "📦 Extension published to VS Code Marketplace"
          echo "🎉 GitHub release created with assets"

      - name: Update marketplace description
        if: success()
        run: |
          echo "📝 Consider updating marketplace description if needed"
          echo "🔄 Update documentation links if changed"
          echo "📊 Monitor download metrics and user feedback"