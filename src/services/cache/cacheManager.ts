import * as vscode from 'vscode'
import { Logger } from '../../utils/logger'
import { StorageService } from '../storageService'

export interface CacheEntry<T> {
  key: string
  value: T
  timestamp: Date
  ttl: number
  size: number
  hitCount: number
  lastAccessed: Date
  metadata: CacheMetadata
}

export interface CacheMetadata {
  source: string
  version: string
  tags: string[]
  priority: CachePriority
  compression: boolean
  encrypted: boolean
  checksum?: string
}

export interface CacheOptions<T> {
  ttl?: number
  maxSize?: number
  priority?: CachePriority
  tags?: string[]
  compress?: boolean
  encrypt?: boolean
  validator?: (value: T) => boolean
  serializer?: CacheSerializer<T>
}

export interface CacheSerializer<T> {
  serialize(value: T): string | Buffer
  deserialize(data: string | Buffer): T
}

export interface CacheStats {
  size: number
  hitCount: number
  missCount: number
  evictionCount: number
  hitRate: number
  memoryUsage: number
  oldestEntry: Date
  newestEntry: Date
  averageEntrySize: number
}

export type CachePriority = 'low' | 'medium' | 'high' | 'critical'

export type CacheEvictionPolicy = 'lru' | 'lfu' | 'fifo' | 'random' | 'ttl'

export class CacheManager {
  private memoryCache: Map<string, CacheEntry<any>> = new Map()
  private diskCache: Map<string, string> = new Map()
  private stats: CacheStats = {
    size: 0,
    hitCount: 0,
    missCount: 0,
    evictionCount: 0,
    hitRate: 0,
    memoryUsage: 0,
    oldestEntry: new Date(),
    newestEntry: new Date(),
    averageEntrySize: 0
  }

  private readonly maxMemorySize: number = 100 * 1024 * 1024 // 100MB
  private readonly maxDiskSize: number = 500 * 1024 * 1024 // 500MB
  private readonly defaultTtl: number = 60 * 60 * 1000 // 1 hour
  private readonly evictionPolicy: CacheEvictionPolicy = 'lru'
  private readonly cleanupInterval: number = 5 * 60 * 1000 // 5 minutes

  private cleanupTimer: NodeJS.Timeout | null = null

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService
  ) {
    this.startCleanupTimer()
    this.loadDiskCache()
  }

  // Memory cache operations
  public async get<T>(key: string, options?: CacheOptions<T>): Promise<T | undefined> {
    const memoryEntry = this.memoryCache.get(key)
    
    if (memoryEntry) {
      if (this.isExpired(memoryEntry)) {
        this.memoryCache.delete(key)
        this.stats.evictionCount++
      } else {
        memoryEntry.hitCount++
        memoryEntry.lastAccessed = new Date()
        this.stats.hitCount++
        this.updateStats()
        
        if (options?.validator && !options.validator(memoryEntry.value)) {
          this.memoryCache.delete(key)
          this.stats.missCount++
          return undefined
        }
        
        return memoryEntry.value as T
      }
    }

    // Try disk cache
    const diskEntry = await this.getDiskEntry<T>(key, options)
    if (diskEntry) {
      // Promote to memory cache
      this.set(key, diskEntry, options)
      this.stats.hitCount++
      this.updateStats()
      return diskEntry
    }

    this.stats.missCount++
    this.updateStats()
    return undefined
  }

  public async set<T>(key: string, value: T, options?: CacheOptions<T>): Promise<void> {
    const now = new Date()
    const ttl = options?.ttl || this.defaultTtl
    const size = this.calculateSize(value)
    const priority = options?.priority || 'medium'

    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: now,
      ttl,
      size,
      hitCount: 0,
      lastAccessed: now,
      metadata: {
        source: 'cache-manager',
        version: '1.0.0',
        tags: options?.tags || [],
        priority,
        compression: options?.compress || false,
        encrypted: options?.encrypt || false
      }
    }

    // Check if we need to evict entries
    if (this.stats.memoryUsage + size > this.maxMemorySize) {
      await this.evictEntries(size)
    }

    this.memoryCache.set(key, entry)
    this.stats.size++
    this.stats.memoryUsage += size
    this.updateStats()

    // Also persist to disk if priority is high or critical
    if (priority === 'high' || priority === 'critical') {
      await this.setDiskEntry(key, value, options)
    }
  }

  public async delete(key: string): Promise<boolean> {
    const memoryEntry = this.memoryCache.get(key)
    const memoryDeleted = this.memoryCache.delete(key)
    
    if (memoryEntry) {
      this.stats.size--
      this.stats.memoryUsage -= memoryEntry.size
      this.updateStats()
    }

    const diskDeleted = await this.deleteDiskEntry(key)
    
    return memoryDeleted || diskDeleted
  }

  public async clear(options?: { tags?: string[], priority?: CachePriority }): Promise<void> {
    if (!options) {
      this.memoryCache.clear()
      await this.clearDiskCache()
      this.resetStats()
      return
    }

    const entriesToDelete: string[] = []
    
    for (const [key, entry] of this.memoryCache) {
      let shouldDelete = false
      
      if (options.tags && options.tags.some(tag => entry.metadata.tags.includes(tag))) {
        shouldDelete = true
      }
      
      if (options.priority && entry.metadata.priority === options.priority) {
        shouldDelete = true
      }
      
      if (shouldDelete) {
        entriesToDelete.push(key)
      }
    }

    for (const key of entriesToDelete) {
      await this.delete(key)
    }
  }

  public async has(key: string): Promise<boolean> {
    const memoryEntry = this.memoryCache.get(key)
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return true
    }

    return await this.hasDiskEntry(key)
  }

  public async keys(pattern?: string): Promise<string[]> {
    const memoryKeys = Array.from(this.memoryCache.keys())
    const diskKeys = await this.getDiskKeys()
    const allKeys = [...new Set([...memoryKeys, ...diskKeys])]
    
    if (pattern) {
      const regex = new RegExp(pattern)
      return allKeys.filter(key => regex.test(key))
    }
    
    return allKeys
  }

  public getStats(): CacheStats {
    return { ...this.stats }
  }

  // Specialized cache methods
  public async memoize<T>(
    key: string,
    fn: () => Promise<T>,
    options?: CacheOptions<T>
  ): Promise<T> {
    const cached = await this.get<T>(key, options)
    if (cached !== undefined) {
      return cached
    }

    const result = await fn()
    await this.set(key, result, options)
    return result
  }

  public async getOrCompute<T>(
    key: string,
    computeFn: () => Promise<T>,
    options?: CacheOptions<T>
  ): Promise<T> {
    return this.memoize(key, computeFn, options)
  }

  public async setMany<T>(entries: Array<{ key: string; value: T; options?: CacheOptions<T> }>): Promise<void> {
    const promises = entries.map(entry => this.set(entry.key, entry.value, entry.options))
    await Promise.all(promises)
  }

  public async getMany<T>(keys: string[], options?: CacheOptions<T>): Promise<Map<string, T>> {
    const results = new Map<string, T>()
    const promises = keys.map(async key => {
      const value = await this.get<T>(key, options)
      if (value !== undefined) {
        results.set(key, value)
      }
    })
    
    await Promise.all(promises)
    return results
  }

  public async invalidateTag(tag: string): Promise<number> {
    let invalidated = 0
    const toDelete: string[] = []
    
    for (const [key, entry] of this.memoryCache) {
      if (entry.metadata.tags.includes(tag)) {
        toDelete.push(key)
      }
    }
    
    for (const key of toDelete) {
      await this.delete(key)
      invalidated++
    }
    
    return invalidated
  }

  public async refresh<T>(key: string, fn: () => Promise<T>, options?: CacheOptions<T>): Promise<T> {
    await this.delete(key)
    return this.memoize(key, fn, options)
  }

  public async warmup<T>(entries: Array<{ key: string; fn: () => Promise<T>; options?: CacheOptions<T> }>): Promise<void> {
    const promises = entries.map(entry => this.memoize(entry.key, entry.fn, entry.options))
    await Promise.all(promises)
  }

  // Disk cache operations
  private async getDiskEntry<T>(key: string, options?: CacheOptions<T>): Promise<T | undefined> {
    try {
      const data = await this.storageService.get<string>(`cache:${key}`)
      if (!data) return undefined

      const entry = JSON.parse(data) as CacheEntry<T>
      if (this.isExpired(entry)) {
        await this.deleteDiskEntry(key)
        return undefined
      }

      if (options?.validator && !options.validator(entry.value)) {
        await this.deleteDiskEntry(key)
        return undefined
      }

      return entry.value
    } catch (error) {
      this.logger.error(`Error reading disk cache entry ${key}:`, error)
      return undefined
    }
  }

  private async setDiskEntry<T>(key: string, value: T, options?: CacheOptions<T>): Promise<void> {
    try {
      const now = new Date()
      const entry: CacheEntry<T> = {
        key,
        value,
        timestamp: now,
        ttl: options?.ttl || this.defaultTtl,
        size: this.calculateSize(value),
        hitCount: 0,
        lastAccessed: now,
        metadata: {
          source: 'cache-manager',
          version: '1.0.0',
          tags: options?.tags || [],
          priority: options?.priority || 'medium',
          compression: options?.compress || false,
          encrypted: options?.encrypt || false
        }
      }

      await this.storageService.set(`cache:${key}`, JSON.stringify(entry))
    } catch (error) {
      this.logger.error(`Error writing disk cache entry ${key}:`, error)
    }
  }

  private async deleteDiskEntry(key: string): Promise<boolean> {
    try {
      await this.storageService.delete(`cache:${key}`)
      return true
    } catch (error) {
      this.logger.error(`Error deleting disk cache entry ${key}:`, error)
      return false
    }
  }

  private async hasDiskEntry(key: string): Promise<boolean> {
    try {
      const data = await this.storageService.get<string>(`cache:${key}`)
      return data !== undefined
    } catch (error) {
      return false
    }
  }

  private async getDiskKeys(): Promise<string[]> {
    try {
      const keys = await this.storageService.getAllKeys()
      return keys
        .filter(key => key.startsWith('cache:'))
        .map(key => key.substring(6))
    } catch (error) {
      this.logger.error('Error getting disk cache keys:', error)
      return []
    }
  }

  private async clearDiskCache(): Promise<void> {
    try {
      const keys = await this.getDiskKeys()
      const promises = keys.map(key => this.deleteDiskEntry(key))
      await Promise.all(promises)
    } catch (error) {
      this.logger.error('Error clearing disk cache:', error)
    }
  }

  private async loadDiskCache(): Promise<void> {
    try {
      const keys = await this.getDiskKeys()
      this.diskCache = new Map(keys.map(key => [key, `cache:${key}`]))
    } catch (error) {
      this.logger.error('Error loading disk cache:', error)
    }
  }

  // Utility methods
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp.getTime() > entry.ttl
  }

  private calculateSize(value: any): number {
    return Buffer.byteLength(JSON.stringify(value), 'utf8')
  }

  private async evictEntries(requiredSpace: number): Promise<void> {
    const entries = Array.from(this.memoryCache.entries())
    let freedSpace = 0

    // Sort by eviction policy
    entries.sort((a, b) => {
      switch (this.evictionPolicy) {
        case 'lru':
          return a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime()
        case 'lfu':
          return a[1].hitCount - b[1].hitCount
        case 'fifo':
          return a[1].timestamp.getTime() - b[1].timestamp.getTime()
        case 'ttl':
          return (a[1].timestamp.getTime() + a[1].ttl) - (b[1].timestamp.getTime() + b[1].ttl)
        default:
          return Math.random() - 0.5
      }
    })

    for (const [key, entry] of entries) {
      if (freedSpace >= requiredSpace) break
      
      // Don't evict critical priority entries
      if (entry.metadata.priority === 'critical') continue
      
      this.memoryCache.delete(key)
      freedSpace += entry.size
      this.stats.evictionCount++
      this.stats.size--
      this.stats.memoryUsage -= entry.size
    }

    this.updateStats()
  }

  private updateStats(): void {
    const totalAccess = this.stats.hitCount + this.stats.missCount
    this.stats.hitRate = totalAccess > 0 ? this.stats.hitCount / totalAccess : 0
    
    if (this.memoryCache.size > 0) {
      const entries = Array.from(this.memoryCache.values())
      this.stats.averageEntrySize = entries.reduce((sum, entry) => sum + entry.size, 0) / entries.length
      this.stats.oldestEntry = entries.reduce((oldest, entry) => 
        entry.timestamp < oldest ? entry.timestamp : oldest, entries[0].timestamp)
      this.stats.newestEntry = entries.reduce((newest, entry) => 
        entry.timestamp > newest ? entry.timestamp : newest, entries[0].timestamp)
    }
  }

  private resetStats(): void {
    this.stats = {
      size: 0,
      hitCount: 0,
      missCount: 0,
      evictionCount: 0,
      hitRate: 0,
      memoryUsage: 0,
      oldestEntry: new Date(),
      newestEntry: new Date(),
      averageEntrySize: 0
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.cleanupInterval)
  }

  private async cleanup(): Promise<void> {
    const now = Date.now()
    const toDelete: string[] = []

    for (const [key, entry] of this.memoryCache) {
      if (now - entry.timestamp.getTime() > entry.ttl) {
        toDelete.push(key)
      }
    }

    for (const key of toDelete) {
      await this.delete(key)
    }
  }

  public dispose(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }
}

// Specialized cache implementations
export class FileContentCache extends CacheManager {
  public async cacheFileContent(filePath: string, content: string, lastModified: Date): Promise<void> {
    const key = `file:${filePath}`
    await this.set(key, { content, lastModified }, {
      ttl: 30 * 60 * 1000, // 30 minutes
      tags: ['file-content'],
      priority: 'medium'
    })
  }

  public async getFileContent(filePath: string, currentModified: Date): Promise<string | undefined> {
    const key = `file:${filePath}`
    const cached = await this.get<{ content: string; lastModified: Date }>(key)
    
    if (cached && cached.lastModified.getTime() === currentModified.getTime()) {
      return cached.content
    }
    
    return undefined
  }
}

export class AnalysisCache extends CacheManager {
  public async cacheAnalysis(filePath: string, analysisType: string, result: any): Promise<void> {
    const key = `analysis:${analysisType}:${filePath}`
    await this.set(key, result, {
      ttl: 60 * 60 * 1000, // 1 hour
      tags: ['analysis', analysisType],
      priority: 'high'
    })
  }

  public async getAnalysis<T>(filePath: string, analysisType: string): Promise<T | undefined> {
    const key = `analysis:${analysisType}:${filePath}`
    return await this.get<T>(key)
  }
}

export class EmbeddingCache extends CacheManager {
  public async cacheEmbedding(text: string, embedding: number[]): Promise<void> {
    const key = `embedding:${this.hashText(text)}`
    await this.set(key, embedding, {
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      tags: ['embedding'],
      priority: 'high'
    })
  }

  public async getEmbedding(text: string): Promise<number[] | undefined> {
    const key = `embedding:${this.hashText(text)}`
    return await this.get<number[]>(key)
  }

  private hashText(text: string): string {
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return hash.toString(36)
  }
}