# API Documentation

This directory contains the API documentation for the Natural Language Task Transformer extension.

## Core Services

### AI Services
- **[AiService](AiService.md)** - Abstract base class for AI providers
- **[OpenAiService](OpenAiService.md)** - OpenAI GPT integration
- **[ClaudeService](ClaudeService.md)** - Anthropic Claude integration

### Template System
- **[TemplateService](TemplateService.md)** - Handlebars template engine
- **[TemplateSelector](TemplateSelector.md)** - Dynamic template selection

### Indexing and Context
- **[IndexingService](IndexingService.md)** - Codebase indexing and analysis
- **[ContextService](ContextService.md)** - Context analysis and scoring
- **[FileWatcherService](FileWatcherService.md)** - File system monitoring

### Storage and State
- **[StorageService](StorageService.md)** - Data persistence with LanceDB/SQLite
- **[SecretService](SecretService.md)** - Secure API key storage

### Commands
- **[TransformCommand](TransformCommand.md)** - Main task transformation
- **[SuggestCommand](SuggestCommand.md)** - Context-aware suggestions
- **[ConfigureCommand](ConfigureCommand.md)** - Extension configuration

### Utilities
- **[ErrorHandler](ErrorHandler.md)** - Centralized error handling
- **[Logger](Logger.md)** - Logging system
- **[Sanitizer](Sanitizer.md)** - Input sanitization
- **[ApiKeyValidator](ApiKeyValidator.md)** - API key validation
- **[SecurityAudit](SecurityAudit.md)** - Security auditing tools

## Type Definitions

### Core Types
- **[AiTypes](AiTypes.md)** - AI service interfaces and types
- **[TemplateTypes](TemplateTypes.md)** - Template system types
- **[IndexTypes](IndexTypes.md)** - Indexing and analysis types
- **[ConfigTypes](ConfigTypes.md)** - Configuration types

## Usage Examples

### Basic AI Service Usage

```typescript
import { AiServiceFactory } from './services/ai/AiServiceFactory';

const aiService = AiServiceFactory.create('openai', {
  apiKey: 'your-api-key',
  model: 'gpt-4',
  maxTokens: 4000
});

const response = await aiService.generateResponse({
  prompt: 'Transform this natural language to tasks: "Add user authentication"',
  context: contextData
});
```

### Template Processing

```typescript
import { TemplateService } from './services/TemplateService';

const templateService = new TemplateService();
const result = await templateService.processTemplate('default', {
  tasks: taskData,
  context: contextData
});
```

### Codebase Indexing

```typescript
import { IndexingService } from './services/IndexingService';

const indexingService = new IndexingService();
await indexingService.indexWorkspace();
const context = await indexingService.getRelevantContext('authentication');
```

## Error Handling

All services use the centralized error handling system:

```typescript
import { ExtensionError, ErrorCode } from './utils/errorHandler';

try {
  await someOperation();
} catch (error) {
  throw new ExtensionError({
    code: ErrorCode.AI_SERVICE_ERROR,
    message: 'Operation failed',
    originalError: error
  });
}
```

## Security

The extension includes comprehensive security measures:

```typescript
import { InputSanitizer } from './utils/sanitizer';
import { ApiKeyValidator } from './utils/apiKeyValidator';

// Input sanitization
const sanitized = InputSanitizer.sanitizeUserInput(userInput);

// API key validation
const validation = ApiKeyValidator.validateApiKey(apiKey, 'openai');
```

## Configuration

Services are configured through the VSCode settings system:

```typescript
import * as vscode from 'vscode';

const config = vscode.workspace.getConfiguration('taskTransformer');
const aiProvider = config.get<string>('aiProvider');
const maxTokens = config.get<number>('maxTokens');
```

## Building Documentation

To generate the full API documentation:

```bash
npm install -g typedoc typedoc-plugin-markdown
npx typedoc
```

This will generate detailed documentation for all exported classes, interfaces, and functions.

## Contributing

When adding new services or utilities:

1. Add comprehensive JSDoc comments
2. Include usage examples in comments
3. Add the entry point to `typedoc.json`
4. Update this README with the new service

## Support

For questions about the API:

1. Check the generated TypeDoc documentation
2. Review the source code comments
3. Open an issue on GitHub
4. Join the discussions forum