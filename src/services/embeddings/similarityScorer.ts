import { SearchResult } from './lanceService'
import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'

export interface SimilarityScore {
  vectorSimilarity: number
  textualSimilarity: number
  structuralSimilarity: number
  contextualSimilarity: number
  combinedScore: number
  confidence: number
}

export interface ScoringWeights {
  vectorWeight: number
  textualWeight: number
  structuralWeight: number
  contextualWeight: number
  recencyWeight: number
  importanceWeight: number
  popularityWeight: number
}

export interface ScoringContext {
  currentFile?: string
  currentSymbol?: string
  recentFiles?: string[]
  workspaceRoot?: string
  queryIntent?: QueryIntent
  userPreferences?: UserScoringPreferences
}

export interface QueryIntent {
  type: 'search' | 'navigation' | 'completion' | 'refactoring' | 'understanding'
  specificity: 'exact' | 'similar' | 'related' | 'broad'
  scope: 'current_file' | 'current_directory' | 'project' | 'workspace'
}

export interface UserScoringPreferences {
  preferExported: boolean
  preferRecent: boolean
  preferLargeFiles: boolean
  preferDocumented: boolean
  penalizeTests: boolean
  languagePreferences: Record<string, number>
}

export interface SimilarityMatrix {
  items: SimilarityMatrixItem[]
  clusters: SimilarityCluster[]
  recommendations: SimilarityRecommendation[]
}

export interface SimilarityMatrixItem {
  id: string
  vector: number[]
  metadata: any
  scores: Map<string, number>
}

export interface SimilarityCluster {
  id: string
  centroid: number[]
  members: string[]
  coherence: number
  topics: string[]
}

export interface SimilarityRecommendation {
  type: 'similar_function' | 'related_class' | 'usage_pattern' | 'refactor_candidate'
  confidence: number
  description: string
  items: string[]
}

export interface ComparisonResult {
  similarity: SimilarityScore
  reasons: string[]
  recommendations: string[]
  relatedItems: string[]
}

export class SimilarityScorer {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private defaultWeights: ScoringWeights
  private cache: Map<string, SimilarityScore>

  constructor(fileSystem: FileSystemService, db: ConnectionManager) {
    this.fileSystem = fileSystem
    this.db = db
    this.defaultWeights = {
      vectorWeight: 0.4,
      textualWeight: 0.2,
      structuralWeight: 0.2,
      contextualWeight: 0.1,
      recencyWeight: 0.05,
      importanceWeight: 0.03,
      popularityWeight: 0.02
    }
    this.cache = new Map()
  }

  async calculateSimilarity(
    item1: SearchResult,
    item2: SearchResult,
    context?: ScoringContext,
    weights?: Partial<ScoringWeights>
  ): Promise<SimilarityScore> {
    const cacheKey = this.getCacheKey(item1.id, item2.id, context)
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    const scoringWeights = { ...this.defaultWeights, ...weights }
    
    const vectorSimilarity = this.calculateVectorSimilarity(item1.vector, item2.vector)
    const textualSimilarity = await this.calculateTextualSimilarity(item1, item2)
    const structuralSimilarity = await this.calculateStructuralSimilarity(item1, item2)
    const contextualSimilarity = await this.calculateContextualSimilarity(item1, item2, context)
    
    const combinedScore = 
      vectorSimilarity * scoringWeights.vectorWeight +
      textualSimilarity * scoringWeights.textualWeight +
      structuralSimilarity * scoringWeights.structuralWeight +
      contextualSimilarity * scoringWeights.contextualWeight

    const confidence = this.calculateConfidence(
      vectorSimilarity,
      textualSimilarity,
      structuralSimilarity,
      contextualSimilarity
    )

    const score: SimilarityScore = {
      vectorSimilarity,
      textualSimilarity,
      structuralSimilarity,
      contextualSimilarity,
      combinedScore,
      confidence
    }

    this.cache.set(cacheKey, score)
    return score
  }

  private calculateVectorSimilarity(vector1?: number[], vector2?: number[]): number {
    if (!vector1 || !vector2 || vector1.length !== vector2.length) {
      return 0
    }

    // Cosine similarity
    const dotProduct = vector1.reduce((sum, a, i) => sum + a * vector2[i], 0)
    const magnitude1 = Math.sqrt(vector1.reduce((sum, a) => sum + a * a, 0))
    const magnitude2 = Math.sqrt(vector2.reduce((sum, a) => sum + a * a, 0))
    
    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0
    }
    
    return dotProduct / (magnitude1 * magnitude2)
  }

  private async calculateTextualSimilarity(item1: SearchResult, item2: SearchResult): Promise<number> {
    const text1 = item1.text || ''
    const text2 = item2.text || ''
    
    if (!text1 || !text2) {
      return 0
    }

    // Jaccard similarity based on words
    const words1 = new Set(text1.toLowerCase().split(/\W+/).filter(w => w.length > 2))
    const words2 = new Set(text2.toLowerCase().split(/\W+/).filter(w => w.length > 2))
    
    const intersection = new Set([...words1].filter(w => words2.has(w)))
    const union = new Set([...words1, ...words2])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }

  private async calculateStructuralSimilarity(item1: SearchResult, item2: SearchResult): Promise<number> {
    let similarity = 0
    
    // Type similarity
    if (item1.metadata.type === item2.metadata.type) {
      similarity += 0.3
    }
    
    // Language similarity
    if (item1.metadata.language === item2.metadata.language) {
      similarity += 0.2
    }
    
    // Size similarity
    if (item1.metadata.size && item2.metadata.size) {
      const sizeDiff = Math.abs(item1.metadata.size - item2.metadata.size)
      const avgSize = (item1.metadata.size + item2.metadata.size) / 2
      const sizeScore = Math.max(0, 1 - sizeDiff / avgSize)
      similarity += sizeScore * 0.2
    }
    
    // Symbol type similarity
    if (item1.metadata.symbolType && item2.metadata.symbolType) {
      if (item1.metadata.symbolType === item2.metadata.symbolType) {
        similarity += 0.3
      }
    }
    
    return Math.min(similarity, 1)
  }

  private async calculateContextualSimilarity(
    item1: SearchResult,
    item2: SearchResult,
    context?: ScoringContext
  ): Promise<number> {
    let similarity = 0
    
    // Same file bonus
    if (item1.metadata.filePath === item2.metadata.filePath) {
      similarity += 0.4
    }
    
    // Same directory bonus
    if (item1.metadata.filePath && item2.metadata.filePath) {
      const dir1 = this.fileSystem.dirname(item1.metadata.filePath)
      const dir2 = this.fileSystem.dirname(item2.metadata.filePath)
      if (dir1 === dir2) {
        similarity += 0.2
      }
    }
    
    // Current file relevance
    if (context?.currentFile) {
      if (item1.metadata.filePath === context.currentFile) {
        similarity += 0.2
      }
      if (item2.metadata.filePath === context.currentFile) {
        similarity += 0.2
      }
    }
    
    // Recent files bonus
    if (context?.recentFiles) {
      if (context.recentFiles.includes(item1.metadata.filePath)) {
        similarity += 0.1
      }
      if (context.recentFiles.includes(item2.metadata.filePath)) {
        similarity += 0.1
      }
    }
    
    return Math.min(similarity, 1)
  }

  private calculateConfidence(
    vectorSim: number,
    textualSim: number,
    structuralSim: number,
    contextualSim: number
  ): number {
    // Higher confidence when multiple similarity measures agree
    const scores = [vectorSim, textualSim, structuralSim, contextualSim]
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length
    
    // Lower variance means higher confidence
    return Math.max(0, 1 - variance)
  }

  async scoreSearchResults(
    results: SearchResult[],
    context?: ScoringContext,
    weights?: Partial<ScoringWeights>
  ): Promise<SearchResult[]> {
    const scoringWeights = { ...this.defaultWeights, ...weights }
    
    const scoredResults = await Promise.all(
      results.map(async (result) => {
        const adjustedScore = await this.calculateContextualScore(result, context, scoringWeights)
        return {
          ...result,
          score: adjustedScore
        }
      })
    )
    
    return scoredResults.sort((a, b) => b.score - a.score)
  }

  private async calculateContextualScore(
    result: SearchResult,
    context?: ScoringContext,
    weights?: ScoringWeights
  ): Promise<number> {
    let score = result.score
    const w = weights || this.defaultWeights
    
    // Recency boost
    if (context?.recentFiles?.includes(result.metadata.filePath)) {
      score += w.recencyWeight
    }
    
    // Importance boost
    if (result.metadata.importance) {
      score += result.metadata.importance * w.importanceWeight
    }
    
    // Language preference boost
    if (context?.userPreferences?.languagePreferences) {
      const langBoost = context.userPreferences.languagePreferences[result.metadata.language] || 0
      score += langBoost * 0.1
    }
    
    // User preferences
    if (context?.userPreferences) {
      const prefs = context.userPreferences
      
      if (prefs.preferExported && result.metadata.symbolName) {
        score += 0.1
      }
      
      if (prefs.preferDocumented && result.metadata.type === 'documentation') {
        score += 0.1
      }
      
      if (prefs.penalizeTests && result.metadata.filePath.includes('test')) {
        score -= 0.1
      }
    }
    
    return Math.max(0, Math.min(1, score))
  }

  async buildSimilarityMatrix(items: SearchResult[]): Promise<SimilarityMatrix> {
    const matrixItems: SimilarityMatrixItem[] = []
    const scoreMap = new Map<string, Map<string, number>>()
    
    // Calculate pairwise similarities
    for (let i = 0; i < items.length; i++) {
      const item1 = items[i]
      const scores = new Map<string, number>()
      
      for (let j = 0; j < items.length; j++) {
        if (i !== j) {
          const item2 = items[j]
          const similarity = await this.calculateSimilarity(item1, item2)
          scores.set(item2.id, similarity.combinedScore)
        }
      }
      
      matrixItems.push({
        id: item1.id,
        vector: item1.vector || [],
        metadata: item1.metadata,
        scores
      })
      
      scoreMap.set(item1.id, scores)
    }
    
    // Find clusters
    const clusters = this.findClusters(matrixItems)
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(matrixItems, clusters)
    
    return {
      items: matrixItems,
      clusters,
      recommendations
    }
  }

  private findClusters(items: SimilarityMatrixItem[]): SimilarityCluster[] {
    const clusters: SimilarityCluster[] = []
    const visited = new Set<string>()
    const threshold = 0.7
    
    for (const item of items) {
      if (visited.has(item.id)) continue
      
      const cluster: SimilarityCluster = {
        id: `cluster_${clusters.length}`,
        centroid: [...item.vector],
        members: [item.id],
        coherence: 0,
        topics: []
      }
      
      visited.add(item.id)
      
      // Find similar items
      for (const [otherId, similarity] of item.scores) {
        if (!visited.has(otherId) && similarity >= threshold) {
          cluster.members.push(otherId)
          visited.add(otherId)
        }
      }
      
      // Calculate centroid if cluster has multiple members
      if (cluster.members.length > 1) {
        cluster.centroid = this.calculateCentroid(cluster.members, items)
        cluster.coherence = this.calculateCoherence(cluster.members, items)
        cluster.topics = this.extractTopics(cluster.members, items)
      }
      
      clusters.push(cluster)
    }
    
    return clusters
  }

  private calculateCentroid(memberIds: string[], items: SimilarityMatrixItem[]): number[] {
    const members = items.filter(item => memberIds.includes(item.id))
    if (members.length === 0) return []
    
    const dimensions = members[0].vector.length
    const centroid = new Array(dimensions).fill(0)
    
    for (const member of members) {
      for (let i = 0; i < dimensions; i++) {
        centroid[i] += member.vector[i]
      }
    }
    
    for (let i = 0; i < dimensions; i++) {
      centroid[i] /= members.length
    }
    
    return centroid
  }

  private calculateCoherence(memberIds: string[], items: SimilarityMatrixItem[]): number {
    const members = items.filter(item => memberIds.includes(item.id))
    if (members.length < 2) return 1
    
    let totalSimilarity = 0
    let pairCount = 0
    
    for (let i = 0; i < members.length; i++) {
      for (let j = i + 1; j < members.length; j++) {
        const similarity = members[i].scores.get(members[j].id) || 0
        totalSimilarity += similarity
        pairCount++
      }
    }
    
    return pairCount > 0 ? totalSimilarity / pairCount : 0
  }

  private extractTopics(memberIds: string[], items: SimilarityMatrixItem[]): string[] {
    const topics: string[] = []
    const members = items.filter(item => memberIds.includes(item.id))
    
    const typeCount = new Map<string, number>()
    const languageCount = new Map<string, number>()
    
    for (const member of members) {
      const type = member.metadata.type
      const language = member.metadata.language
      
      typeCount.set(type, (typeCount.get(type) || 0) + 1)
      languageCount.set(language, (languageCount.get(language) || 0) + 1)
    }
    
    // Add dominant types and languages as topics
    const dominantType = Array.from(typeCount.entries()).sort((a, b) => b[1] - a[1])[0]
    const dominantLanguage = Array.from(languageCount.entries()).sort((a, b) => b[1] - a[1])[0]
    
    if (dominantType) topics.push(dominantType[0])
    if (dominantLanguage) topics.push(dominantLanguage[0])
    
    return topics
  }

  private generateRecommendations(
    items: SimilarityMatrixItem[],
    clusters: SimilarityCluster[]
  ): SimilarityRecommendation[] {
    const recommendations: SimilarityRecommendation[] = []
    
    // Find similar functions
    const functionClusters = clusters.filter(cluster => 
      cluster.topics.includes('function') && cluster.members.length > 2
    )
    
    for (const cluster of functionClusters) {
      recommendations.push({
        type: 'similar_function',
        confidence: cluster.coherence,
        description: `Found ${cluster.members.length} similar functions`,
        items: cluster.members
      })
    }
    
    // Find related classes
    const classClusters = clusters.filter(cluster => 
      cluster.topics.includes('class') && cluster.members.length > 1
    )
    
    for (const cluster of classClusters) {
      recommendations.push({
        type: 'related_class',
        confidence: cluster.coherence,
        description: `Found ${cluster.members.length} related classes`,
        items: cluster.members
      })
    }
    
    return recommendations
  }

  async compareCodeStructures(filePath1: string, filePath2: string): Promise<ComparisonResult> {
    // This would compare the overall structure of two files
    // For now, return a basic comparison
    return {
      similarity: {
        vectorSimilarity: 0,
        textualSimilarity: 0,
        structuralSimilarity: 0,
        contextualSimilarity: 0,
        combinedScore: 0,
        confidence: 0
      },
      reasons: ['Structure comparison not implemented yet'],
      recommendations: [],
      relatedItems: []
    }
  }

  private getCacheKey(id1: string, id2: string, context?: ScoringContext): string {
    const contextKey = context ? JSON.stringify(context) : ''
    return `${id1}_${id2}_${contextKey}`
  }

  clearCache(): void {
    this.cache.clear()
  }

  getCacheSize(): number {
    return this.cache.size
  }
}