import * as vscode from 'vscode'
import {
  IntentAnalysis,
  DeveloperIntent,
  TaskType,
  ProblemDomain,
  ImplementationStrategy,
  Tradeoff,
  ContentAnalysis,
  StructuralAnalysis
} from '../../../types/selectionAnalyzer.types'

export class IntentAnalyzer {
  async analyzeIntent(
    document: vscode.TextDocument,
    selection: vscode.Selection,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): Promise<IntentAnalysis> {
    const text = document.getText(selection)
    
    const primaryIntent = this.detectPrimaryIntent(text, content, structure)
    const secondaryIntents = this.detectSecondaryIntents(text, content, structure)
    const taskType = this.classifyTaskType(text, structure, primaryIntent)
    const problemDomain = this.identifyProblemDomain(text, content, structure)
    const implementationStrategy = this.analyzeImplementationStrategy(text, structure, taskType)
    
    const confidence = this.calculateConfidence(primaryIntent, taskType, problemDomain)

    return {
      primaryIntent,
      secondaryIntents,
      taskType,
      problemDomain,
      implementationStrategy,
      confidence
    }
  }

  private detectPrimaryIntent(
    text: string,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): DeveloperIntent {
    const intents: Array<DeveloperIntent & { score: number }> = []

    // Analyze function/class names for intent
    structure.elements.forEach(element => {
      if (element.name) {
        const intent = this.inferIntentFromName(element.name, element.type)
        if (intent) {
          intents.push({
            ...intent,
            score: element.type === 'class' ? 3 : 2,
            confidence: 0.7,
            evidence: [`${element.type} named '${element.name}'`]
          })
        }
      }
    })

    // Analyze comments for intent
    content.comments.singleLineComments.concat(content.comments.multiLineComments).forEach(comment => {
      const intent = this.inferIntentFromComment(comment.text)
      if (intent) {
        intents.push({
          ...intent,
          score: 1,
          confidence: 0.6,
          evidence: ['Comment description']
        })
      }
    })

    // Analyze code patterns for intent
    const patternIntent = this.inferIntentFromPatterns(text, structure)
    if (patternIntent) {
      intents.push({
        ...patternIntent,
        score: 2,
        confidence: 0.8,
        evidence: ['Code pattern analysis']
      })
    }

    // Select the highest scoring intent
    const primaryIntent = intents.sort((a, b) => b.score - a.score)[0] || {
      action: 'process',
      target: 'data',
      purpose: 'general computation',
      confidence: 0.3,
      evidence: ['Default intent']
    }

    return {
      action: primaryIntent.action,
      target: primaryIntent.target,
      purpose: primaryIntent.purpose,
      confidence: primaryIntent.confidence,
      evidence: primaryIntent.evidence
    }
  }

  private inferIntentFromName(name: string, type: string): Omit<DeveloperIntent, 'confidence' | 'evidence'> | null {
    const patterns: Array<{
      pattern: RegExp,
      action: string,
      target: string,
      purpose: string
    }> = [
      { pattern: /^(get|fetch|retrieve|find)(\w+)/, action: 'retrieve', target: '$2', purpose: 'data access' },
      { pattern: /^(set|update|modify|change)(\w+)/, action: 'update', target: '$2', purpose: 'data modification' },
      { pattern: /^(create|add|insert|new)(\w+)/, action: 'create', target: '$2', purpose: 'entity creation' },
      { pattern: /^(delete|remove|destroy)(\w+)/, action: 'delete', target: '$2', purpose: 'entity removal' },
      { pattern: /^(validate|check|verify)(\w+)/, action: 'validate', target: '$2', purpose: 'data validation' },
      { pattern: /^(calculate|compute|derive)(\w+)/, action: 'calculate', target: '$2', purpose: 'computation' },
      { pattern: /^(render|display|show)(\w+)/, action: 'render', target: '$2', purpose: 'presentation' },
      { pattern: /^(handle|process|manage)(\w+)/, action: 'process', target: '$2', purpose: 'operation handling' },
      { pattern: /^(init|setup|configure)(\w+)/, action: 'initialize', target: '$2', purpose: 'setup' },
      { pattern: /^(test|assert|expect)(\w+)/, action: 'test', target: '$2', purpose: 'quality assurance' },
      { pattern: /^(parse|extract|transform)(\w+)/, action: 'transform', target: '$2', purpose: 'data transformation' },
      { pattern: /^(send|emit|dispatch)(\w+)/, action: 'communicate', target: '$2', purpose: 'messaging' }
    ]

    for (const { pattern, action, target, purpose } of patterns) {
      const match = name.match(pattern)
      if (match) {
        const extractedTarget = match[2] ? 
          match[2].replace(/([A-Z])/g, ' $1').trim().toLowerCase() : 
          target
        return {
          action,
          target: extractedTarget,
          purpose
        }
      }
    }

    // If no pattern matches, try to infer from the name itself
    if (type === 'class') {
      return {
        action: 'define',
        target: name.toLowerCase(),
        purpose: 'entity definition'
      }
    }

    return null
  }

  private inferIntentFromComment(comment: string): Omit<DeveloperIntent, 'confidence' | 'evidence'> | null {
    const actionWords = {
      'creates?|generates?|builds?|constructs?': 'create',
      'updates?|modifies?|changes?|edits?': 'update',
      'deletes?|removes?|destroys?|clears?': 'delete',
      'gets?|fetches?|retrieves?|finds?|loads?': 'retrieve',
      'validates?|checks?|verifies?|ensures?': 'validate',
      'calculates?|computes?|derives?|determines?': 'calculate',
      'handles?|processes?|manages?|deals with': 'process',
      'renders?|displays?|shows?|presents?': 'render',
      'sends?|emits?|dispatches?|publishes?': 'communicate'
    }

    for (const [pattern, action] of Object.entries(actionWords)) {
      const regex = new RegExp(`\\b(${pattern})\\s+(.+?)(?:\\.|$)`, 'i')
      const match = comment.match(regex)
      if (match) {
        return {
          action,
          target: this.extractTarget(match[2]),
          purpose: `${action} operation`
        }
      }
    }

    return null
  }

  private extractTarget(text: string): string {
    // Remove common words and extract the main noun
    const cleaned = text
      .replace(/\b(the|a|an|this|that|these|those)\b/gi, '')
      .replace(/\s+/g, ' ')
      .trim()
    
    // Extract the first noun-like word
    const words = cleaned.split(' ')
    return words.find(word => /^[a-z]+$/i.test(word)) || 'data'
  }

  private inferIntentFromPatterns(text: string, structure: StructuralAnalysis): Omit<DeveloperIntent, 'confidence' | 'evidence'> | null {
    // Check for CRUD patterns
    if (/INSERT INTO|CREATE TABLE/i.test(text)) {
      return { action: 'create', target: 'database entity', purpose: 'data persistence' }
    }
    if (/SELECT .* FROM/i.test(text)) {
      return { action: 'retrieve', target: 'database records', purpose: 'data access' }
    }
    if (/UPDATE .* SET/i.test(text)) {
      return { action: 'update', target: 'database records', purpose: 'data modification' }
    }
    if (/DELETE FROM/i.test(text)) {
      return { action: 'delete', target: 'database records', purpose: 'data removal' }
    }

    // Check for API patterns
    if (/fetch|axios|http\.get/i.test(text)) {
      return { action: 'retrieve', target: 'remote data', purpose: 'API interaction' }
    }
    if (/\.post\(|POST/i.test(text)) {
      return { action: 'create', target: 'remote resource', purpose: 'API interaction' }
    }

    // Check for UI patterns
    if (/render|jsx|createElement/i.test(text)) {
      return { action: 'render', target: 'UI component', purpose: 'user interface' }
    }
    if (/addEventListener|onClick|onChange/i.test(text)) {
      return { action: 'handle', target: 'user interaction', purpose: 'event handling' }
    }

    // Check for test patterns
    if (/describe\(|it\(|test\(|expect\(/i.test(text)) {
      return { action: 'test', target: 'functionality', purpose: 'quality assurance' }
    }

    return null
  }

  private detectSecondaryIntents(
    text: string,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): DeveloperIntent[] {
    const secondaryIntents: DeveloperIntent[] = []

    // Check for error handling intent
    if (/try\s*{|catch\s*\(|\.catch\(/i.test(text)) {
      secondaryIntents.push({
        action: 'handle',
        target: 'errors',
        purpose: 'error management',
        confidence: 0.8,
        evidence: ['Error handling code present']
      })
    }

    // Check for logging intent
    if (/console\.(log|info|warn|error)|logger\./i.test(text)) {
      secondaryIntents.push({
        action: 'log',
        target: 'execution details',
        purpose: 'debugging and monitoring',
        confidence: 0.7,
        evidence: ['Logging statements found']
      })
    }

    // Check for validation intent
    if (/if\s*\(!|throw\s+new\s+Error|assert/i.test(text)) {
      secondaryIntents.push({
        action: 'validate',
        target: 'input data',
        purpose: 'data integrity',
        confidence: 0.7,
        evidence: ['Validation logic present']
      })
    }

    // Check for optimization intent
    if (/cache|memoize|optimize|performance/i.test(text)) {
      secondaryIntents.push({
        action: 'optimize',
        target: 'performance',
        purpose: 'efficiency improvement',
        confidence: 0.6,
        evidence: ['Optimization patterns detected']
      })
    }

    return secondaryIntents
  }

  private classifyTaskType(
    text: string,
    structure: StructuralAnalysis,
    primaryIntent: DeveloperIntent
  ): TaskType {
    let category: TaskType['category'] = 'feature'
    let subcategory = 'general'
    let complexity: TaskType['complexity'] = 'moderate'
    let estimatedEffort = 30

    // Determine category based on patterns and intent
    if (/bug|fix|issue|problem|error/i.test(text) || 
        structure.patterns.some(p => p.type === 'anti' || p.type === 'code_smell')) {
      category = 'bugfix'
      subcategory = 'error correction'
    } else if (/refactor|improve|clean|optimize/i.test(text) ||
               primaryIntent.action === 'optimize') {
      category = 'refactor'
      subcategory = 'code improvement'
    } else if (/test|spec|assert|expect/i.test(text) ||
               primaryIntent.action === 'test') {
      category = 'test'
      subcategory = 'unit testing'
    } else if (/\/\*\*|@param|@returns|documentation/i.test(text)) {
      category = 'documentation'
      subcategory = 'code documentation'
    } else if (/performance|optimize|cache|speed/i.test(text)) {
      category = 'optimization'
      subcategory = 'performance improvement'
    }

    // Determine complexity
    const loc = structure.complexity.lineCount
    const cyclomaticComplexity = structure.complexity.cyclomaticComplexity
    
    if (loc < 20 && cyclomaticComplexity < 5) {
      complexity = 'simple'
      estimatedEffort = 15
    } else if (loc > 100 || cyclomaticComplexity > 15) {
      complexity = 'complex'
      estimatedEffort = 120
    } else {
      complexity = 'moderate'
      estimatedEffort = 60
    }

    // Adjust effort based on category
    if (category === 'bugfix') {
      estimatedEffort *= 0.8
    } else if (category === 'refactor') {
      estimatedEffort *= 1.2
    } else if (category === 'test') {
      estimatedEffort *= 0.6
    }

    return {
      category,
      subcategory,
      complexity,
      estimatedEffort: Math.round(estimatedEffort)
    }
  }

  private identifyProblemDomain(
    text: string,
    content: ContentAnalysis,
    structure: StructuralAnalysis
  ): ProblemDomain {
    const domains: Record<string, number> = {}
    const technologies: string[] = []
    const patterns: string[] = []
    const constraints: string[] = []

    // Domain detection patterns
    const domainPatterns = [
      { pattern: /\b(http|request|response|api|rest|graphql)\b/i, domain: 'web/api', weight: 2 },
      { pattern: /\b(database|sql|query|table|schema|migration)\b/i, domain: 'database', weight: 2 },
      { pattern: /\b(ui|component|render|view|template|css|style)\b/i, domain: 'frontend', weight: 2 },
      { pattern: /\b(algorithm|sort|search|optimize|calculate)\b/i, domain: 'algorithms', weight: 2 },
      { pattern: /\b(auth|login|permission|role|token|jwt)\b/i, domain: 'authentication', weight: 3 },
      { pattern: /\b(test|spec|mock|stub|assert|expect)\b/i, domain: 'testing', weight: 2 },
      { pattern: /\b(log|monitor|metric|trace|debug)\b/i, domain: 'observability', weight: 1 },
      { pattern: /\b(queue|stream|event|message|publish|subscribe)\b/i, domain: 'messaging', weight: 2 },
      { pattern: /\b(file|fs|path|directory|upload|download)\b/i, domain: 'filesystem', weight: 2 },
      { pattern: /\b(crypto|encrypt|decrypt|hash|signature)\b/i, domain: 'security', weight: 3 }
    ]

    // Calculate domain scores
    domainPatterns.forEach(({ pattern, domain, weight }) => {
      const matches = text.match(pattern) || []
      if (matches.length > 0) {
        domains[domain] = (domains[domain] || 0) + matches.length * weight
      }
    })

    // Identify primary domain
    const primaryDomain = Object.entries(domains)
      .sort(([, a], [, b]) => b - a)[0]?.[0] || 'general'

    // Detect technologies
    const techPatterns = [
      { pattern: /\breact\b/i, tech: 'React' },
      { pattern: /\bangular\b/i, tech: 'Angular' },
      { pattern: /\bvue\b/i, tech: 'Vue' },
      { pattern: /\bnode(js)?\b/i, tech: 'Node.js' },
      { pattern: /\bexpress\b/i, tech: 'Express' },
      { pattern: /\bmongodb?\b/i, tech: 'MongoDB' },
      { pattern: /\bpostgres(ql)?\b/i, tech: 'PostgreSQL' },
      { pattern: /\bmysql\b/i, tech: 'MySQL' },
      { pattern: /\bredis\b/i, tech: 'Redis' },
      { pattern: /\btypescript\b/i, tech: 'TypeScript' },
      { pattern: /\bpython\b/i, tech: 'Python' },
      { pattern: /\bjava\b/i, tech: 'Java' },
      { pattern: /\bspring\b/i, tech: 'Spring' },
      { pattern: /\bdjango\b/i, tech: 'Django' },
      { pattern: /\bflask\b/i, tech: 'Flask' }
    ]

    techPatterns.forEach(({ pattern, tech }) => {
      if (pattern.test(text)) {
        technologies.push(tech)
      }
    })

    // Detect patterns
    if (structure.patterns.length > 0) {
      patterns.push(...structure.patterns.map(p => p.pattern))
    }

    // Common architectural patterns
    if (/\bsingleton\b/i.test(text)) patterns.push('Singleton')
    if (/\bfactory\b/i.test(text)) patterns.push('Factory')
    if (/\bobserver\b/i.test(text)) patterns.push('Observer')
    if (/\bmvc\b/i.test(text)) patterns.push('MVC')
    if (/\brepository\b/i.test(text)) patterns.push('Repository')

    // Identify constraints
    if (/\bperformance|speed|fast|optimize\b/i.test(text)) {
      constraints.push('Performance requirements')
    }
    if (/\bsecurity|secure|auth|encrypt\b/i.test(text)) {
      constraints.push('Security requirements')
    }
    if (/\bscale|scalable|concurrent|parallel\b/i.test(text)) {
      constraints.push('Scalability requirements')
    }
    if (/\blegacy|backward|compatible\b/i.test(text)) {
      constraints.push('Backward compatibility')
    }

    return {
      domain: primaryDomain,
      subdomains: Object.keys(domains).filter(d => d !== primaryDomain),
      technologies,
      patterns,
      constraints
    }
  }

  private analyzeImplementationStrategy(
    text: string,
    structure: StructuralAnalysis,
    taskType: TaskType
  ): ImplementationStrategy {
    const approach = this.determineApproach(taskType, structure)
    const patterns = this.identifyUsefulPatterns(taskType, structure)
    const alternatives = this.generateAlternatives(approach, taskType)
    const tradeoffs = this.analyzeTradeoffs(approach, alternatives)
    const recommendations = this.generateRecommendations(approach, taskType, structure)

    return {
      approach,
      patterns,
      alternatives,
      tradeoffs,
      recommendations
    }
  }

  private determineApproach(taskType: TaskType, structure: StructuralAnalysis): string {
    if (taskType.category === 'bugfix') {
      return 'Targeted fix with minimal code changes'
    } else if (taskType.category === 'refactor') {
      if (structure.complexity.cyclomaticComplexity > 10) {
        return 'Extract methods to reduce complexity'
      }
      return 'Incremental refactoring with test coverage'
    } else if (taskType.category === 'feature') {
      if (taskType.complexity === 'complex') {
        return 'Modular implementation with clear interfaces'
      }
      return 'Direct implementation following existing patterns'
    } else if (taskType.category === 'optimization') {
      return 'Profile-guided optimization with benchmarks'
    } else if (taskType.category === 'test') {
      return 'Comprehensive test suite with edge cases'
    }
    return 'Standard implementation approach'
  }

  private identifyUsefulPatterns(taskType: TaskType, structure: StructuralAnalysis): string[] {
    const patterns: string[] = []

    if (taskType.category === 'feature') {
      if (structure.elements.some(e => e.type === 'class')) {
        patterns.push('Factory Pattern', 'Builder Pattern')
      }
      patterns.push('Strategy Pattern', 'Observer Pattern')
    } else if (taskType.category === 'refactor') {
      patterns.push('Extract Method', 'Replace Conditional with Polymorphism')
      if (structure.complexity.cyclomaticComplexity > 10) {
        patterns.push('Command Pattern', 'Chain of Responsibility')
      }
    } else if (taskType.category === 'test') {
      patterns.push('Arrange-Act-Assert', 'Test Data Builder')
    }

    return patterns
  }

  private generateAlternatives(approach: string, taskType: TaskType): string[] {
    const alternatives: string[] = []

    if (taskType.category === 'feature') {
      alternatives.push(
        'Implement as a separate module',
        'Extend existing functionality',
        'Use third-party library'
      )
    } else if (taskType.category === 'refactor') {
      alternatives.push(
        'Complete rewrite',
        'Gradual migration',
        'Wrapper-based approach'
      )
    } else if (taskType.category === 'optimization') {
      alternatives.push(
        'Caching strategy',
        'Algorithm optimization',
        'Parallel processing'
      )
    }

    return alternatives.filter(alt => alt !== approach)
  }

  private analyzeTradeoffs(approach: string, alternatives: string[]): Tradeoff[] {
    const tradeoffs: Tradeoff[] = []

    // Main approach tradeoff
    tradeoffs.push({
      option: approach,
      pros: ['Follows best practices', 'Maintainable', 'Clear implementation path'],
      cons: ['May require more initial effort', 'Could impact existing code'],
      recommendation: 'Recommended approach for most cases'
    })

    // Alternative tradeoffs
    alternatives.forEach(alt => {
      const tradeoff: Tradeoff = {
        option: alt,
        pros: [],
        cons: [],
        recommendation: ''
      }

      if (alt.includes('separate module')) {
        tradeoff.pros = ['Clean separation', 'Easy to test', 'No impact on existing code']
        tradeoff.cons = ['Additional complexity', 'Potential duplication']
        tradeoff.recommendation = 'Good for large features'
      } else if (alt.includes('third-party')) {
        tradeoff.pros = ['Faster implementation', 'Well-tested solution', 'Community support']
        tradeoff.cons = ['External dependency', 'Less control', 'Potential security risks']
        tradeoff.recommendation = 'Consider for non-core functionality'
      } else if (alt.includes('rewrite')) {
        tradeoff.pros = ['Clean slate', 'Modern approach', 'Remove technical debt']
        tradeoff.cons = ['High risk', 'Time consuming', 'Potential regression']
        tradeoff.recommendation = 'Only for severely problematic code'
      }

      if (tradeoff.pros.length > 0) {
        tradeoffs.push(tradeoff)
      }
    })

    return tradeoffs
  }

  private generateRecommendations(
    approach: string,
    taskType: TaskType,
    structure: StructuralAnalysis
  ): string[] {
    const recommendations: string[] = []

    // General recommendations
    recommendations.push('Write tests before implementing changes')
    recommendations.push('Document design decisions and assumptions')
    
    // Task-specific recommendations
    if (taskType.category === 'feature') {
      recommendations.push('Follow existing code patterns and conventions')
      recommendations.push('Consider edge cases and error scenarios')
    } else if (taskType.category === 'bugfix') {
      recommendations.push('Add regression tests for the bug')
      recommendations.push('Verify fix doesn\'t introduce new issues')
    } else if (taskType.category === 'refactor') {
      recommendations.push('Ensure behavior remains unchanged')
      recommendations.push('Run existing tests frequently')
    } else if (taskType.category === 'optimization') {
      recommendations.push('Benchmark before and after changes')
      recommendations.push('Profile to identify actual bottlenecks')
    }

    // Complexity-based recommendations
    if (structure.complexity.cyclomaticComplexity > 10) {
      recommendations.push('Consider breaking down into smaller functions')
      recommendations.push('Reduce nested conditionals')
    }

    if (structure.complexity.lineCount > 100) {
      recommendations.push('Consider splitting into multiple files')
      recommendations.push('Extract reusable components')
    }

    return recommendations
  }

  private calculateConfidence(
    primaryIntent: DeveloperIntent,
    taskType: TaskType,
    problemDomain: ProblemDomain
  ): number {
    let confidence = 0.5

    // Adjust based on intent confidence
    confidence += primaryIntent.confidence * 0.3

    // Adjust based on task type clarity
    if (taskType.category !== 'feature') {
      confidence += 0.1 // More specific task types have higher confidence
    }

    // Adjust based on domain specificity
    if (problemDomain.domain !== 'general') {
      confidence += 0.1
    }

    // Adjust based on technology detection
    if (problemDomain.technologies.length > 0) {
      confidence += 0.1
    }

    return Math.min(confidence, 0.95)
  }
}