import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'
import { TaskGenerator } from '../services/output/taskGenerator'
import { MarkdownFormatter } from '../services/output/markdownFormatter'
import { TemplateService } from '../services/templateService'
import { TemplateCustomizer } from '../services/templateCustomizer'
import { TemplateSelector } from '../services/templateSelector'
import { PreferenceTracker } from '../services/memory/preferenceTracker'

export interface Template {
  id: string
  name: string
  description: string
  category: TemplateCategory
  version: string
  author: string
  created: Date
  lastModified: Date
  content: string
  variables: TemplateVariable[]
  metadata: TemplateMetadata
  validation: TemplateValidation
  examples: TemplateExample[]
  tags: string[]
  isBuiltIn: boolean
  isActive: boolean
  usageCount: number
  rating: number
  reviews: TemplateReview[]
}

export interface TemplateCategory {
  id: string
  name: string
  description: string
  icon: string
  color: string
  parent?: string
  subcategories: string[]
}

export interface TemplateVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date' | 'enum'
  description: string
  defaultValue?: any
  required: boolean
  validation: VariableValidation
  options?: VariableOption[]
  dependencies: string[]
}

export interface VariableValidation {
  pattern?: string
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  enum?: string[]
  custom?: string
}

export interface VariableOption {
  label: string
  value: any
  description: string
  icon?: string
}

export interface TemplateMetadata {
  language: string
  framework: string
  projectType: string
  complexity: 'simple' | 'moderate' | 'complex'
  estimatedTime: number
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  prerequisites: string[]
  outputs: string[]
  integrations: string[]
  compatibility: CompatibilityInfo
}

export interface CompatibilityInfo {
  vsCodeVersion: string
  nodeVersion: string
  platforms: string[]
  languages: string[]
  frameworks: string[]
}

export interface TemplateValidation {
  schema: any
  rules: ValidationRule[]
  tests: ValidationTest[]
}

export interface ValidationRule {
  field: string
  rule: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

export interface ValidationTest {
  name: string
  description: string
  input: any
  expectedOutput: any
  assertion: string
}

export interface TemplateExample {
  name: string
  description: string
  input: Record<string, any>
  output: string
  explanation: string
  useCase: string
}

export interface TemplateReview {
  id: string
  userId: string
  rating: number
  comment: string
  date: Date
  helpful: number
  reported: boolean
}

export interface TemplateBundle {
  id: string
  name: string
  description: string
  templates: Template[]
  dependencies: string[]
  installation: InstallationInfo
  documentation: string
  changelog: BundleChangelog[]
}

export interface InstallationInfo {
  commands: string[]
  files: string[]
  configuration: Record<string, any>
  postInstall: string[]
}

export interface BundleChangelog {
  version: string
  date: Date
  changes: string[]
  breaking: boolean
}

export interface TemplateOperation {
  type: 'create' | 'update' | 'delete' | 'duplicate' | 'import' | 'export' | 'share'
  templateId: string
  changes?: Partial<Template>
  metadata: OperationMetadata
}

export interface OperationMetadata {
  timestamp: Date
  userId: string
  source: string
  reason: string
  backup: boolean
}

export interface TemplateLibrary {
  local: Template[]
  remote: Template[]
  installed: Template[]
  favorites: Template[]
  recent: Template[]
  categories: TemplateCategory[]
  bundles: TemplateBundle[]
  settings: LibrarySettings
}

export interface LibrarySettings {
  autoUpdate: boolean
  autoBackup: boolean
  syncSettings: boolean
  defaultCategory: string
  sortBy: 'name' | 'date' | 'usage' | 'rating'
  viewMode: 'grid' | 'list' | 'detailed'
  showPreviews: boolean
  enableSharing: boolean
}

export interface TemplateContext {
  workspace: string
  project: string
  language: string
  framework: string
  currentFile: string
  selection: vscode.Selection
  variables: Record<string, any>
  preferences: UserPreferences
}

export interface UserPreferences {
  favoriteTemplates: string[]
  recentTemplates: string[]
  customVariables: Record<string, any>
  defaultValues: Record<string, any>
  notifications: NotificationSettings
  shortcuts: ShortcutSettings
}

export interface NotificationSettings {
  newTemplates: boolean
  updates: boolean
  recommendations: boolean
  errors: boolean
}

export interface ShortcutSettings {
  quickInsert: string
  templateBrowser: string
  createTemplate: string
  editTemplate: string
}

export class TemplateCommands {
  private templateService: TemplateService
  private templateCustomizer: TemplateCustomizer
  private templateSelector: TemplateSelector
  private taskGenerator: TaskGenerator
  private markdownFormatter: MarkdownFormatter
  private preferenceTracker: PreferenceTracker
  
  private templateLibrary: TemplateLibrary
  private recentOperations: TemplateOperation[]
  private disposables: vscode.Disposable[]

  constructor(
    private readonly context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly storageService: StorageService,
    templateService: TemplateService,
    templateCustomizer: TemplateCustomizer,
    templateSelector: TemplateSelector,
    taskGenerator: TaskGenerator,
    markdownFormatter: MarkdownFormatter,
    preferenceTracker: PreferenceTracker
  ) {
    this.templateService = templateService
    this.templateCustomizer = templateCustomizer
    this.templateSelector = templateSelector
    this.taskGenerator = taskGenerator
    this.markdownFormatter = markdownFormatter
    this.preferenceTracker = preferenceTracker
    
    this.templateLibrary = {
      local: [],
      remote: [],
      installed: [],
      favorites: [],
      recent: [],
      categories: [],
      bundles: [],
      settings: {
        autoUpdate: true,
        autoBackup: true,
        syncSettings: true,
        defaultCategory: 'general',
        sortBy: 'name',
        viewMode: 'grid',
        showPreviews: true,
        enableSharing: false
      }
    }
    
    this.recentOperations = []
    this.disposables = []
    
    this.registerCommands()
    this.loadTemplateLibrary()
  }

  private registerCommands(): void {
    // Template Management Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.createTemplate', async () => {
        await this.createTemplate()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.editTemplate', async (templateId?: string) => {
        await this.editTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.deleteTemplate', async (templateId?: string) => {
        await this.deleteTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.duplicateTemplate', async (templateId?: string) => {
        await this.duplicateTemplate(templateId)
      })
    )

    // Template Selection Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.selectTemplate', async () => {
        await this.selectTemplate()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.browseTemplates', async () => {
        await this.browseTemplates()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.searchTemplates', async () => {
        await this.searchTemplates()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.filterTemplates', async () => {
        await this.filterTemplates()
      })
    )

    // Template Application Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.applyTemplate', async (templateId?: string) => {
        await this.applyTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.previewTemplate', async (templateId?: string) => {
        await this.previewTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.customizeTemplate', async (templateId?: string) => {
        await this.customizeTemplate(templateId)
      })
    )

    // Template Import/Export Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.importTemplate', async () => {
        await this.importTemplate()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.exportTemplate', async (templateId?: string) => {
        await this.exportTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.shareTemplate', async (templateId?: string) => {
        await this.shareTemplate(templateId)
      })
    )

    // Template Library Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.updateTemplateLibrary', async () => {
        await this.updateTemplateLibrary()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.installTemplateBundle', async () => {
        await this.installTemplateBundle()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.manageTemplateLibrary', async () => {
        await this.manageTemplateLibrary()
      })
    )

    // Template Validation Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.validateTemplate', async (templateId?: string) => {
        await this.validateTemplate(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.testTemplate', async (templateId?: string) => {
        await this.testTemplate(templateId)
      })
    )

    // Template Favorites Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.addTemplateToFavorites', async (templateId?: string) => {
        await this.addTemplateToFavorites(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.removeTemplateFromFavorites', async (templateId?: string) => {
        await this.removeTemplateFromFavorites(templateId)
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.showFavoriteTemplates', async () => {
        await this.showFavoriteTemplates()
      })
    )

    // Template History Commands
    this.disposables.push(
      vscode.commands.registerCommand('extension.showRecentTemplates', async () => {
        await this.showRecentTemplates()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.showTemplateHistory', async () => {
        await this.showTemplateHistory()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.revertTemplateOperation', async (operationId?: string) => {
        await this.revertTemplateOperation(operationId)
      })
    )
  }

  // Template Management Methods
  async createTemplate(): Promise<void> {
    try {
      const editor = vscode.window.activeTextEditor
      if (!editor) {
        vscode.window.showWarningMessage('No active editor found')
        return
      }

      const selectedText = editor.document.getText(editor.selection)
      if (!selectedText) {
        vscode.window.showWarningMessage('Please select text to create a template')
        return
      }

      const templateData = await this.collectTemplateData(selectedText)
      if (!templateData) return

      const template = await this.buildTemplate(templateData)
      await this.saveTemplate(template)
      
      vscode.window.showInformationMessage(`Template "${template.name}" created successfully`)
      
      this.trackOperation({
        type: 'create',
        templateId: template.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'editor_selection',
          reason: 'user_created',
          backup: false
        }
      })

    } catch (error) {
      this.logger.error('Error creating template:', error)
      vscode.window.showErrorMessage(`Failed to create template: ${error}`)
    }
  }

  async editTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForEditing()

      if (!template) return

      const editedTemplate = await this.openTemplateEditor(template)
      if (!editedTemplate) return

      await this.saveTemplate(editedTemplate)
      
      vscode.window.showInformationMessage(`Template "${editedTemplate.name}" updated successfully`)
      
      this.trackOperation({
        type: 'update',
        templateId: template.id,
        changes: editedTemplate,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'template_editor',
          reason: 'user_modified',
          backup: true
        }
      })

    } catch (error) {
      this.logger.error('Error editing template:', error)
      vscode.window.showErrorMessage(`Failed to edit template: ${error}`)
    }
  }

  async deleteTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForDeletion()

      if (!template) return

      const confirmation = await vscode.window.showWarningMessage(
        `Are you sure you want to delete template "${template.name}"?`,
        { modal: true },
        'Delete',
        'Cancel'
      )

      if (confirmation !== 'Delete') return

      await this.removeTemplate(template.id)
      
      vscode.window.showInformationMessage(`Template "${template.name}" deleted successfully`)
      
      this.trackOperation({
        type: 'delete',
        templateId: template.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'template_manager',
          reason: 'user_deleted',
          backup: true
        }
      })

    } catch (error) {
      this.logger.error('Error deleting template:', error)
      vscode.window.showErrorMessage(`Failed to delete template: ${error}`)
    }
  }

  async duplicateTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForDuplication()

      if (!template) return

      const duplicatedTemplate = await this.cloneTemplate(template)
      await this.saveTemplate(duplicatedTemplate)
      
      vscode.window.showInformationMessage(`Template duplicated as "${duplicatedTemplate.name}"`)
      
      this.trackOperation({
        type: 'duplicate',
        templateId: duplicatedTemplate.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'template_manager',
          reason: 'user_duplicated',
          backup: false
        }
      })

    } catch (error) {
      this.logger.error('Error duplicating template:', error)
      vscode.window.showErrorMessage(`Failed to duplicate template: ${error}`)
    }
  }

  // Template Selection Methods
  async selectTemplate(): Promise<void> {
    try {
      const context = await this.buildTemplateContext()
      const templates = await this.getAvailableTemplates(context)
      
      if (templates.length === 0) {
        vscode.window.showInformationMessage('No templates available')
        return
      }

      const items = templates.map(template => ({
        label: template.name,
        description: template.description,
        detail: `${template.category.name} | ${template.metadata.complexity} | ${template.usageCount} uses`,
        template
      }))

      const selected = await vscode.window.showQuickPick(items, {
        title: 'Select Template',
        placeHolder: 'Choose a template to apply',
        ignoreFocusOut: true
      })

      if (selected) {
        await this.applyTemplate(selected.template.id)
      }

    } catch (error) {
      this.logger.error('Error selecting template:', error)
      vscode.window.showErrorMessage(`Failed to select template: ${error}`)
    }
  }

  async browseTemplates(): Promise<void> {
    try {
      const panel = vscode.window.createWebviewPanel(
        'templateBrowser',
        'Template Browser',
        vscode.ViewColumn.One,
        {
          enableScripts: true,
          retainContextWhenHidden: true
        }
      )

      panel.webview.html = await this.generateTemplateBrowserHTML()
      
      panel.webview.onDidReceiveMessage(
        async (message) => {
          switch (message.command) {
            case 'applyTemplate':
              await this.applyTemplate(message.templateId)
              break
            case 'previewTemplate':
              await this.previewTemplate(message.templateId)
              break
            case 'favoriteTemplate':
              await this.addTemplateToFavorites(message.templateId)
              break
            case 'refreshTemplates':
              await this.updateTemplateLibrary()
              panel.webview.html = await this.generateTemplateBrowserHTML()
              break
          }
        },
        undefined,
        this.disposables
      )

    } catch (error) {
      this.logger.error('Error browsing templates:', error)
      vscode.window.showErrorMessage(`Failed to browse templates: ${error}`)
    }
  }

  async searchTemplates(): Promise<void> {
    try {
      const query = await vscode.window.showInputBox({
        prompt: 'Enter search query for templates',
        placeHolder: 'e.g., "react component", "test", "documentation"'
      })

      if (!query) return

      const results = await this.searchTemplateLibrary(query)
      
      if (results.length === 0) {
        vscode.window.showInformationMessage(`No templates found for "${query}"`)
        return
      }

      const items = results.map(template => ({
        label: template.name,
        description: template.description,
        detail: `${template.category.name} | Match: ${template.relevance}%`,
        template
      }))

      const selected = await vscode.window.showQuickPick(items, {
        title: `Search Results: ${results.length} templates found`,
        placeHolder: 'Select a template to apply',
        ignoreFocusOut: true
      })

      if (selected) {
        await this.applyTemplate(selected.template.id)
      }

    } catch (error) {
      this.logger.error('Error searching templates:', error)
      vscode.window.showErrorMessage(`Failed to search templates: ${error}`)
    }
  }

  async filterTemplates(): Promise<void> {
    try {
      const filterOptions = [
        { label: 'Category', value: 'category' },
        { label: 'Language', value: 'language' },
        { label: 'Framework', value: 'framework' },
        { label: 'Complexity', value: 'complexity' },
        { label: 'Rating', value: 'rating' },
        { label: 'Usage', value: 'usage' }
      ]

      const filterType = await vscode.window.showQuickPick(filterOptions, {
        title: 'Filter Templates',
        placeHolder: 'Select filter type'
      })

      if (!filterType) return

      const filterValues = await this.getFilterValues(filterType.value)
      const filterValue = await vscode.window.showQuickPick(filterValues, {
        title: `Filter by ${filterType.label}`,
        placeHolder: `Select ${filterType.label.toLowerCase()}`
      })

      if (!filterValue) return

      const filteredTemplates = await this.filterTemplateLibrary(filterType.value, filterValue.value)
      
      if (filteredTemplates.length === 0) {
        vscode.window.showInformationMessage(`No templates found for ${filterType.label}: ${filterValue.label}`)
        return
      }

      const items = filteredTemplates.map(template => ({
        label: template.name,
        description: template.description,
        detail: `${template.category.name} | ${template.metadata.complexity}`,
        template
      }))

      const selected = await vscode.window.showQuickPick(items, {
        title: `Filtered Templates: ${filteredTemplates.length} found`,
        placeHolder: 'Select a template to apply',
        ignoreFocusOut: true
      })

      if (selected) {
        await this.applyTemplate(selected.template.id)
      }

    } catch (error) {
      this.logger.error('Error filtering templates:', error)
      vscode.window.showErrorMessage(`Failed to filter templates: ${error}`)
    }
  }

  // Template Application Methods
  async applyTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForApplication()

      if (!template) return

      const context = await this.buildTemplateContext()
      const variables = await this.collectTemplateVariables(template, context)
      
      if (!variables) return

      const result = await this.processTemplate(template, variables, context)
      await this.insertTemplateResult(result)
      
      // Update usage statistics
      await this.updateTemplateUsage(template.id)
      
      vscode.window.showInformationMessage(`Template "${template.name}" applied successfully`)

    } catch (error) {
      this.logger.error('Error applying template:', error)
      vscode.window.showErrorMessage(`Failed to apply template: ${error}`)
    }
  }

  async previewTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForPreview()

      if (!template) return

      const context = await this.buildTemplateContext()
      const variables = await this.collectTemplateVariables(template, context)
      
      if (!variables) return

      const result = await this.processTemplate(template, variables, context)
      
      const document = await vscode.workspace.openTextDocument({
        content: result,
        language: template.metadata.language || 'markdown'
      })

      await vscode.window.showTextDocument(document, {
        preview: true,
        viewColumn: vscode.ViewColumn.Beside
      })

    } catch (error) {
      this.logger.error('Error previewing template:', error)
      vscode.window.showErrorMessage(`Failed to preview template: ${error}`)
    }
  }

  async customizeTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForCustomization()

      if (!template) return

      const customizations = await this.collectCustomizations(template)
      if (!customizations) return

      const customizedTemplate = await this.templateCustomizer.customize(template, customizations)
      await this.saveTemplate(customizedTemplate)
      
      vscode.window.showInformationMessage(`Template "${customizedTemplate.name}" customized successfully`)

    } catch (error) {
      this.logger.error('Error customizing template:', error)
      vscode.window.showErrorMessage(`Failed to customize template: ${error}`)
    }
  }

  // Template Import/Export Methods
  async importTemplate(): Promise<void> {
    try {
      const fileUri = await vscode.window.showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: false,
        filters: {
          'Template Files': ['json', 'yaml', 'yml'],
          'All Files': ['*']
        }
      })

      if (!fileUri || fileUri.length === 0) return

      const templateData = await vscode.workspace.fs.readFile(fileUri[0])
      const template = await this.parseImportedTemplate(templateData.toString())
      
      if (!template) {
        vscode.window.showErrorMessage('Invalid template file')
        return
      }

      await this.saveTemplate(template)
      
      vscode.window.showInformationMessage(`Template "${template.name}" imported successfully`)
      
      this.trackOperation({
        type: 'import',
        templateId: template.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'file_import',
          reason: 'user_imported',
          backup: false
        }
      })

    } catch (error) {
      this.logger.error('Error importing template:', error)
      vscode.window.showErrorMessage(`Failed to import template: ${error}`)
    }
  }

  async exportTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForExport()

      if (!template) return

      const exportFormat = await vscode.window.showQuickPick([
        { label: 'JSON', value: 'json' },
        { label: 'YAML', value: 'yaml' }
      ], {
        title: 'Export Format',
        placeHolder: 'Select export format'
      })

      if (!exportFormat) return

      const fileUri = await vscode.window.showSaveDialog({
        defaultUri: vscode.Uri.file(`${template.name}.${exportFormat.value}`),
        filters: {
          'Template Files': [exportFormat.value],
          'All Files': ['*']
        }
      })

      if (!fileUri) return

      const exportData = await this.formatTemplateForExport(template, exportFormat.value)
      await vscode.workspace.fs.writeFile(fileUri, Buffer.from(exportData))
      
      vscode.window.showInformationMessage(`Template "${template.name}" exported successfully`)
      
      this.trackOperation({
        type: 'export',
        templateId: template.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: 'file_export',
          reason: 'user_exported',
          backup: false
        }
      })

    } catch (error) {
      this.logger.error('Error exporting template:', error)
      vscode.window.showErrorMessage(`Failed to export template: ${error}`)
    }
  }

  async shareTemplate(templateId?: string): Promise<void> {
    try {
      const template = templateId ? 
        await this.getTemplate(templateId) : 
        await this.selectTemplateForSharing()

      if (!template) return

      const shareOptions = [
        { label: 'Copy to Clipboard', value: 'clipboard' },
        { label: 'Save to File', value: 'file' },
        { label: 'Generate Share Link', value: 'link' },
        { label: 'Export as Bundle', value: 'bundle' }
      ]

      const shareMethod = await vscode.window.showQuickPick(shareOptions, {
        title: 'Share Template',
        placeHolder: 'Select sharing method'
      })

      if (!shareMethod) return

      switch (shareMethod.value) {
        case 'clipboard':
          await this.copyTemplateToClipboard(template)
          break
        case 'file':
          await this.exportTemplate(template.id)
          break
        case 'link':
          await this.generateShareLink(template)
          break
        case 'bundle':
          await this.createTemplateBundle([template])
          break
      }

      this.trackOperation({
        type: 'share',
        templateId: template.id,
        metadata: {
          timestamp: new Date(),
          userId: 'current_user',
          source: `share_${shareMethod.value}`,
          reason: 'user_shared',
          backup: false
        }
      })

    } catch (error) {
      this.logger.error('Error sharing template:', error)
      vscode.window.showErrorMessage(`Failed to share template: ${error}`)
    }
  }

  // Template Library Methods
  async updateTemplateLibrary(): Promise<void> {
    try {
      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: 'Updating Template Library...',
        cancellable: false
      }, async (progress) => {
        progress.report({ increment: 0, message: 'Fetching remote templates...' })
        
        const remoteTemplates = await this.fetchRemoteTemplates()
        this.templateLibrary.remote = remoteTemplates
        
        progress.report({ increment: 50, message: 'Updating local templates...' })
        
        await this.syncLocalTemplates()
        
        progress.report({ increment: 100, message: 'Template library updated' })
      })

      vscode.window.showInformationMessage('Template library updated successfully')

    } catch (error) {
      this.logger.error('Error updating template library:', error)
      vscode.window.showErrorMessage(`Failed to update template library: ${error}`)
    }
  }

  async installTemplateBundle(): Promise<void> {
    try {
      const bundleUri = await vscode.window.showOpenDialog({
        canSelectFiles: true,
        canSelectFolders: false,
        canSelectMany: false,
        filters: {
          'Bundle Files': ['bundle', 'zip'],
          'All Files': ['*']
        }
      })

      if (!bundleUri || bundleUri.length === 0) return

      const bundleData = await vscode.workspace.fs.readFile(bundleUri[0])
      const bundle = await this.parseTemplateBundle(bundleData.toString())
      
      if (!bundle) {
        vscode.window.showErrorMessage('Invalid bundle file')
        return
      }

      await this.installBundle(bundle)
      
      vscode.window.showInformationMessage(`Bundle "${bundle.name}" installed successfully`)

    } catch (error) {
      this.logger.error('Error installing template bundle:', error)
      vscode.window.showErrorMessage(`Failed to install template bundle: ${error}`)
    }
  }

  async manageTemplateLibrary(): Promise<void> {
    try {
      const actions = [
        { label: 'View Library Statistics', value: 'stats' },
        { label: 'Clean Up Unused Templates', value: 'cleanup' },
        { label: 'Backup Templates', value: 'backup' },
        { label: 'Restore Templates', value: 'restore' },
        { label: 'Reset Library', value: 'reset' },
        { label: 'Library Settings', value: 'settings' }
      ]

      const action = await vscode.window.showQuickPick(actions, {
        title: 'Manage Template Library',
        placeHolder: 'Select management action'
      })

      if (!action) return

      switch (action.value) {
        case 'stats':
          await this.showLibraryStatistics()
          break
        case 'cleanup':
          await this.cleanupUnusedTemplates()
          break
        case 'backup':
          await this.backupTemplates()
          break
        case 'restore':
          await this.restoreTemplates()
          break
        case 'reset':
          await this.resetLibrary()
          break
        case 'settings':
          await this.configureLibrarySettings()
          break
      }

    } catch (error) {
      this.logger.error('Error managing template library:', error)
      vscode.window.showErrorMessage(`Failed to manage template library: ${error}`)
    }
  }

  // Helper Methods
  private async buildTemplateContext(): Promise<TemplateContext> {
    const editor = vscode.window.activeTextEditor
    const workspace = vscode.workspace.rootPath || ''
    
    return {
      workspace,
      project: workspace.split('/').pop() || '',
      language: editor?.document.languageId || 'unknown',
      framework: await this.detectFramework(),
      currentFile: editor?.document.fileName || '',
      selection: editor?.selection || new vscode.Selection(0, 0, 0, 0),
      variables: {},
      preferences: await this.getUserPreferences()
    }
  }

  private async collectTemplateData(content: string): Promise<Partial<Template> | undefined> {
    const name = await vscode.window.showInputBox({
      prompt: 'Enter template name',
      placeHolder: 'e.g., "React Component", "Test Case"',
      validateInput: (value) => {
        if (!value.trim()) return 'Template name is required'
        if (value.length < 3) return 'Template name must be at least 3 characters'
        return undefined
      }
    })

    if (!name) return undefined

    const description = await vscode.window.showInputBox({
      prompt: 'Enter template description',
      placeHolder: 'Brief description of what this template does'
    })

    const categoryItems = this.templateLibrary.categories.map(cat => ({
      label: cat.name,
      description: cat.description,
      value: cat.id
    }))

    const category = await vscode.window.showQuickPick(categoryItems, {
      title: 'Select Category',
      placeHolder: 'Choose a category for this template'
    })

    const variableNames = this.extractVariableNames(content)
    const variables = await this.defineTemplateVariables(variableNames)

    return {
      name,
      description: description || '',
      category: this.templateLibrary.categories.find(c => c.id === category?.value) || this.templateLibrary.categories[0],
      content,
      variables,
      tags: [],
      isBuiltIn: false,
      isActive: true
    }
  }

  private extractVariableNames(content: string): string[] {
    const variableRegex = /\{\{([^}]+)\}\}/g
    const variables: string[] = []
    let match

    while ((match = variableRegex.exec(content)) !== null) {
      const variableName = match[1].trim()
      if (!variables.includes(variableName)) {
        variables.push(variableName)
      }
    }

    return variables
  }

  private async defineTemplateVariables(variableNames: string[]): Promise<TemplateVariable[]> {
    const variables: TemplateVariable[] = []

    for (const name of variableNames) {
      const type = await vscode.window.showQuickPick([
        { label: 'String', value: 'string' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'Array', value: 'array' },
        { label: 'Date', value: 'date' }
      ], {
        title: `Variable Type: ${name}`,
        placeHolder: 'Select variable type'
      })

      if (!type) continue

      const description = await vscode.window.showInputBox({
        prompt: `Description for variable "${name}"`,
        placeHolder: 'What does this variable represent?'
      })

      const defaultValue = await vscode.window.showInputBox({
        prompt: `Default value for "${name}" (optional)`,
        placeHolder: 'Leave empty for no default'
      })

      variables.push({
        name,
        type: type.value as any,
        description: description || '',
        defaultValue: defaultValue || undefined,
        required: true,
        validation: {},
        options: [],
        dependencies: []
      })
    }

    return variables
  }

  private async buildTemplate(data: Partial<Template>): Promise<Template> {
    const id = `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const now = new Date()

    return {
      id,
      name: data.name || '',
      description: data.description || '',
      category: data.category || this.templateLibrary.categories[0],
      version: '1.0.0',
      author: 'current_user',
      created: now,
      lastModified: now,
      content: data.content || '',
      variables: data.variables || [],
      metadata: {
        language: 'unknown',
        framework: 'unknown',
        projectType: 'unknown',
        complexity: 'moderate',
        estimatedTime: 30,
        skillLevel: 'intermediate',
        prerequisites: [],
        outputs: [],
        integrations: [],
        compatibility: {
          vsCodeVersion: '*',
          nodeVersion: '*',
          platforms: ['*'],
          languages: ['*'],
          frameworks: ['*']
        }
      },
      validation: {
        schema: {},
        rules: [],
        tests: []
      },
      examples: [],
      tags: data.tags || [],
      isBuiltIn: data.isBuiltIn || false,
      isActive: data.isActive || true,
      usageCount: 0,
      rating: 0,
      reviews: []
    }
  }

  private async saveTemplate(template: Template): Promise<void> {
    const existingIndex = this.templateLibrary.local.findIndex(t => t.id === template.id)
    
    if (existingIndex >= 0) {
      this.templateLibrary.local[existingIndex] = template
    } else {
      this.templateLibrary.local.push(template)
    }

    await this.storageService.set(`template_${template.id}`, template)
    await this.saveTemplateLibrary()
  }

  private async getTemplate(templateId: string): Promise<Template | undefined> {
    return this.templateLibrary.local.find(t => t.id === templateId) ||
           this.templateLibrary.remote.find(t => t.id === templateId) ||
           this.templateLibrary.installed.find(t => t.id === templateId)
  }

  private async loadTemplateLibrary(): Promise<void> {
    try {
      const library = await this.storageService.get<TemplateLibrary>('template_library')
      if (library) {
        this.templateLibrary = library
      }
    } catch (error) {
      this.logger.error('Error loading template library:', error)
    }
  }

  private async saveTemplateLibrary(): Promise<void> {
    await this.storageService.set('template_library', this.templateLibrary)
  }

  private trackOperation(operation: TemplateOperation): void {
    this.recentOperations.unshift(operation)
    
    if (this.recentOperations.length > 100) {
      this.recentOperations = this.recentOperations.slice(0, 100)
    }
  }

  private async detectFramework(): Promise<string> {
    const workspace = vscode.workspace.rootPath
    if (!workspace) return 'unknown'

    try {
      const packageJson = await vscode.workspace.fs.readFile(vscode.Uri.file(`${workspace}/package.json`))
      const pkg = JSON.parse(packageJson.toString())
      
      if (pkg.dependencies?.react) return 'react'
      if (pkg.dependencies?.vue) return 'vue'
      if (pkg.dependencies?.angular) return 'angular'
      if (pkg.dependencies?.express) return 'express'
      
      return 'unknown'
    } catch {
      return 'unknown'
    }
  }

  private async getUserPreferences(): Promise<UserPreferences> {
    const prefs = await this.preferenceTracker.getUserPreferences()
    return {
      favoriteTemplates: prefs.favoriteTemplates || [],
      recentTemplates: prefs.recentTemplates || [],
      customVariables: prefs.customVariables || {},
      defaultValues: prefs.defaultValues || {},
      notifications: prefs.notifications || {
        newTemplates: true,
        updates: true,
        recommendations: true,
        errors: true
      },
      shortcuts: prefs.shortcuts || {
        quickInsert: 'Ctrl+Shift+T',
        templateBrowser: 'Ctrl+Shift+B',
        createTemplate: 'Ctrl+Shift+C',
        editTemplate: 'Ctrl+Shift+E'
      }
    }
  }

  // Placeholder methods for complex operations
  private async getAvailableTemplates(context: TemplateContext): Promise<Template[]> {
    return this.templateLibrary.local.filter(t => t.isActive)
  }

  private async searchTemplateLibrary(query: string): Promise<Template[]> {
    return this.templateLibrary.local.filter(t => 
      t.name.toLowerCase().includes(query.toLowerCase()) ||
      t.description.toLowerCase().includes(query.toLowerCase()) ||
      t.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    )
  }

  private async filterTemplateLibrary(filterType: string, filterValue: string): Promise<Template[]> {
    return this.templateLibrary.local.filter(t => {
      switch (filterType) {
        case 'category':
          return t.category.id === filterValue
        case 'language':
          return t.metadata.language === filterValue
        case 'framework':
          return t.metadata.framework === filterValue
        case 'complexity':
          return t.metadata.complexity === filterValue
        default:
          return true
      }
    })
  }

  private async getFilterValues(filterType: string): Promise<{ label: string; value: string }[]> {
    switch (filterType) {
      case 'category':
        return this.templateLibrary.categories.map(c => ({ label: c.name, value: c.id }))
      case 'language':
        return [...new Set(this.templateLibrary.local.map(t => t.metadata.language))]
          .map(lang => ({ label: lang, value: lang }))
      case 'framework':
        return [...new Set(this.templateLibrary.local.map(t => t.metadata.framework))]
          .map(fw => ({ label: fw, value: fw }))
      case 'complexity':
        return [
          { label: 'Simple', value: 'simple' },
          { label: 'Moderate', value: 'moderate' },
          { label: 'Complex', value: 'complex' }
        ]
      default:
        return []
    }
  }

  private async collectTemplateVariables(template: Template, context: TemplateContext): Promise<Record<string, any> | undefined> {
    const variables: Record<string, any> = {}

    for (const variable of template.variables) {
      const value = await vscode.window.showInputBox({
        prompt: `Enter value for "${variable.name}"`,
        placeHolder: variable.description,
        value: variable.defaultValue?.toString() || ''
      })

      if (value === undefined) return undefined
      
      variables[variable.name] = value
    }

    return variables
  }

  private async processTemplate(template: Template, variables: Record<string, any>, context: TemplateContext): Promise<string> {
    return await this.templateService.render(template.content, variables)
  }

  private async insertTemplateResult(result: string): Promise<void> {
    const editor = vscode.window.activeTextEditor
    if (!editor) return

    const selection = editor.selection
    const edit = new vscode.WorkspaceEdit()
    
    if (selection.isEmpty) {
      edit.insert(editor.document.uri, selection.active, result)
    } else {
      edit.replace(editor.document.uri, selection, result)
    }

    await vscode.workspace.applyEdit(edit)
  }

  private async updateTemplateUsage(templateId: string): Promise<void> {
    const template = await this.getTemplate(templateId)
    if (template) {
      template.usageCount++
      template.lastModified = new Date()
      await this.saveTemplate(template)
    }
  }

  private async generateTemplateBrowserHTML(): Promise<string> {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template Browser</title>
    <style>
        body { font-family: var(--vscode-font-family); padding: 20px; }
        .template-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .template-card { border: 1px solid var(--vscode-widget-border); padding: 16px; border-radius: 4px; }
        .template-title { font-size: 16px; font-weight: bold; margin-bottom: 8px; }
        .template-description { color: var(--vscode-foreground); margin-bottom: 12px; }
        .template-actions { display: flex; gap: 8px; }
        .btn { padding: 6px 12px; border: 1px solid var(--vscode-button-border); background: var(--vscode-button-background); color: var(--vscode-button-foreground); border-radius: 2px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Template Browser</h1>
    <div class="template-grid">
        ${this.templateLibrary.local.map(template => `
            <div class="template-card">
                <div class="template-title">${template.name}</div>
                <div class="template-description">${template.description}</div>
                <div class="template-actions">
                    <button class="btn" onclick="applyTemplate('${template.id}')">Apply</button>
                    <button class="btn" onclick="previewTemplate('${template.id}')">Preview</button>
                    <button class="btn" onclick="favoriteTemplate('${template.id}')">Favorite</button>
                </div>
            </div>
        `).join('')}
    </div>
    <script>
        const vscode = acquireVsCodeApi();
        function applyTemplate(id) { vscode.postMessage({ command: 'applyTemplate', templateId: id }); }
        function previewTemplate(id) { vscode.postMessage({ command: 'previewTemplate', templateId: id }); }
        function favoriteTemplate(id) { vscode.postMessage({ command: 'favoriteTemplate', templateId: id }); }
    </script>
</body>
</html>
`
  }

  // Additional placeholder methods for completeness
  private async selectTemplateForEditing(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Edit' })
    return selected?.template
  }

  private async selectTemplateForDeletion(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Delete' })
    return selected?.template
  }

  private async selectTemplateForDuplication(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Duplicate' })
    return selected?.template
  }

  private async selectTemplateForApplication(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Apply' })
    return selected?.template
  }

  private async selectTemplateForPreview(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Preview' })
    return selected?.template
  }

  private async selectTemplateForCustomization(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Customize' })
    return selected?.template
  }

  private async selectTemplateForExport(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Export' })
    return selected?.template
  }

  private async selectTemplateForSharing(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template to Share' })
    return selected?.template
  }

  private async openTemplateEditor(template: Template): Promise<Template | undefined> {
    // This would open a comprehensive template editor
    // For now, we'll simulate with a simple input
    const newContent = await vscode.window.showInputBox({
      prompt: 'Edit template content',
      value: template.content,
      ignoreFocusOut: true
    })

    if (newContent === undefined) return undefined

    return {
      ...template,
      content: newContent,
      lastModified: new Date()
    }
  }

  private async cloneTemplate(template: Template): Promise<Template> {
    const newName = await vscode.window.showInputBox({
      prompt: 'Enter name for duplicated template',
      value: `${template.name} (Copy)`,
      validateInput: (value) => {
        if (!value.trim()) return 'Template name is required'
        return undefined
      }
    })

    return {
      ...template,
      id: `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: newName || `${template.name} (Copy)`,
      created: new Date(),
      lastModified: new Date(),
      usageCount: 0
    }
  }

  private async removeTemplate(templateId: string): Promise<void> {
    this.templateLibrary.local = this.templateLibrary.local.filter(t => t.id !== templateId)
    await this.storageService.remove(`template_${templateId}`)
    await this.saveTemplateLibrary()
  }

  private async collectCustomizations(template: Template): Promise<any> {
    // This would collect customization options
    // For now, return empty object
    return {}
  }

  private async parseImportedTemplate(data: string): Promise<Template | undefined> {
    try {
      return JSON.parse(data)
    } catch {
      return undefined
    }
  }

  private async formatTemplateForExport(template: Template, format: string): Promise<string> {
    if (format === 'yaml') {
      // Would use a YAML library
      return JSON.stringify(template, null, 2)
    }
    return JSON.stringify(template, null, 2)
  }

  private async copyTemplateToClipboard(template: Template): Promise<void> {
    const templateData = JSON.stringify(template, null, 2)
    await vscode.env.clipboard.writeText(templateData)
    vscode.window.showInformationMessage('Template copied to clipboard')
  }

  private async generateShareLink(template: Template): Promise<void> {
    const link = `vscode://template-extension/share/${template.id}`
    await vscode.env.clipboard.writeText(link)
    vscode.window.showInformationMessage('Share link copied to clipboard')
  }

  private async createTemplateBundle(templates: Template[]): Promise<void> {
    const bundle: TemplateBundle = {
      id: `bundle_${Date.now()}`,
      name: 'Custom Bundle',
      description: 'User-created template bundle',
      templates,
      dependencies: [],
      installation: {
        commands: [],
        files: [],
        configuration: {},
        postInstall: []
      },
      documentation: '',
      changelog: []
    }

    const bundleData = JSON.stringify(bundle, null, 2)
    const fileUri = await vscode.window.showSaveDialog({
      defaultUri: vscode.Uri.file(`${bundle.name}.bundle`),
      filters: {
        'Bundle Files': ['bundle'],
        'All Files': ['*']
      }
    })

    if (fileUri) {
      await vscode.workspace.fs.writeFile(fileUri, Buffer.from(bundleData))
      vscode.window.showInformationMessage('Template bundle created successfully')
    }
  }

  private async fetchRemoteTemplates(): Promise<Template[]> {
    // This would fetch from a remote template repository
    return []
  }

  private async syncLocalTemplates(): Promise<void> {
    // This would sync local templates with remote updates
  }

  private async parseTemplateBundle(data: string): Promise<TemplateBundle | undefined> {
    try {
      return JSON.parse(data)
    } catch {
      return undefined
    }
  }

  private async installBundle(bundle: TemplateBundle): Promise<void> {
    for (const template of bundle.templates) {
      await this.saveTemplate(template)
    }
    this.templateLibrary.bundles.push(bundle)
    await this.saveTemplateLibrary()
  }

  private async showLibraryStatistics(): Promise<void> {
    const stats = {
      totalTemplates: this.templateLibrary.local.length,
      categories: this.templateLibrary.categories.length,
      totalUsage: this.templateLibrary.local.reduce((sum, t) => sum + t.usageCount, 0),
      averageRating: this.templateLibrary.local.reduce((sum, t) => sum + t.rating, 0) / this.templateLibrary.local.length
    }

    vscode.window.showInformationMessage(
      `Library Stats: ${stats.totalTemplates} templates, ${stats.categories} categories, ${stats.totalUsage} total uses`
    )
  }

  private async cleanupUnusedTemplates(): Promise<void> {
    const unusedTemplates = this.templateLibrary.local.filter(t => t.usageCount === 0)
    if (unusedTemplates.length === 0) {
      vscode.window.showInformationMessage('No unused templates found')
      return
    }

    const confirmation = await vscode.window.showWarningMessage(
      `Found ${unusedTemplates.length} unused templates. Delete them?`,
      'Delete',
      'Cancel'
    )

    if (confirmation === 'Delete') {
      for (const template of unusedTemplates) {
        await this.removeTemplate(template.id)
      }
      vscode.window.showInformationMessage(`${unusedTemplates.length} unused templates deleted`)
    }
  }

  private async backupTemplates(): Promise<void> {
    const backupData = JSON.stringify(this.templateLibrary, null, 2)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    
    const fileUri = await vscode.window.showSaveDialog({
      defaultUri: vscode.Uri.file(`templates-backup-${timestamp}.json`),
      filters: {
        'JSON Files': ['json'],
        'All Files': ['*']
      }
    })

    if (fileUri) {
      await vscode.workspace.fs.writeFile(fileUri, Buffer.from(backupData))
      vscode.window.showInformationMessage('Templates backed up successfully')
    }
  }

  private async restoreTemplates(): Promise<void> {
    const fileUri = await vscode.window.showOpenDialog({
      canSelectFiles: true,
      canSelectFolders: false,
      canSelectMany: false,
      filters: {
        'JSON Files': ['json'],
        'All Files': ['*']
      }
    })

    if (!fileUri || fileUri.length === 0) return

    try {
      const backupData = await vscode.workspace.fs.readFile(fileUri[0])
      const restoredLibrary = JSON.parse(backupData.toString())
      
      this.templateLibrary = restoredLibrary
      await this.saveTemplateLibrary()
      
      vscode.window.showInformationMessage('Templates restored successfully')
    } catch (error) {
      vscode.window.showErrorMessage(`Failed to restore templates: ${error}`)
    }
  }

  private async resetLibrary(): Promise<void> {
    const confirmation = await vscode.window.showWarningMessage(
      'This will delete all templates and reset the library. Are you sure?',
      { modal: true },
      'Reset',
      'Cancel'
    )

    if (confirmation === 'Reset') {
      this.templateLibrary.local = []
      this.templateLibrary.favorites = []
      this.templateLibrary.recent = []
      await this.saveTemplateLibrary()
      vscode.window.showInformationMessage('Template library reset successfully')
    }
  }

  private async configureLibrarySettings(): Promise<void> {
    const setting = await vscode.window.showQuickPick([
      { label: 'Auto Update', value: 'autoUpdate' },
      { label: 'Auto Backup', value: 'autoBackup' },
      { label: 'Sync Settings', value: 'syncSettings' },
      { label: 'Default Category', value: 'defaultCategory' },
      { label: 'Sort By', value: 'sortBy' },
      { label: 'View Mode', value: 'viewMode' }
    ], {
      title: 'Configure Library Settings',
      placeHolder: 'Select setting to configure'
    })

    if (setting) {
      // Would show setting-specific configuration UI
      vscode.window.showInformationMessage(`Configure ${setting.label} setting`)
    }
  }

  private async addTemplateToFavorites(templateId?: string): Promise<void> {
    const template = templateId ? await this.getTemplate(templateId) : await this.selectTemplateForFavorites()
    if (!template) return

    if (!this.templateLibrary.favorites.find(t => t.id === template.id)) {
      this.templateLibrary.favorites.push(template)
      await this.saveTemplateLibrary()
      vscode.window.showInformationMessage(`"${template.name}" added to favorites`)
    }
  }

  private async removeTemplateFromFavorites(templateId?: string): Promise<void> {
    const template = templateId ? await this.getTemplate(templateId) : await this.selectTemplateFromFavorites()
    if (!template) return

    this.templateLibrary.favorites = this.templateLibrary.favorites.filter(t => t.id !== template.id)
    await this.saveTemplateLibrary()
    vscode.window.showInformationMessage(`"${template.name}" removed from favorites`)
  }

  private async showFavoriteTemplates(): Promise<void> {
    if (this.templateLibrary.favorites.length === 0) {
      vscode.window.showInformationMessage('No favorite templates')
      return
    }

    const items = this.templateLibrary.favorites.map(t => ({
      label: t.name,
      description: t.description,
      template: t
    }))

    const selected = await vscode.window.showQuickPick(items, {
      title: 'Favorite Templates',
      placeHolder: 'Select a favorite template'
    })

    if (selected) {
      await this.applyTemplate(selected.template.id)
    }
  }

  private async showRecentTemplates(): Promise<void> {
    if (this.templateLibrary.recent.length === 0) {
      vscode.window.showInformationMessage('No recent templates')
      return
    }

    const items = this.templateLibrary.recent.map(t => ({
      label: t.name,
      description: t.description,
      template: t
    }))

    const selected = await vscode.window.showQuickPick(items, {
      title: 'Recent Templates',
      placeHolder: 'Select a recent template'
    })

    if (selected) {
      await this.applyTemplate(selected.template.id)
    }
  }

  private async showTemplateHistory(): Promise<void> {
    if (this.recentOperations.length === 0) {
      vscode.window.showInformationMessage('No template operations history')
      return
    }

    const items = this.recentOperations.map(op => ({
      label: `${op.type} - ${op.templateId}`,
      description: op.metadata.reason,
      detail: op.metadata.timestamp.toLocaleString(),
      operation: op
    }))

    const selected = await vscode.window.showQuickPick(items, {
      title: 'Template History',
      placeHolder: 'Select an operation to view details'
    })

    if (selected) {
      vscode.window.showInformationMessage(`Operation: ${selected.operation.type} at ${selected.operation.metadata.timestamp}`)
    }
  }

  private async revertTemplateOperation(operationId?: string): Promise<void> {
    vscode.window.showInformationMessage('Template operation revert not implemented')
  }

  private async selectTemplateForFavorites(): Promise<Template | undefined> {
    const templates = this.templateLibrary.local
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template for Favorites' })
    return selected?.template
  }

  private async selectTemplateFromFavorites(): Promise<Template | undefined> {
    const templates = this.templateLibrary.favorites
    const items = templates.map(t => ({ label: t.name, description: t.description, template: t }))
    const selected = await vscode.window.showQuickPick(items, { title: 'Select Template from Favorites' })
    return selected?.template
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
  }
}