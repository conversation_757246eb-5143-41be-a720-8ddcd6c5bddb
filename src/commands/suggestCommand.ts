import * as vscode from 'vscode'
import { Logger } from '../utils/logger'
import { StorageService } from '../services/storageService'
import { ContextAnalyzer } from '../services/context/contextAnalyzer'
import { SelectionAnalyzer } from '../services/input/selectionAnalyzer'
import { SuggestionProvider } from '../services/input/suggestionProvider'
import { LearningEngine } from '../services/memory/learningEngine'
import { PatternRecognizer } from '../services/memory/patternRecognizer'
import { PreferenceTracker } from '../services/memory/preferenceTracker'
import { SemanticSearch } from '../services/embeddings/semanticSearch'
import { AiService } from '../services/ai/aiService'

export interface SuggestionRequest {
  text: string
  context: SuggestionContext
  options: SuggestionOptions
  source: SuggestionSource
}

export interface SuggestionContext {
  document: vscode.TextDocument
  selection: vscode.Selection
  position: vscode.Position
  language: string
  fileName: string
  workspace: string
  projectContext: ProjectContext
  codeContext: CodeContext
  userContext: UserContext
}

export interface ProjectContext {
  type: string
  framework: string
  dependencies: string[]
  architecture: string
  patterns: string[]
  conventions: string[]
  testingFramework?: string
  buildSystem?: string
}

export interface CodeContext {
  symbols: Symbol[]
  imports: ImportDeclaration[]
  functions: FunctionDeclaration[]
  classes: ClassDeclaration[]
  interfaces: InterfaceDeclaration[]
  variables: VariableDeclaration[]
  comments: Comment[]
  issues: CodeIssue[]
  metrics: CodeMetrics
}

export interface UserContext {
  experience: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  preferences: UserPreferences
  history: InteractionHistory
  patterns: UserPattern[]
  workingStyle: WorkingStyle
}

export interface UserPreferences {
  suggestionTypes: SuggestionType[]
  maxSuggestions: number
  autoApply: boolean
  showExplanations: boolean
  includeExamples: boolean
  prioritizeBy: 'relevance' | 'confidence' | 'impact' | 'effort'
  filterBy: SuggestionFilter[]
  customTemplates: CustomTemplate[]
}

export interface SuggestionOptions {
  maxSuggestions: number
  includeExplanations: boolean
  includeExamples: boolean
  includeConfidence: boolean
  includeImpact: boolean
  includeEffort: boolean
  sortBy: 'relevance' | 'confidence' | 'impact' | 'effort'
  filterBy: SuggestionFilter[]
  useAI: boolean
  useLearning: boolean
  useSemanticSearch: boolean
  timeout: number
}

export interface SuggestionFilter {
  type: 'category' | 'complexity' | 'language' | 'framework' | 'confidence' | 'impact'
  value: string | number
  operator: 'eq' | 'gt' | 'lt' | 'gte' | 'lte' | 'in' | 'not_in'
}

export interface SuggestionSource {
  type: 'command' | 'hover' | 'context_menu' | 'inline' | 'autocomplete'
  trigger: string
  location: vscode.Position
  metadata: Record<string, any>
}

export interface SuggestionType {
  category: 'refactoring' | 'testing' | 'documentation' | 'performance' | 'security' | 'patterns' | 'error_handling' | 'best_practices' | 'code_quality'
  subcategory: string
  enabled: boolean
  priority: number
}

export interface Suggestion {
  id: string
  title: string
  description: string
  category: string
  type: SuggestionType
  confidence: number
  impact: SuggestionImpact
  effort: SuggestionEffort
  implementation: SuggestionImplementation
  examples: SuggestionExample[]
  explanation: string
  benefits: string[]
  risks: string[]
  metadata: SuggestionMetadata
}

export interface SuggestionImpact {
  codeQuality: number
  performance: number
  maintainability: number
  readability: number
  testability: number
  security: number
  overall: number
}

export interface SuggestionEffort {
  timeMinutes: number
  complexity: 'low' | 'medium' | 'high' | 'very_high'
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  dependencies: string[]
  risks: string[]
}

export interface SuggestionImplementation {
  type: 'replace' | 'insert' | 'wrap' | 'extract' | 'inline' | 'move' | 'rename' | 'template'
  code: string
  position: vscode.Position
  range?: vscode.Range
  edits: vscode.TextEdit[]
  commands: vscode.Command[]
  validation: string[]
}

export interface SuggestionExample {
  title: string
  description: string
  before: string
  after: string
  explanation: string
}

export interface SuggestionMetadata {
  source: string
  generated: Date
  lastUpdated: Date
  version: string
  language: string
  framework: string
  tags: string[]
  related: string[]
}

export interface SuggestionResult {
  suggestions: Suggestion[]
  context: SuggestionContext
  metadata: SuggestionResultMetadata
  performance: PerformanceMetrics
}

export interface SuggestionResultMetadata {
  totalSuggestions: number
  filteredSuggestions: number
  processingTime: number
  aiUsed: boolean
  learningUsed: boolean
  semanticSearchUsed: boolean
  confidence: number
  coverage: number
}

export interface PerformanceMetrics {
  analysisTime: number
  aiTime: number
  learningTime: number
  semanticSearchTime: number
  totalTime: number
  memoryUsage: number
  cacheHits: number
  cacheMisses: number
}

export class SuggestCommand {
  private contextAnalyzer: ContextAnalyzer
  private selectionAnalyzer: SelectionAnalyzer
  private suggestionProvider: SuggestionProvider
  private learningEngine: LearningEngine
  private patternRecognizer: PatternRecognizer
  private preferenceTracker: PreferenceTracker
  private semanticSearch: SemanticSearch
  private aiService: AiService
  
  private suggestionHistory: SuggestionResult[]
  private userInteractions: Map<string, InteractionHistory>
  private disposables: vscode.Disposable[]

  constructor (
    private readonly _context: vscode.ExtensionContext,
    private readonly logger: Logger,
    private readonly _storageService: StorageService,
    contextAnalyzer: ContextAnalyzer,
    selectionAnalyzer: SelectionAnalyzer,
    suggestionProvider: SuggestionProvider,
    learningEngine: LearningEngine,
    patternRecognizer: PatternRecognizer,
    preferenceTracker: PreferenceTracker,
    semanticSearch: SemanticSearch,
    aiService: AiService
  ) {
    this.contextAnalyzer = contextAnalyzer
    this.selectionAnalyzer = selectionAnalyzer
    this.suggestionProvider = suggestionProvider
    this.learningEngine = learningEngine
    this.patternRecognizer = patternRecognizer
    this.preferenceTracker = preferenceTracker
    this.semanticSearch = semanticSearch
    this.aiService = aiService
    
    this.suggestionHistory = []
    this.userInteractions = new Map()
    this.disposables = []
    
    this.registerCommands()
  }

  private registerCommands(): void {
    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestImprovements', async () => {
        await this.execute()
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestRefactoring', async () => {
        await this.execute({ filterBy: [{ type: 'category', value: 'refactoring', operator: 'eq' }] })
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestTesting', async () => {
        await this.execute({ filterBy: [{ type: 'category', value: 'testing', operator: 'eq' }] })
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestDocumentation', async () => {
        await this.execute({ filterBy: [{ type: 'category', value: 'documentation', operator: 'eq' }] })
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestPerformance', async () => {
        await this.execute({ filterBy: [{ type: 'category', value: 'performance', operator: 'eq' }] })
      })
    )

    this.disposables.push(
      vscode.commands.registerCommand('extension.suggestSecurity', async () => {
        await this.execute({ filterBy: [{ type: 'category', value: 'security', operator: 'eq' }] })
      })
    )
  }

  async execute (options?: Partial<SuggestionOptions>): Promise<void> {
    try {
      this.logger.info('Suggest command executed')

      const editor = vscode.window.activeTextEditor
      if (!editor) {
        vscode.window.showWarningMessage('No active editor found')
        return
      }

      const selection = editor.selection
      const selectedText = editor.document.getText(selection)

      if (!selectedText.trim()) {
        vscode.window.showWarningMessage('Please select some text to get suggestions')
        return
      }

      await vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: 'Generating intelligent suggestions...',
        cancellable: true
      }, async (progress, token) => {
        progress.report({ increment: 0, message: 'Analyzing context...' })

        const request = await this.buildSuggestionRequest(selectedText, editor, options)
        
        if (token.isCancellationRequested) {
          return
        }

        progress.report({ increment: 20, message: 'Analyzing selection...' })
        
        const result = await this.generateSuggestions(request, progress, token)
        
        if (token.isCancellationRequested) {
          return
        }

        progress.report({ increment: 80, message: 'Presenting suggestions...' })
        
        await this.presentSuggestions(result)
        
        progress.report({ increment: 100, message: 'Complete!' })
        
        this.suggestionHistory.push(result)
        await this.trackInteraction(result)
      })

    } catch (error) {
      this.logger.error('Suggest command failed:', error)
      vscode.window.showErrorMessage(`Failed to generate suggestions: ${error}`)
    }
  }

  private async buildSuggestionRequest(
    text: string,
    editor: vscode.TextEditor,
    options?: Partial<SuggestionOptions>
  ): Promise<SuggestionRequest> {
    const context = await this.buildSuggestionContext(editor)
    const suggestionOptions = await this.buildSuggestionOptions(options)
    const source = this.buildSuggestionSource(editor)

    return {
      text,
      context,
      options: suggestionOptions,
      source
    }
  }

  private async buildSuggestionContext(editor: vscode.TextEditor): Promise<SuggestionContext> {
    const document = editor.document
    const selection = editor.selection
    const position = editor.selection.active
    const language = document.languageId
    const fileName = document.fileName
    const workspace = vscode.workspace.rootPath || ''

    const projectContext = await this.analyzeProjectContext(workspace)
    const codeContext = await this.analyzeCodeContext(document, selection)
    const userContext = await this.analyzeUserContext()

    return {
      document,
      selection,
      position,
      language,
      fileName,
      workspace,
      projectContext,
      codeContext,
      userContext
    }
  }

  private async analyzeProjectContext(workspace: string): Promise<ProjectContext> {
    const context = await this.contextAnalyzer.analyzeWorkspace(workspace)
    
    return {
      type: context.projectType || 'unknown',
      framework: context.framework || 'unknown',
      dependencies: context.dependencies || [],
      architecture: context.architecture || 'unknown',
      patterns: context.patterns || [],
      conventions: context.conventions || [],
      testingFramework: context.testingFramework,
      buildSystem: context.buildSystem
    }
  }

  private async analyzeCodeContext(document: vscode.TextDocument, selection: vscode.Selection): Promise<CodeContext> {
    const analysis = await this.selectionAnalyzer.analyzeSelection(document, selection)
    
    return {
      symbols: analysis.symbols || [],
      imports: analysis.imports || [],
      functions: analysis.functions || [],
      classes: analysis.classes || [],
      interfaces: analysis.interfaces || [],
      variables: analysis.variables || [],
      comments: analysis.comments || [],
      issues: analysis.issues || [],
      metrics: analysis.metrics || {} as CodeMetrics
    }
  }

  private async analyzeUserContext(): Promise<UserContext> {
    const preferences = await this.preferenceTracker.getUserPreferences()
    const patterns = await this.patternRecognizer.getUserPatterns()
    const history = await this.getUserInteractionHistory()
    
    return {
      experience: preferences.experience || 'intermediate',
      preferences: {
        suggestionTypes: preferences.suggestionTypes || [],
        maxSuggestions: preferences.maxSuggestions || 10,
        autoApply: preferences.autoApply || false,
        showExplanations: preferences.showExplanations || true,
        includeExamples: preferences.includeExamples || true,
        prioritizeBy: preferences.prioritizeBy || 'relevance',
        filterBy: preferences.filterBy || [],
        customTemplates: preferences.customTemplates || []
      },
      history,
      patterns,
      workingStyle: preferences.workingStyle || {} as WorkingStyle
    }
  }

  private async buildSuggestionOptions(options?: Partial<SuggestionOptions>): Promise<SuggestionOptions> {
    const config = vscode.workspace.getConfiguration('taskTransformation.suggestions')
    
    return {
      maxSuggestions: options?.maxSuggestions || config.get('maxSuggestions', 10),
      includeExplanations: options?.includeExplanations || config.get('includeExplanations', true),
      includeExamples: options?.includeExamples || config.get('includeExamples', true),
      includeConfidence: options?.includeConfidence || config.get('includeConfidence', true),
      includeImpact: options?.includeImpact || config.get('includeImpact', true),
      includeEffort: options?.includeEffort || config.get('includeEffort', true),
      sortBy: options?.sortBy || config.get('sortBy', 'relevance'),
      filterBy: options?.filterBy || config.get('filterBy', []),
      useAI: options?.useAI || config.get('useAI', true),
      useLearning: options?.useLearning || config.get('useLearning', true),
      useSemanticSearch: options?.useSemanticSearch || config.get('useSemanticSearch', true),
      timeout: options?.timeout || config.get('timeout', 30000)
    }
  }

  private buildSuggestionSource(editor: vscode.TextEditor): SuggestionSource {
    return {
      type: 'command',
      trigger: 'extension.suggestImprovements',
      location: editor.selection.active,
      metadata: {
        hasSelection: !editor.selection.isEmpty,
        selectionSize: editor.document.getText(editor.selection).length,
        language: editor.document.languageId,
        timestamp: new Date().toISOString()
      }
    }
  }

  private async generateSuggestions(
    request: SuggestionRequest,
    progress: vscode.Progress<{ increment?: number; message?: string }>,
    token: vscode.CancellationToken
  ): Promise<SuggestionResult> {
    const startTime = Date.now()
    const suggestions: Suggestion[] = []
    
    try {
      progress.report({ increment: 10, message: 'Generating pattern-based suggestions...' })
      
      const patternSuggestions = await this.generatePatternSuggestions(request)
      suggestions.push(...patternSuggestions)
      
      if (token.isCancellationRequested) {
        throw new Error('Suggestion generation cancelled')
      }
      
      progress.report({ increment: 20, message: 'Generating AI-powered suggestions...' })
      
      if (request.options.useAI) {
        const aiSuggestions = await this.generateAISuggestions(request)
        suggestions.push(...aiSuggestions)
      }
      
      if (token.isCancellationRequested) {
        throw new Error('Suggestion generation cancelled')
      }
      
      progress.report({ increment: 20, message: 'Applying semantic search...' })
      
      if (request.options.useSemanticSearch) {
        const semanticSuggestions = await this.generateSemanticSuggestions(request)
        suggestions.push(...semanticSuggestions)
      }
      
      progress.report({ increment: 10, message: 'Applying learning engine...' })
      
      if (request.options.useLearning) {
        const learningEnhancedSuggestions = await this.enhanceSuggestionsWithLearning(suggestions, request)
        suggestions.splice(0, suggestions.length, ...learningEnhancedSuggestions)
      }
      
      progress.report({ increment: 10, message: 'Filtering and sorting...' })
      
      const filteredSuggestions = this.filterAndSortSuggestions(suggestions, request.options)
      
      const processingTime = Date.now() - startTime
      
      return {
        suggestions: filteredSuggestions,
        context: request.context,
        metadata: {
          totalSuggestions: suggestions.length,
          filteredSuggestions: filteredSuggestions.length,
          processingTime,
          aiUsed: request.options.useAI,
          learningUsed: request.options.useLearning,
          semanticSearchUsed: request.options.useSemanticSearch,
          confidence: this.calculateOverallConfidence(filteredSuggestions),
          coverage: this.calculateCoverage(filteredSuggestions)
        },
        performance: {
          analysisTime: processingTime * 0.3,
          aiTime: processingTime * 0.4,
          learningTime: processingTime * 0.2,
          semanticSearchTime: processingTime * 0.1,
          totalTime: processingTime,
          memoryUsage: process.memoryUsage().heapUsed,
          cacheHits: 0,
          cacheMisses: 0
        }
      }
      
    } catch (error) {
      this.logger.error('Error generating suggestions:', error)
      
      return {
        suggestions: [],
        context: request.context,
        metadata: {
          totalSuggestions: 0,
          filteredSuggestions: 0,
          processingTime: Date.now() - startTime,
          aiUsed: false,
          learningUsed: false,
          semanticSearchUsed: false,
          confidence: 0,
          coverage: 0
        },
        performance: {
          analysisTime: 0,
          aiTime: 0,
          learningTime: 0,
          semanticSearchTime: 0,
          totalTime: Date.now() - startTime,
          memoryUsage: process.memoryUsage().heapUsed,
          cacheHits: 0,
          cacheMisses: 0
        }
      }
    }
  }

  private async generatePatternSuggestions(request: SuggestionRequest): Promise<Suggestion[]> {
    const patterns = await this.patternRecognizer.analyzeCode(request.text, request.context.language)
    const suggestions: Suggestion[] = []
    
    for (const pattern of patterns) {
      if (pattern.type === 'anti_pattern' || pattern.improvement) {
        suggestions.push({
          id: `pattern_${pattern.id}`,
          title: pattern.suggestion || `Improve ${pattern.name}`,
          description: pattern.description || `Consider refactoring this ${pattern.name} pattern`,
          category: 'patterns',
          type: {
            category: 'patterns',
            subcategory: pattern.type,
            enabled: true,
            priority: pattern.priority || 5
          },
          confidence: pattern.confidence || 0.7,
          impact: {
            codeQuality: pattern.impact?.codeQuality || 0.8,
            performance: pattern.impact?.performance || 0.6,
            maintainability: pattern.impact?.maintainability || 0.9,
            readability: pattern.impact?.readability || 0.7,
            testability: pattern.impact?.testability || 0.6,
            security: pattern.impact?.security || 0.5,
            overall: pattern.impact?.overall || 0.7
          },
          effort: {
            timeMinutes: pattern.effort?.timeMinutes || 30,
            complexity: pattern.effort?.complexity || 'medium',
            skillLevel: pattern.effort?.skillLevel || 'intermediate',
            dependencies: pattern.effort?.dependencies || [],
            risks: pattern.effort?.risks || []
          },
          implementation: {
            type: 'replace',
            code: pattern.suggestedCode || '',
            position: request.context.position,
            range: request.context.selection,
            edits: [],
            commands: [],
            validation: []
          },
          examples: pattern.examples || [],
          explanation: pattern.explanation || '',
          benefits: pattern.benefits || [],
          risks: pattern.risks || [],
          metadata: {
            source: 'pattern_recognizer',
            generated: new Date(),
            lastUpdated: new Date(),
            version: '1.0.0',
            language: request.context.language,
            framework: request.context.projectContext.framework,
            tags: pattern.tags || [],
            related: pattern.related || []
          }
        })
      }
    }
    
    return suggestions
  }

  private async generateAISuggestions(request: SuggestionRequest): Promise<Suggestion[]> {
    try {
      const prompt = this.buildAIPrompt(request)
      const response = await this.aiService.generateSuggestions(prompt, {
        maxTokens: 2000,
        temperature: 0.7,
        model: 'gpt-4'
      })
      
      return this.parseAISuggestions(response, request)
    } catch (error) {
      this.logger.error('Error generating AI suggestions:', error)
      return []
    }
  }

  private async generateSemanticSuggestions(request: SuggestionRequest): Promise<Suggestion[]> {
    try {
      const similarCode = await this.semanticSearch.findSimilarCode(request.text, {
        language: request.context.language,
        limit: 5,
        threshold: 0.7
      })
      
      const suggestions: Suggestion[] = []
      
      for (const similar of similarCode) {
        if (similar.improvements && similar.improvements.length > 0) {
          suggestions.push({
            id: `semantic_${similar.id}`,
            title: `Similar code improvement: ${similar.improvements[0].title}`,
            description: similar.improvements[0].description,
            category: 'semantic',
            type: {
              category: 'best_practices',
              subcategory: 'semantic_similarity',
              enabled: true,
              priority: 7
            },
            confidence: similar.similarity,
            impact: similar.improvements[0].impact || {} as SuggestionImpact,
            effort: similar.improvements[0].effort || {} as SuggestionEffort,
            implementation: similar.improvements[0].implementation || {} as SuggestionImplementation,
            examples: similar.improvements[0].examples || [],
            explanation: similar.improvements[0].explanation || '',
            benefits: similar.improvements[0].benefits || [],
            risks: similar.improvements[0].risks || [],
            metadata: {
              source: 'semantic_search',
              generated: new Date(),
              lastUpdated: new Date(),
              version: '1.0.0',
              language: request.context.language,
              framework: request.context.projectContext.framework,
              tags: ['semantic', 'similarity'],
              related: [similar.id]
            }
          })
        }
      }
      
      return suggestions
    } catch (error) {
      this.logger.error('Error generating semantic suggestions:', error)
      return []
    }
  }

  private async enhanceSuggestionsWithLearning(suggestions: Suggestion[], request: SuggestionRequest): Promise<Suggestion[]> {
    return await this.learningEngine.enhanceSuggestions(suggestions, request.context.userContext)
  }

  private filterAndSortSuggestions(suggestions: Suggestion[], options: SuggestionOptions): Suggestion[] {
    let filtered = suggestions
    
    // Apply filters
    for (const filter of options.filterBy) {
      filtered = filtered.filter(suggestion => {
        const value = this.getSuggestionValue(suggestion, filter.type)
        return this.matchesFilter(value, filter.value, filter.operator)
      })
    }
    
    // Sort suggestions
    filtered.sort((a, b) => {
      switch (options.sortBy) {
        case 'confidence':
          return b.confidence - a.confidence
        case 'impact':
          return b.impact.overall - a.impact.overall
        case 'effort':
          return a.effort.timeMinutes - b.effort.timeMinutes
        case 'relevance':
        default:
          return (b.confidence * b.impact.overall) - (a.confidence * a.impact.overall)
      }
    })
    
    return filtered.slice(0, options.maxSuggestions)
  }

  private async presentSuggestions(result: SuggestionResult): Promise<void> {
    if (result.suggestions.length === 0) {
      vscode.window.showInformationMessage('No suggestions found for the selected code.')
      return
    }
    
    const items = result.suggestions.map(suggestion => ({
      label: suggestion.title,
      description: suggestion.description,
      detail: `Confidence: ${(suggestion.confidence * 100).toFixed(0)}% | Impact: ${(suggestion.impact.overall * 100).toFixed(0)}% | Effort: ${suggestion.effort.timeMinutes}min`,
      suggestion
    }))
    
    const selected = await vscode.window.showQuickPick(items, {
      title: 'AI-Powered Code Suggestions',
      placeHolder: 'Select a suggestion to view details or apply',
      ignoreFocusOut: true
    })
    
    if (selected) {
      await this.handleSuggestionSelection(selected.suggestion)
    }
  }

  private async handleSuggestionSelection(suggestion: Suggestion): Promise<void> {
    const action = await vscode.window.showInformationMessage(
      `${suggestion.title}\n\n${suggestion.description}`,
      {
        modal: true,
        detail: suggestion.explanation
      },
      'Apply',
      'View Details',
      'Show Examples',
      'Cancel'
    )
    
    switch (action) {
      case 'Apply':
        await this.applySuggestion(suggestion)
        break
      case 'View Details':
        await this.showSuggestionDetails(suggestion)
        break
      case 'Show Examples':
        await this.showSuggestionExamples(suggestion)
        break
    }
  }

  private async applySuggestion(suggestion: Suggestion): Promise<void> {
    try {
      const editor = vscode.window.activeTextEditor
      if (!editor) return
      
      const edit = new vscode.WorkspaceEdit()
      
      if (suggestion.implementation.edits.length > 0) {
        edit.set(editor.document.uri, suggestion.implementation.edits)
      } else if (suggestion.implementation.code) {
        const range = suggestion.implementation.range || editor.selection
        edit.replace(editor.document.uri, range, suggestion.implementation.code)
      }
      
      await vscode.workspace.applyEdit(edit)
      
      if (suggestion.implementation.commands.length > 0) {
        for (const command of suggestion.implementation.commands) {
          await vscode.commands.executeCommand(command.command, ...command.arguments || [])
        }
      }
      
      await this.trackSuggestionApplication(suggestion)
      
      vscode.window.showInformationMessage(`Applied suggestion: ${suggestion.title}`)
    } catch (error) {
      this.logger.error('Error applying suggestion:', error)
      vscode.window.showErrorMessage(`Failed to apply suggestion: ${error}`)
    }
  }

  private async showSuggestionDetails(suggestion: Suggestion): Promise<void> {
    const details = `
# ${suggestion.title}

## Description
${suggestion.description}

## Benefits
${suggestion.benefits.map(benefit => `- ${benefit}`).join('\n')}

## Risks
${suggestion.risks.map(risk => `- ${risk}`).join('\n')}

## Effort
- Time: ${suggestion.effort.timeMinutes} minutes
- Complexity: ${suggestion.effort.complexity}
- Skill Level: ${suggestion.effort.skillLevel}

## Impact
- Code Quality: ${(suggestion.impact.codeQuality * 100).toFixed(0)}%
- Performance: ${(suggestion.impact.performance * 100).toFixed(0)}%
- Maintainability: ${(suggestion.impact.maintainability * 100).toFixed(0)}%
- Readability: ${(suggestion.impact.readability * 100).toFixed(0)}%
- Testability: ${(suggestion.impact.testability * 100).toFixed(0)}%
- Security: ${(suggestion.impact.security * 100).toFixed(0)}%

## Implementation
${suggestion.implementation.code || 'No code provided'}
`
    
    const document = await vscode.workspace.openTextDocument({
      content: details,
      language: 'markdown'
    })
    
    await vscode.window.showTextDocument(document)
  }

  private async showSuggestionExamples(suggestion: Suggestion): Promise<void> {
    if (suggestion.examples.length === 0) {
      vscode.window.showInformationMessage('No examples available for this suggestion.')
      return
    }
    
    const exampleItems = suggestion.examples.map((example, index) => ({
      label: example.title,
      description: example.description,
      detail: `Example ${index + 1}`,
      example
    }))
    
    const selected = await vscode.window.showQuickPick(exampleItems, {
      title: 'Suggestion Examples',
      placeHolder: 'Select an example to view'
    })
    
    if (selected) {
      const exampleContent = `
# ${selected.example.title}

## Description
${selected.example.description}

## Before
\`\`\`${vscode.window.activeTextEditor?.document.languageId || 'javascript'}
${selected.example.before}
\`\`\`

## After
\`\`\`${vscode.window.activeTextEditor?.document.languageId || 'javascript'}
${selected.example.after}
\`\`\`

## Explanation
${selected.example.explanation}
`
      
      const document = await vscode.workspace.openTextDocument({
        content: exampleContent,
        language: 'markdown'
      })
      
      await vscode.window.showTextDocument(document)
    }
  }

  // Helper methods
  private buildAIPrompt(request: SuggestionRequest): string {
    return `
Analyze the following code and provide improvement suggestions:

Code:
\`\`\`${request.context.language}
${request.text}
\`\`\`

Context:
- Language: ${request.context.language}
- Framework: ${request.context.projectContext.framework}
- Project Type: ${request.context.projectContext.type}
- User Experience: ${request.context.userContext.experience}

Please provide specific, actionable suggestions for improving this code in terms of:
1. Code quality and best practices
2. Performance optimization
3. Security considerations
4. Maintainability and readability
5. Error handling
6. Testing opportunities

Format your response as a JSON array of suggestion objects.
`
  }

  private parseAISuggestions(response: string, request: SuggestionRequest): Suggestion[] {
    try {
      const parsed = JSON.parse(response)
      return parsed.map((item: any, index: number) => ({
        id: `ai_${index}`,
        title: item.title || 'AI Suggestion',
        description: item.description || '',
        category: item.category || 'ai_generated',
        type: {
          category: item.type?.category || 'best_practices',
          subcategory: item.type?.subcategory || 'ai_generated',
          enabled: true,
          priority: item.type?.priority || 5
        },
        confidence: item.confidence || 0.8,
        impact: item.impact || {
          codeQuality: 0.8,
          performance: 0.6,
          maintainability: 0.7,
          readability: 0.8,
          testability: 0.6,
          security: 0.7,
          overall: 0.7
        },
        effort: item.effort || {
          timeMinutes: 30,
          complexity: 'medium',
          skillLevel: 'intermediate',
          dependencies: [],
          risks: []
        },
        implementation: item.implementation || {
          type: 'replace',
          code: '',
          position: request.context.position,
          range: request.context.selection,
          edits: [],
          commands: [],
          validation: []
        },
        examples: item.examples || [],
        explanation: item.explanation || '',
        benefits: item.benefits || [],
        risks: item.risks || [],
        metadata: {
          source: 'ai_service',
          generated: new Date(),
          lastUpdated: new Date(),
          version: '1.0.0',
          language: request.context.language,
          framework: request.context.projectContext.framework,
          tags: ['ai', 'generated'],
          related: []
        }
      }))
    } catch (error) {
      this.logger.error('Error parsing AI suggestions:', error)
      return []
    }
  }

  private getSuggestionValue(suggestion: Suggestion, type: string): any {
    switch (type) {
      case 'category':
        return suggestion.category
      case 'complexity':
        return suggestion.effort.complexity
      case 'language':
        return suggestion.metadata.language
      case 'framework':
        return suggestion.metadata.framework
      case 'confidence':
        return suggestion.confidence
      case 'impact':
        return suggestion.impact.overall
      default:
        return undefined
    }
  }

  private matchesFilter(value: any, filterValue: any, operator: string): boolean {
    switch (operator) {
      case 'eq':
        return value === filterValue
      case 'gt':
        return value > filterValue
      case 'lt':
        return value < filterValue
      case 'gte':
        return value >= filterValue
      case 'lte':
        return value <= filterValue
      case 'in':
        return Array.isArray(filterValue) && filterValue.includes(value)
      case 'not_in':
        return Array.isArray(filterValue) && !filterValue.includes(value)
      default:
        return true
    }
  }

  private calculateOverallConfidence(suggestions: Suggestion[]): number {
    if (suggestions.length === 0) return 0
    return suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length
  }

  private calculateCoverage(suggestions: Suggestion[]): number {
    const categories = new Set(suggestions.map(s => s.category))
    const totalCategories = ['refactoring', 'testing', 'documentation', 'performance', 'security', 'patterns', 'error_handling', 'best_practices', 'code_quality'].length
    return categories.size / totalCategories
  }

  private async trackInteraction(result: SuggestionResult): Promise<void> {
    await this.preferenceTracker.trackInteraction({
      type: 'suggestion_request',
      timestamp: new Date(),
      context: result.context,
      suggestions: result.suggestions.length,
      performance: result.performance
    })
  }

  private async trackSuggestionApplication(suggestion: Suggestion): Promise<void> {
    await this.learningEngine.recordFeedback({
      suggestionId: suggestion.id,
      action: 'applied',
      timestamp: new Date(),
      context: suggestion.metadata
    })
  }

  private async getUserInteractionHistory(): Promise<InteractionHistory> {
    const history = this.userInteractions.get('default')
    if (!history) {
      const newHistory: InteractionHistory = {
        totalInteractions: 0,
        successfulSuggestions: 0,
        rejectedSuggestions: 0,
        averageResponseTime: 0,
        preferredCategories: [],
        lastInteraction: new Date()
      }
      this.userInteractions.set('default', newHistory)
      return newHistory
    }
    return history
  }

  dispose(): void {
    this.disposables.forEach(d => d.dispose())
  }
}

// Supporting interfaces
interface InteractionHistory {
  totalInteractions: number
  successfulSuggestions: number
  rejectedSuggestions: number
  averageResponseTime: number
  preferredCategories: string[]
  lastInteraction: Date
}

interface UserPattern {
  id: string
  type: string
  frequency: number
  confidence: number
  lastSeen: Date
}

interface WorkingStyle {
  preferredComplexity: string
  workingHours: [number, number]
  productivityPeaks: number[]
  communicationStyle: string
}

interface CustomTemplate {
  id: string
  name: string
  content: string
  category: string
  tags: string[]
}

interface Symbol {
  name: string
  type: string
  range: vscode.Range
  kind: vscode.SymbolKind
}

interface ImportDeclaration {
  module: string
  imports: string[]
  range: vscode.Range
}

interface FunctionDeclaration {
  name: string
  parameters: string[]
  returnType: string
  range: vscode.Range
}

interface ClassDeclaration {
  name: string
  methods: string[]
  properties: string[]
  range: vscode.Range
}

interface InterfaceDeclaration {
  name: string
  properties: string[]
  methods: string[]
  range: vscode.Range
}

interface VariableDeclaration {
  name: string
  type: string
  value: string
  range: vscode.Range
}

interface Comment {
  text: string
  type: 'line' | 'block'
  range: vscode.Range
}

interface CodeIssue {
  message: string
  severity: 'error' | 'warning' | 'info'
  range: vscode.Range
  code: string
}

interface CodeMetrics {
  linesOfCode: number
  cyclomaticComplexity: number
  cognitiveComplexity: number
  maintainabilityIndex: number
  technicalDebt: number
}
