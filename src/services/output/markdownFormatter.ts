import * as vscode from 'vscode'

export interface MarkdownDocument {
  title: string
  content: MarkdownSection[]
  metadata: DocumentMetadata
  styles: MarkdownStyles
}

export interface MarkdownSection {
  id: string
  type: SectionType
  title: string
  content: string
  level: number
  subsections: MarkdownSection[]
  elements: MarkdownElement[]
  metadata: SectionMetadata
}

export interface SectionType {
  category: 'header' | 'paragraph' | 'list' | 'code' | 'table' | 'quote' | 'divider' | 'task_breakdown'
  variant: string
  formatting: FormattingOptions
}

export interface MarkdownElement {
  type: 'text' | 'code' | 'link' | 'image' | 'table' | 'list' | 'checkbox' | 'emphasis' | 'strong' | 'inline_code'
  content: string
  attributes: ElementAttributes
  position: ElementPosition
}

export interface ElementAttributes {
  language?: string
  url?: string
  alt?: string
  title?: string
  checked?: boolean
  style?: string
  classes?: string[]
}

export interface ElementPosition {
  line: number
  character: number
  length: number
}

export interface FormattingOptions {
  indentation: number
  spacing: SpacingOptions
  alignment: 'left' | 'center' | 'right' | 'justify'
  emphasis: EmphasisOptions
  borders: BorderOptions
}

export interface SpacingOptions {
  before: number
  after: number
  internal: number
}

export interface EmphasisOptions {
  bold: boolean
  italic: boolean
  underline: boolean
  strikethrough: boolean
  highlight: boolean
}

export interface BorderOptions {
  top: boolean
  bottom: boolean
  left: boolean
  right: boolean
  style: 'solid' | 'dashed' | 'dotted'
}

export interface DocumentMetadata {
  author: string
  created: Date
  modified: Date
  version: string
  tags: string[]
  language: string
  encoding: string
  lineEnding: string
}

export interface SectionMetadata {
  generated: boolean
  source: string
  confidence: number
  editCount: number
  lastModified: Date
  dependencies: string[]
}

export interface MarkdownStyles {
  theme: 'default' | 'github' | 'gitlab' | 'minimal' | 'professional'
  headingStyle: 'atx' | 'setext'
  listStyle: 'dash' | 'asterisk' | 'plus'
  codeStyle: 'fenced' | 'indented'
  linkStyle: 'inline' | 'reference'
  tableStyle: 'pipe' | 'grid'
  emphasisStyle: 'asterisk' | 'underscore'
}

export interface TableDefinition {
  headers: string[]
  rows: string[][]
  alignment: ('left' | 'center' | 'right')[]
  formatting: TableFormatting
}

export interface TableFormatting {
  headerStyle: 'bold' | 'italic' | 'normal'
  cellPadding: number
  borderStyle: 'minimal' | 'full' | 'none'
  zebra: boolean
}

export interface ListDefinition {
  items: ListItem[]
  ordered: boolean
  startNumber: number
  style: string
  indent: number
}

export interface ListItem {
  content: string
  level: number
  checked?: boolean
  sublists: ListDefinition[]
}

export interface CodeBlock {
  language: string
  code: string
  filename?: string
  highlights: LineHighlight[]
  lineNumbers: boolean
  wrap: boolean
}

export interface LineHighlight {
  line: number
  type: 'error' | 'warning' | 'info' | 'success' | 'highlight'
  message?: string
}

export interface FormattingContext {
  currentSection: string
  indentLevel: number
  listContext: ListContext[]
  codeContext: CodeContext | null
  tableContext: TableContext | null
  linkReferences: Map<string, string>
}

export interface ListContext {
  type: 'ordered' | 'unordered' | 'task'
  level: number
  counter: number
  style: string
}

export interface CodeContext {
  language: string
  inline: boolean
  fenced: boolean
  startLine: number
}

export interface TableContext {
  columnCount: number
  currentRow: number
  alignments: string[]
  inHeader: boolean
}

export class MarkdownFormatter {
  private styles: MarkdownStyles
  private context: FormattingContext
  private lineEnding: string
  private indentString: string

  constructor(styles?: Partial<MarkdownStyles>) {
    this.styles = {
      theme: 'default',
      headingStyle: 'atx',
      listStyle: 'dash',
      codeStyle: 'fenced',
      linkStyle: 'inline',
      tableStyle: 'pipe',
      emphasisStyle: 'asterisk',
      ...styles
    }

    this.context = {
      currentSection: '',
      indentLevel: 0,
      listContext: [],
      codeContext: null,
      tableContext: null,
      linkReferences: new Map()
    }

    this.lineEnding = '\n'
    this.indentString = '  '
  }

  formatDocument(document: MarkdownDocument): string {
    const parts: string[] = []
    
    // Add document metadata if present
    if (document.metadata) {
      parts.push(this.formatMetadata(document.metadata))
    }
    
    // Add title
    if (document.title) {
      parts.push(this.formatHeading(document.title, 1))
    }
    
    // Add content sections
    for (const section of document.content) {
      parts.push(this.formatSection(section))
    }
    
    // Add link references if using reference style
    if (this.styles.linkStyle === 'reference' && this.context.linkReferences.size > 0) {
      parts.push(this.formatLinkReferences())
    }
    
    return parts.filter(part => part.trim()).join(this.lineEnding + this.lineEnding)
  }

  formatSection(section: MarkdownSection): string {
    const parts: string[] = []
    this.context.currentSection = section.id
    
    // Add section title
    if (section.title) {
      parts.push(this.formatHeading(section.title, section.level))
    }
    
    // Add section content
    if (section.content) {
      parts.push(this.formatContent(section.content, section.type))
    }
    
    // Add elements
    for (const element of section.elements) {
      parts.push(this.formatElement(element))
    }
    
    // Add subsections
    for (const subsection of section.subsections) {
      parts.push(this.formatSection(subsection))
    }
    
    return parts.filter(part => part.trim()).join(this.lineEnding + this.lineEnding)
  }

  formatHeading(text: string, level: number): string {
    const clampedLevel = Math.max(1, Math.min(6, level))
    
    if (this.styles.headingStyle === 'atx') {
      const hashes = '#'.repeat(clampedLevel)
      return `${hashes} ${text}`
    } else if (this.styles.headingStyle === 'setext' && clampedLevel <= 2) {
      const underline = clampedLevel === 1 ? '=' : '-'
      return `${text}${this.lineEnding}${underline.repeat(text.length)}`
    } else {
      // Fall back to atx for levels > 2
      const hashes = '#'.repeat(clampedLevel)
      return `${hashes} ${text}`
    }
  }

  formatContent(content: string, type: SectionType): string {
    switch (type.category) {
      case 'paragraph':
        return this.formatParagraph(content, type.formatting)
      case 'code':
        return this.formatCodeBlock(content, type.formatting)
      case 'list':
        return this.formatList(JSON.parse(content), type.formatting)
      case 'table':
        return this.formatTable(JSON.parse(content), type.formatting)
      case 'quote':
        return this.formatBlockquote(content, type.formatting)
      case 'divider':
        return this.formatDivider(type.formatting)
      case 'task_breakdown':
        return this.formatTaskBreakdown(JSON.parse(content), type.formatting)
      default:
        return content
    }
  }

  formatElement(element: MarkdownElement): string {
    switch (element.type) {
      case 'text':
        return this.formatText(element.content, element.attributes)
      case 'code':
        return this.formatInlineCode(element.content)
      case 'link':
        return this.formatLink(element.content, element.attributes.url!, element.attributes.title)
      case 'image':
        return this.formatImage(element.content, element.attributes.url!, element.attributes.alt)
      case 'table':
        return this.formatTable(JSON.parse(element.content), {})
      case 'list':
        return this.formatList(JSON.parse(element.content), {})
      case 'checkbox':
        return this.formatCheckbox(element.content, element.attributes.checked || false)
      case 'emphasis':
        return this.formatEmphasis(element.content, false)
      case 'strong':
        return this.formatEmphasis(element.content, true)
      case 'inline_code':
        return this.formatInlineCode(element.content)
      default:
        return element.content
    }
  }

  formatParagraph(text: string, formatting: FormattingOptions): string {
    const lines = text.split('\n')
    const formattedLines = lines.map(line => {
      const indent = this.indentString.repeat(formatting.indentation || 0)
      return `${indent}${line.trim()}`
    })
    
    return formattedLines.join(this.lineEnding)
  }

  formatCodeBlock(code: string, formatting: FormattingOptions): string {
    if (this.styles.codeStyle === 'fenced') {
      const language = (formatting as any).language || ''
      const fence = '```'
      return `${fence}${language}${this.lineEnding}${code}${this.lineEnding}${fence}`
    } else {
      const lines = code.split('\n')
      const indentedLines = lines.map(line => `    ${line}`)
      return indentedLines.join(this.lineEnding)
    }
  }

  formatList(list: ListDefinition, formatting: FormattingOptions): string {
    const lines: string[] = []
    const baseIndent = this.indentString.repeat(formatting.indentation || 0)
    
    this.context.listContext.push({
      type: list.ordered ? 'ordered' : 'unordered',
      level: this.context.indentLevel,
      counter: list.startNumber || 1,
      style: list.style || this.styles.listStyle
    })
    
    for (const item of list.items) {
      lines.push(...this.formatListItem(item, baseIndent))
    }
    
    this.context.listContext.pop()
    return lines.join(this.lineEnding)
  }

  formatListItem(item: ListItem, baseIndent: string): string[] {
    const lines: string[] = []
    const indent = baseIndent + this.indentString.repeat(item.level)
    
    const currentContext = this.context.listContext[this.context.listContext.length - 1]
    
    let bullet: string
    if (currentContext.type === 'ordered') {
      bullet = `${currentContext.counter}.`
      currentContext.counter++
    } else if (currentContext.type === 'task') {
      bullet = item.checked ? '- [x]' : '- [ ]'
    } else {
      bullet = this.getUnorderedBullet(currentContext.style, item.level)
    }
    
    lines.push(`${indent}${bullet} ${item.content}`)
    
    // Handle sublists
    for (const sublist of item.sublists) {
      const sublistLines = this.formatList(sublist, { indentation: item.level + 1 } as FormattingOptions)
      lines.push(sublistLines)
    }
    
    return lines
  }

  formatTable(table: TableDefinition, formatting: FormattingOptions): string {
    if (this.styles.tableStyle === 'pipe') {
      return this.formatPipeTable(table)
    } else {
      return this.formatGridTable(table)
    }
  }

  formatPipeTable(table: TableDefinition): string {
    const lines: string[] = []
    
    // Header row
    const headerRow = `| ${table.headers.join(' | ')} |`
    lines.push(headerRow)
    
    // Separator row
    const separators = table.alignment.map(align => {
      switch (align) {
        case 'left': return ':---'
        case 'center': return ':---:'
        case 'right': return '---:'
        default: return '---'
      }
    })
    const separatorRow = `| ${separators.join(' | ')} |`
    lines.push(separatorRow)
    
    // Data rows
    for (const row of table.rows) {
      const dataRow = `| ${row.join(' | ')} |`
      lines.push(dataRow)
    }
    
    return lines.join(this.lineEnding)
  }

  formatGridTable(table: TableDefinition): string {
    // Complex grid table implementation
    const lines: string[] = []
    
    // Calculate column widths
    const columnWidths = table.headers.map((header, index) => {
      const dataWidths = table.rows.map(row => (row[index] || '').length)
      return Math.max(header.length, ...dataWidths) + 2 // +2 for padding
    })
    
    // Top border
    const topBorder = '+' + columnWidths.map(width => '-'.repeat(width)).join('+') + '+'
    lines.push(topBorder)
    
    // Header row
    const headerRow = '|' + table.headers.map((header, index) => {
      return ` ${header.padEnd(columnWidths[index] - 1)}`
    }).join('|') + '|'
    lines.push(headerRow)
    
    // Header separator
    const headerSeparator = '+' + columnWidths.map(width => '='.repeat(width)).join('+') + '+'
    lines.push(headerSeparator)
    
    // Data rows
    for (const row of table.rows) {
      const dataRow = '|' + row.map((cell, index) => {
        return ` ${(cell || '').padEnd(columnWidths[index] - 1)}`
      }).join('|') + '|'
      lines.push(dataRow)
    }
    
    // Bottom border
    const bottomBorder = '+' + columnWidths.map(width => '-'.repeat(width)).join('+') + '+'
    lines.push(bottomBorder)
    
    return lines.join(this.lineEnding)
  }

  formatBlockquote(text: string, formatting: FormattingOptions): string {
    const lines = text.split('\n')
    const quotedLines = lines.map(line => `> ${line}`)
    return quotedLines.join(this.lineEnding)
  }

  formatDivider(formatting: FormattingOptions): string {
    return '---'
  }

  formatTaskBreakdown(tasks: any[], formatting: FormattingOptions): string {
    const lines: string[] = []
    
    for (const task of tasks) {
      const checkbox = task.completed ? '[x]' : '[ ]'
      const priority = task.priority ? ` **${task.priority.toUpperCase()}**` : ''
      const estimate = task.estimate ? ` _(${task.estimate})_` : ''
      
      lines.push(`- ${checkbox} ${task.title}${priority}${estimate}`)
      
      if (task.description) {
        lines.push(`  ${task.description}`)
      }
      
      if (task.subtasks && task.subtasks.length > 0) {
        for (const subtask of task.subtasks) {
          const subCheckbox = subtask.completed ? '[x]' : '[ ]'
          lines.push(`  - ${subCheckbox} ${subtask.title}`)
        }
      }
      
      if (task.notes) {
        lines.push(`  > ${task.notes}`)
      }
    }
    
    return lines.join(this.lineEnding)
  }

  formatText(text: string, attributes: ElementAttributes): string {
    let formatted = text
    
    if (attributes.style) {
      const styles = attributes.style.split(' ')
      for (const style of styles) {
        switch (style) {
          case 'bold':
            formatted = this.formatEmphasis(formatted, true)
            break
          case 'italic':
            formatted = this.formatEmphasis(formatted, false)
            break
          case 'code':
            formatted = this.formatInlineCode(formatted)
            break
          case 'strikethrough':
            formatted = `~~${formatted}~~`
            break
        }
      }
    }
    
    return formatted
  }

  formatInlineCode(code: string): string {
    // Handle backticks in code
    const backticks = code.includes('`') ? '``' : '`'
    return `${backticks}${code}${backticks}`
  }

  formatLink(text: string, url: string, title?: string): string {
    if (this.styles.linkStyle === 'inline') {
      const titleAttr = title ? ` "${title}"` : ''
      return `[${text}](${url}${titleAttr})`
    } else {
      // Reference style
      const refId = this.generateLinkReference(text, url)
      this.context.linkReferences.set(refId, url)
      return `[${text}][${refId}]`
    }
  }

  formatImage(alt: string, url: string, title?: string): string {
    const titleAttr = title ? ` "${title}"` : ''
    return `![${alt}](${url}${titleAttr})`
  }

  formatCheckbox(text: string, checked: boolean): string {
    const checkbox = checked ? '[x]' : '[ ]'
    return `- ${checkbox} ${text}`
  }

  formatEmphasis(text: string, strong: boolean): string {
    const marker = this.styles.emphasisStyle === 'asterisk' ? '*' : '_'
    const emphasis = strong ? marker.repeat(2) : marker
    return `${emphasis}${text}${emphasis}`
  }

  formatMetadata(metadata: DocumentMetadata): string {
    const lines: string[] = []
    lines.push('---')
    lines.push(`title: "${metadata.author}"`)
    lines.push(`author: "${metadata.author}"`)
    lines.push(`date: ${metadata.created.toISOString().split('T')[0]}`)
    lines.push(`version: ${metadata.version}`)
    if (metadata.tags.length > 0) {
      lines.push(`tags: [${metadata.tags.map(tag => `"${tag}"`).join(', ')}]`)
    }
    lines.push('---')
    return lines.join(this.lineEnding)
  }

  formatLinkReferences(): string {
    const lines: string[] = []
    for (const [refId, url] of this.context.linkReferences) {
      lines.push(`[${refId}]: ${url}`)
    }
    return lines.join(this.lineEnding)
  }

  private getUnorderedBullet(style: string, level: number): string {
    const bullets = {
      'dash': ['-', '-', '-'],
      'asterisk': ['*', '*', '*'],
      'plus': ['+', '+', '+'],
      'mixed': ['-', '*', '+']
    }
    
    const bulletSet = bullets[style as keyof typeof bullets] || bullets.dash
    return bulletSet[level % bulletSet.length]
  }

  private generateLinkReference(text: string, url: string): string {
    const baseRef = text.toLowerCase().replace(/[^a-z0-9]/g, '')
    let refId = baseRef
    let counter = 1
    
    while (this.context.linkReferences.has(refId)) {
      refId = `${baseRef}${counter}`
      counter++
    }
    
    return refId
  }

  // Utility methods for creating common markdown structures
  createTaskList(tasks: { title: string; completed: boolean; priority?: string; estimate?: string }[]): MarkdownSection {
    return {
      id: 'task_list',
      type: {
        category: 'task_breakdown',
        variant: 'checkbox',
        formatting: { indentation: 0, spacing: { before: 0, after: 0, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
      },
      title: 'Tasks',
      content: JSON.stringify(tasks),
      level: 2,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'task_generator',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    }
  }

  createCodeSection(code: string, language: string, title?: string): MarkdownSection {
    return {
      id: 'code_section',
      type: {
        category: 'code',
        variant: 'fenced',
        formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' }, language } as any
      },
      title: title || 'Code',
      content: code,
      level: 3,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'code_formatter',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    }
  }

  createTable(headers: string[], rows: string[][], title?: string): MarkdownSection {
    const tableData: TableDefinition = {
      headers,
      rows,
      alignment: headers.map(() => 'left'),
      formatting: {
        headerStyle: 'bold',
        cellPadding: 1,
        borderStyle: 'minimal',
        zebra: false
      }
    }
    
    return {
      id: 'table_section',
      type: {
        category: 'table',
        variant: 'pipe',
        formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
      },
      title: title || 'Table',
      content: JSON.stringify(tableData),
      level: 3,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'table_generator',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    }
  }

  // Configuration methods
  setStyles(styles: Partial<MarkdownStyles>): void {
    this.styles = { ...this.styles, ...styles }
  }

  setLineEnding(lineEnding: string): void {
    this.lineEnding = lineEnding
  }

  setIndentString(indentString: string): void {
    this.indentString = indentString
  }

  // Export methods
  exportToString(document: MarkdownDocument): string {
    return this.formatDocument(document)
  }

  exportToFile(document: MarkdownDocument, filePath: string): Promise<void> {
    const content = this.formatDocument(document)
    return vscode.workspace.fs.writeFile(vscode.Uri.file(filePath), Buffer.from(content, 'utf8'))
  }

  exportToClipboard(document: MarkdownDocument): Promise<void> {
    const content = this.formatDocument(document)
    return vscode.env.clipboard.writeText(content)
  }
}