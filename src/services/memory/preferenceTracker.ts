import { FileSystemService } from '../fileSystemService'
import { ConnectionManager } from '../database/connectionManager'
import { StorageService } from '../storageService'

export interface UserPreference {
  key: string
  value: any
  category: PreferenceCategory
  confidence: number
  frequency: number
  lastUpdated: Date
  context?: string
  tags: string[]
}

export interface PreferenceCategory {
  name: string
  description: string
  weight: number
  decayRate: number
}

export interface UserInteraction {
  id: string
  type: InteractionType
  timestamp: Date
  context: InteractionContext
  metadata: any
  outcome: InteractionOutcome
}

export interface InteractionType {
  category: 'search' | 'file_access' | 'command' | 'completion' | 'feedback'
  action: string
  importance: number
}

export interface InteractionContext {
  filePath?: string
  language?: string
  symbolType?: string
  query?: string
  selectedText?: string
  workspaceRoot?: string
  timeOfDay?: string
  dayOfWeek?: string
}

export interface InteractionOutcome {
  success: boolean
  used: boolean
  rating?: number
  duration?: number
  followUpActions?: string[]
}

export interface PreferenceProfile {
  userId: string
  preferences: Map<string, UserPreference>
  categories: PreferenceCategory[]
  lastActive: Date
  totalInteractions: number
  learningEnabled: boolean
}

export interface LearningPattern {
  pattern: string
  confidence: number
  frequency: number
  examples: string[]
  category: string
  predictiveValue: number
}

export interface PreferenceInsight {
  type: 'language_preference' | 'file_pattern' | 'time_pattern' | 'workflow_pattern'
  description: string
  confidence: number
  recommendation: string
  impact: 'high' | 'medium' | 'low'
}

export class PreferenceTracker {
  private fileSystem: FileSystemService
  private db: ConnectionManager
  private storage: StorageService
  private userProfile: PreferenceProfile
  private interactionHistory: UserInteraction[]
  private categories: PreferenceCategory[]
  private patterns: Map<string, LearningPattern>

  constructor(
    fileSystem: FileSystemService,
    db: ConnectionManager,
    storage: StorageService
  ) {
    this.fileSystem = fileSystem
    this.db = db
    this.storage = storage
    this.interactionHistory = []
    this.patterns = new Map()
    this.categories = this.initializeCategories()
    this.userProfile = this.initializeProfile()
  }

  private initializeCategories(): PreferenceCategory[] {
    return [
      {
        name: 'language_preference',
        description: 'Programming language preferences',
        weight: 1.0,
        decayRate: 0.1
      },
      {
        name: 'file_type_preference',
        description: 'File type access patterns',
        weight: 0.8,
        decayRate: 0.15
      },
      {
        name: 'search_behavior',
        description: 'Search query patterns and preferences',
        weight: 0.9,
        decayRate: 0.2
      },
      {
        name: 'code_style',
        description: 'Coding style and pattern preferences',
        weight: 0.7,
        decayRate: 0.05
      },
      {
        name: 'workflow_timing',
        description: 'Time-based activity patterns',
        weight: 0.6,
        decayRate: 0.3
      },
      {
        name: 'completion_preference',
        description: 'Code completion and suggestion preferences',
        weight: 0.8,
        decayRate: 0.25
      }
    ]
  }

  private initializeProfile(): PreferenceProfile {
    return {
      userId: 'default_user',
      preferences: new Map(),
      categories: this.categories,
      lastActive: new Date(),
      totalInteractions: 0,
      learningEnabled: true
    }
  }

  async loadProfile(userId: string = 'default_user'): Promise<void> {
    try {
      const savedProfile = await this.storage.getWorkspaceState(`preference_profile_${userId}`)
      const savedInteractions = await this.storage.getWorkspaceState(`interaction_history_${userId}`)
      
      if (savedProfile) {
        this.userProfile = {
          ...savedProfile,
          preferences: new Map(savedProfile.preferences || [])
        }
      }
      
      if (savedInteractions) {
        this.interactionHistory = savedInteractions
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  async saveProfile(): Promise<void> {
    try {
      const profileToSave = {
        ...this.userProfile,
        preferences: Array.from(this.userProfile.preferences.entries())
      }
      
      await this.storage.setWorkspaceState(`preference_profile_${this.userProfile.userId}`, profileToSave)
      await this.storage.setWorkspaceState(`interaction_history_${this.userProfile.userId}`, this.interactionHistory)
    } catch (error) {
      console.error('Error saving user profile:', error)
    }
  }

  async trackInteraction(interaction: Omit<UserInteraction, 'id' | 'timestamp'>): Promise<void> {
    const fullInteraction: UserInteraction = {
      ...interaction,
      id: this.generateInteractionId(),
      timestamp: new Date()
    }
    
    this.interactionHistory.push(fullInteraction)
    this.userProfile.totalInteractions++
    this.userProfile.lastActive = new Date()
    
    // Keep only recent interactions to prevent memory bloat
    if (this.interactionHistory.length > 10000) {
      this.interactionHistory = this.interactionHistory.slice(-5000)
    }
    
    // Update preferences based on interaction
    await this.updatePreferencesFromInteraction(fullInteraction)
    
    // Save profile periodically
    if (this.userProfile.totalInteractions % 50 === 0) {
      await this.saveProfile()
    }
  }

  private async updatePreferencesFromInteraction(interaction: UserInteraction): Promise<void> {
    if (!this.userProfile.learningEnabled) return
    
    const context = interaction.context
    
    // Track language preferences
    if (context.language) {
      await this.updateLanguagePreference(context.language, interaction.outcome)
    }
    
    // Track file type preferences
    if (context.filePath) {
      const fileType = this.fileSystem.extname(context.filePath)
      await this.updateFileTypePreference(fileType, interaction.outcome)
    }
    
    // Track search behavior
    if (interaction.type.category === 'search' && context.query) {
      await this.updateSearchBehavior(context.query, interaction.outcome)
    }
    
    // Track completion preferences
    if (interaction.type.category === 'completion') {
      await this.updateCompletionPreference(interaction.type.action, interaction.outcome)
    }
    
    // Track time-based patterns
    await this.updateTimePatterns(interaction)
  }

  private async updateLanguagePreference(language: string, outcome: InteractionOutcome): Promise<void> {
    const key = `language_${language}`
    const category = this.categories.find(c => c.name === 'language_preference')!
    
    const existing = this.userProfile.preferences.get(key)
    const baseWeight = outcome.success ? 1.0 : 0.5
    const usageWeight = outcome.used ? 1.2 : 0.8
    const ratingWeight = outcome.rating ? outcome.rating / 5 : 1.0
    
    const weight = baseWeight * usageWeight * ratingWeight
    
    if (existing) {
      existing.frequency++
      existing.confidence = Math.min(1.0, existing.confidence + weight * 0.1)
      existing.lastUpdated = new Date()
    } else {
      this.userProfile.preferences.set(key, {
        key,
        value: language,
        category,
        confidence: weight * 0.3,
        frequency: 1,
        lastUpdated: new Date(),
        context: 'language_usage',
        tags: ['language', 'programming']
      })
    }
  }

  private async updateFileTypePreference(fileType: string, outcome: InteractionOutcome): Promise<void> {
    const key = `file_type_${fileType}`
    const category = this.categories.find(c => c.name === 'file_type_preference')!
    
    const existing = this.userProfile.preferences.get(key)
    const weight = outcome.success ? 1.0 : 0.3
    
    if (existing) {
      existing.frequency++
      existing.confidence = Math.min(1.0, existing.confidence + weight * 0.05)
      existing.lastUpdated = new Date()
    } else {
      this.userProfile.preferences.set(key, {
        key,
        value: fileType,
        category,
        confidence: weight * 0.2,
        frequency: 1,
        lastUpdated: new Date(),
        context: 'file_access',
        tags: ['file_type', 'access_pattern']
      })
    }
  }

  private async updateSearchBehavior(query: string, outcome: InteractionOutcome): Promise<void> {
    const searchTerms = query.toLowerCase().split(/\s+/)
    const category = this.categories.find(c => c.name === 'search_behavior')!
    
    for (const term of searchTerms) {
      if (term.length < 3) continue
      
      const key = `search_term_${term}`
      const existing = this.userProfile.preferences.get(key)
      const weight = outcome.success ? 1.0 : 0.2
      
      if (existing) {
        existing.frequency++
        existing.confidence = Math.min(1.0, existing.confidence + weight * 0.1)
        existing.lastUpdated = new Date()
      } else {
        this.userProfile.preferences.set(key, {
          key,
          value: term,
          category,
          confidence: weight * 0.4,
          frequency: 1,
          lastUpdated: new Date(),
          context: 'search_query',
          tags: ['search', 'query_term']
        })
      }
    }
  }

  private async updateCompletionPreference(action: string, outcome: InteractionOutcome): Promise<void> {
    const key = `completion_${action}`
    const category = this.categories.find(c => c.name === 'completion_preference')!
    
    const existing = this.userProfile.preferences.get(key)
    const weight = outcome.used ? 1.0 : 0.1
    
    if (existing) {
      existing.frequency++
      existing.confidence = Math.min(1.0, existing.confidence + weight * 0.15)
      existing.lastUpdated = new Date()
    } else {
      this.userProfile.preferences.set(key, {
        key,
        value: action,
        category,
        confidence: weight * 0.3,
        frequency: 1,
        lastUpdated: new Date(),
        context: 'code_completion',
        tags: ['completion', 'code_assist']
      })
    }
  }

  private async updateTimePatterns(interaction: UserInteraction): Promise<void> {
    const hour = interaction.timestamp.getHours()
    const dayOfWeek = interaction.timestamp.getDay()
    
    const hourKey = `active_hour_${hour}`
    const dayKey = `active_day_${dayOfWeek}`
    const category = this.categories.find(c => c.name === 'workflow_timing')!
    
    // Track hourly patterns
    const hourPref = this.userProfile.preferences.get(hourKey)
    if (hourPref) {
      hourPref.frequency++
      hourPref.confidence = Math.min(1.0, hourPref.confidence + 0.02)
      hourPref.lastUpdated = new Date()
    } else {
      this.userProfile.preferences.set(hourKey, {
        key: hourKey,
        value: hour,
        category,
        confidence: 0.1,
        frequency: 1,
        lastUpdated: new Date(),
        context: 'time_pattern',
        tags: ['time', 'activity_pattern']
      })
    }
    
    // Track daily patterns
    const dayPref = this.userProfile.preferences.get(dayKey)
    if (dayPref) {
      dayPref.frequency++
      dayPref.confidence = Math.min(1.0, dayPref.confidence + 0.01)
      dayPref.lastUpdated = new Date()
    } else {
      this.userProfile.preferences.set(dayKey, {
        key: dayKey,
        value: dayOfWeek,
        category,
        confidence: 0.05,
        frequency: 1,
        lastUpdated: new Date(),
        context: 'time_pattern',
        tags: ['time', 'weekly_pattern']
      })
    }
  }

  getPreferencesByCategory(categoryName: string): UserPreference[] {
    return Array.from(this.userProfile.preferences.values())
      .filter(pref => pref.category.name === categoryName)
      .sort((a, b) => b.confidence - a.confidence)
  }

  getTopPreferences(limit: number = 10): UserPreference[] {
    return Array.from(this.userProfile.preferences.values())
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, limit)
  }

  getPreferencesByTag(tag: string): UserPreference[] {
    return Array.from(this.userProfile.preferences.values())
      .filter(pref => pref.tags.includes(tag))
      .sort((a, b) => b.confidence - a.confidence)
  }

  async analyzePatterns(): Promise<PreferenceInsight[]> {
    const insights: PreferenceInsight[] = []
    
    // Language preferences
    const langPrefs = this.getPreferencesByCategory('language_preference')
    if (langPrefs.length > 0) {
      const topLang = langPrefs[0]
      insights.push({
        type: 'language_preference',
        description: `You primarily work with ${topLang.value} (${Math.round(topLang.confidence * 100)}% confidence)`,
        confidence: topLang.confidence,
        recommendation: `Consider exploring advanced ${topLang.value} features and tools`,
        impact: 'medium'
      })
    }
    
    // File type patterns
    const filePrefs = this.getPreferencesByCategory('file_type_preference')
    if (filePrefs.length > 0) {
      const topFileType = filePrefs[0]
      insights.push({
        type: 'file_pattern',
        description: `You frequently work with ${topFileType.value} files`,
        confidence: topFileType.confidence,
        recommendation: `Optimize your workflow for ${topFileType.value} development`,
        impact: 'low'
      })
    }
    
    // Time patterns
    const timePrefs = this.getPreferencesByCategory('workflow_timing')
    const hourPrefs = timePrefs.filter(p => p.key.startsWith('active_hour_'))
    if (hourPrefs.length > 0) {
      const topHour = hourPrefs.sort((a, b) => b.frequency - a.frequency)[0]
      insights.push({
        type: 'time_pattern',
        description: `You're most active around ${topHour.value}:00`,
        confidence: topHour.confidence,
        recommendation: 'Consider scheduling important tasks during peak hours',
        impact: 'low'
      })
    }
    
    return insights
  }

  async decayPreferences(): Promise<void> {
    const now = new Date()
    const oneDayMs = 24 * 60 * 60 * 1000
    
    for (const [key, preference] of this.userProfile.preferences) {
      const daysSinceUpdate = (now.getTime() - preference.lastUpdated.getTime()) / oneDayMs
      const decayFactor = Math.exp(-preference.category.decayRate * daysSinceUpdate)
      
      preference.confidence *= decayFactor
      
      // Remove preferences that have become too weak
      if (preference.confidence < 0.05) {
        this.userProfile.preferences.delete(key)
      }
    }
  }

  async clearPreferences(): Promise<void> {
    this.userProfile.preferences.clear()
    this.interactionHistory = []
    this.userProfile.totalInteractions = 0
    await this.saveProfile()
  }

  setLearningEnabled(enabled: boolean): void {
    this.userProfile.learningEnabled = enabled
  }

  isLearningEnabled(): boolean {
    return this.userProfile.learningEnabled
  }

  getProfile(): PreferenceProfile {
    return { ...this.userProfile }
  }

  getInteractionHistory(): UserInteraction[] {
    return [...this.interactionHistory]
  }

  private generateInteractionId(): string {
    return `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}