{"entryPoints": ["src/extension.ts", "src/services/ai/AiService.ts", "src/services/TemplateService.ts", "src/services/IndexingService.ts", "src/services/StorageService.ts", "src/services/FileWatcherService.ts", "src/services/ContextService.ts", "src/commands/TransformCommand.ts", "src/commands/SuggestCommand.ts", "src/commands/ConfigureCommand.ts", "src/utils/errorHandler.ts", "src/utils/logger.ts", "src/utils/sanitizer.ts", "src/utils/apiKeyValidator.ts", "src/utils/securityAudit.ts"], "out": "docs/api", "theme": "default", "readme": "README.md", "excludePrivate": true, "excludeProtected": false, "excludeInternal": true, "includeVersion": true, "categorizeByGroup": true, "sort": ["source-order"], "gitRevision": "main", "name": "Natural Language Task Transformer API", "plugin": ["typedoc-plugin-markdown"], "tsconfig": "tsconfig.json", "exclude": ["src/test/**/*", "**/*.test.ts", "**/*.spec.ts", "node_modules/**/*"], "validation": {"notExported": false, "invalidLink": true, "notDocumented": false}}