import * as vscode from 'vscode'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er, MarkdownDocument, MarkdownSection } from './markdownFormatter'
import { ContextAnalyzer } from '../context/contextAnalyzer'
import { <PERSON><PERSON><PERSON><PERSON>ognizer } from '../memory/patternRecognizer'
import { LearningEngine } from '../memory/learningEngine'
import { SemanticSearch } from '../embeddings/semanticSearch'

export interface TaskBreakdown {
  id: string
  title: string
  description: string
  complexity: TaskComplexity
  priority: TaskPriority
  tasks: Task[]
  dependencies: TaskDependency[]
  timeline: TaskTimeline
  resources: TaskResource[]
  metadata: TaskMetadata
}

export interface Task {
  id: string
  title: string
  description: string
  type: TaskType
  status: TaskStatus
  priority: TaskPriority
  complexity: TaskComplexity
  estimatedTime: number
  actualTime?: number
  dependencies: string[]
  subtasks: Subtask[]
  checklist: ChecklistItem[]
  notes: string[]
  assignee?: string
  tags: string[]
  context: TaskContext
  validation: TaskValidation
  deliverables: TaskDeliverable[]
}

export interface Subtask {
  id: string
  title: string
  description: string
  completed: boolean
  priority: TaskPriority
  estimatedTime: number
  actualTime?: number
  dependencies: string[]
  notes: string[]
  validation: SubtaskValidation
  files: string[]
}

export interface ChecklistItem {
  id: string
  description: string
  completed: boolean
  required: boolean
  category: string
  validationRule?: string
}

export interface TaskType {
  category: 'development' | 'design' | 'testing' | 'documentation' | 'deployment' | 'maintenance' | 'research'
  subcategory: string
  methodology: string
  tools: string[]
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'
}

export interface TaskStatus {
  current: 'not_started' | 'in_progress' | 'completed' | 'blocked' | 'cancelled' | 'on_hold'
  progress: number
  lastUpdated: Date
  blockers: TaskBlocker[]
  milestones: TaskMilestone[]
}

export interface TaskPriority {
  level: 'low' | 'medium' | 'high' | 'critical'
  urgency: 'low' | 'medium' | 'high' | 'critical'
  importance: 'low' | 'medium' | 'high' | 'critical'
  businessValue: number
  technicalRisk: number
  dependencies: number
}

export interface TaskComplexity {
  overall: 'simple' | 'moderate' | 'complex' | 'very_complex'
  technical: number
  business: number
  integration: number
  testing: number
  documentation: number
  factors: ComplexityFactor[]
}

export interface ComplexityFactor {
  name: string
  description: string
  impact: number
  category: 'technical' | 'business' | 'organizational' | 'external'
  mitigation: string
}

export interface TaskDependency {
  fromTask: string
  toTask: string
  type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish' | 'start_to_finish'
  relationship: 'required' | 'preferred' | 'optional'
  lag: number
  description: string
}

export interface TaskTimeline {
  startDate: Date
  endDate: Date
  duration: number
  criticalPath: string[]
  milestones: TaskMilestone[]
  phases: TaskPhase[]
  buffer: number
}

export interface TaskMilestone {
  id: string
  title: string
  description: string
  date: Date
  type: 'deliverable' | 'checkpoint' | 'review' | 'approval'
  criteria: string[]
  dependencies: string[]
}

export interface TaskPhase {
  id: string
  name: string
  description: string
  startDate: Date
  endDate: Date
  tasks: string[]
  deliverables: string[]
  criticalPath: boolean
}

export interface TaskResource {
  type: 'human' | 'tool' | 'service' | 'infrastructure' | 'knowledge'
  name: string
  description: string
  availability: ResourceAvailability
  requirements: ResourceRequirement[]
  cost: ResourceCost
  alternatives: string[]
}

export interface ResourceAvailability {
  status: 'available' | 'limited' | 'unavailable' | 'unknown'
  startDate: Date
  endDate: Date
  utilization: number
  constraints: string[]
}

export interface ResourceRequirement {
  skill: string
  level: 'basic' | 'intermediate' | 'advanced' | 'expert'
  duration: number
  criticality: 'low' | 'medium' | 'high' | 'critical'
}

export interface ResourceCost {
  type: 'fixed' | 'variable' | 'subscription' | 'one_time'
  amount: number
  currency: string
  period: string
  breakdown: CostBreakdown[]
}

export interface CostBreakdown {
  item: string
  cost: number
  description: string
  category: string
}

export interface TaskContext {
  project: string
  module: string
  feature: string
  codebase: CodebaseContext
  environment: EnvironmentContext
  stakeholders: string[]
  constraints: string[]
  assumptions: string[]
}

export interface CodebaseContext {
  repository: string
  branch: string
  languages: string[]
  frameworks: string[]
  architecture: string
  patterns: string[]
  conventions: string[]
  coverage: number
}

export interface EnvironmentContext {
  development: EnvironmentInfo
  testing: EnvironmentInfo
  staging: EnvironmentInfo
  production: EnvironmentInfo
}

export interface EnvironmentInfo {
  name: string
  url?: string
  status: 'active' | 'inactive' | 'maintenance'
  configuration: Record<string, any>
  dependencies: string[]
}

export interface TaskValidation {
  criteria: ValidationCriteria[]
  methods: ValidationMethod[]
  acceptance: AcceptanceCriteria[]
  testCases: TestCase[]
  reviewers: string[]
}

export interface ValidationCriteria {
  id: string
  description: string
  type: 'functional' | 'non_functional' | 'quality' | 'performance' | 'security'
  priority: 'required' | 'recommended' | 'optional'
  measurable: boolean
  metric?: string
  target?: number
}

export interface ValidationMethod {
  type: 'manual' | 'automated' | 'peer_review' | 'testing' | 'analysis'
  description: string
  tools: string[]
  procedures: string[]
  frequency: string
}

export interface AcceptanceCriteria {
  id: string
  description: string
  scenario: string
  given: string
  when: string
  then: string
  priority: 'must_have' | 'should_have' | 'could_have' | 'wont_have'
}

export interface TestCase {
  id: string
  title: string
  description: string
  preconditions: string[]
  steps: TestStep[]
  expectedResult: string
  priority: 'high' | 'medium' | 'low'
  category: 'smoke' | 'regression' | 'integration' | 'unit' | 'e2e'
}

export interface TestStep {
  step: number
  action: string
  input?: string
  expectedOutput?: string
}

export interface SubtaskValidation {
  checkpoints: string[]
  deliverables: string[]
  quality_gates: QualityGate[]
}

export interface QualityGate {
  name: string
  description: string
  criteria: string[]
  automated: boolean
  threshold: number
}

export interface TaskDeliverable {
  id: string
  name: string
  description: string
  type: 'code' | 'document' | 'design' | 'test' | 'deployment' | 'configuration'
  format: string
  location: string
  dependencies: string[]
  acceptance: AcceptanceCriteria[]
  reviewers: string[]
}

export interface TaskBlocker {
  id: string
  description: string
  type: 'technical' | 'resource' | 'dependency' | 'approval' | 'external'
  severity: 'low' | 'medium' | 'high' | 'critical'
  impact: string
  resolution: string
  owner: string
  created: Date
  resolved?: Date
}

export interface TaskMetadata {
  created: Date
  createdBy: string
  lastModified: Date
  modifiedBy: string
  version: string
  source: string
  confidence: number
  template: string
  customFields: Record<string, any>
}

export interface GenerationContext {
  input: string
  language: string
  framework: string
  projectType: string
  complexity: string
  timeline: string
  resources: string[]
  constraints: string[]
  preferences: GenerationPreferences
  codebase: CodebaseContext
  stakeholders: string[]
}

export interface GenerationPreferences {
  detailLevel: 'high' | 'medium' | 'low'
  methodology: 'agile' | 'waterfall' | 'hybrid'
  estimationMethod: 'story_points' | 'time_based' | 'complexity_based'
  riskTolerance: 'low' | 'medium' | 'high'
  automationLevel: 'low' | 'medium' | 'high'
  documentationLevel: 'minimal' | 'standard' | 'comprehensive'
  testingStrategy: 'unit' | 'integration' | 'e2e' | 'comprehensive'
}

export interface TaskTemplate {
  id: string
  name: string
  description: string
  category: string
  applicability: TemplateApplicability
  structure: TaskStructure
  customization: TemplateCustomization
  examples: TaskExample[]
}

export interface TemplateApplicability {
  languages: string[]
  frameworks: string[]
  projectTypes: string[]
  complexity: string[]
  team_size: string[]
  timeline: string[]
}

export interface TaskStructure {
  phases: string[]
  taskTypes: string[]
  dependencies: string[]
  deliverables: string[]
  milestones: string[]
  resources: string[]
}

export interface TemplateCustomization {
  parameters: TemplateParameter[]
  conditionals: TemplateConditional[]
  variables: TemplateVariable[]
  transformations: TemplateTransformation[]
}

export interface TemplateParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  description: string
  required: boolean
  default?: any
  validation?: string
  options?: any[]
}

export interface TemplateConditional {
  condition: string
  include: string[]
  exclude: string[]
  modify: Record<string, any>
}

export interface TemplateVariable {
  name: string
  value: string
  scope: 'global' | 'phase' | 'task' | 'subtask'
  type: 'static' | 'dynamic' | 'computed'
}

export interface TemplateTransformation {
  type: 'rename' | 'merge' | 'split' | 'reorder' | 'filter' | 'aggregate'
  source: string
  target: string
  parameters: Record<string, any>
}

export interface TaskExample {
  name: string
  description: string
  input: any
  output: TaskBreakdown
  metadata: ExampleMetadata
}

export interface ExampleMetadata {
  complexity: string
  domain: string
  success_rate: number
  feedback_score: number
  usage_count: number
  last_updated: Date
}

export class TaskGenerator {
  private markdownFormatter: MarkdownFormatter
  private contextAnalyzer: ContextAnalyzer
  private patternRecognizer: PatternRecognizer
  private learningEngine: LearningEngine
  private semanticSearch: SemanticSearch
  private templates: Map<string, TaskTemplate>
  private generationHistory: TaskBreakdown[]

  constructor(
    markdownFormatter: MarkdownFormatter,
    contextAnalyzer: ContextAnalyzer,
    patternRecognizer: PatternRecognizer,
    learningEngine: LearningEngine,
    semanticSearch: SemanticSearch
  ) {
    this.markdownFormatter = markdownFormatter
    this.contextAnalyzer = contextAnalyzer
    this.patternRecognizer = patternRecognizer
    this.learningEngine = learningEngine
    this.semanticSearch = semanticSearch
    this.templates = new Map()
    this.generationHistory = []
    
    this.initializeTemplates()
  }

  private initializeTemplates(): void {
    // Initialize common task templates
    this.templates.set('web_development', {
      id: 'web_development',
      name: 'Web Development Project',
      description: 'Standard web development project breakdown',
      category: 'development',
      applicability: {
        languages: ['javascript', 'typescript', 'html', 'css'],
        frameworks: ['react', 'vue', 'angular', 'svelte'],
        projectTypes: ['spa', 'website', 'web_app'],
        complexity: ['simple', 'moderate', 'complex'],
        team_size: ['1-3', '4-8', '9+'],
        timeline: ['weeks', 'months']
      },
      structure: {
        phases: ['planning', 'design', 'development', 'testing', 'deployment'],
        taskTypes: ['setup', 'feature', 'integration', 'testing', 'documentation'],
        dependencies: ['linear', 'parallel', 'conditional'],
        deliverables: ['code', 'tests', 'documentation', 'deployment'],
        milestones: ['mvp', 'beta', 'release'],
        resources: ['developers', 'designers', 'qa', 'devops']
      },
      customization: {
        parameters: [
          {
            name: 'framework',
            type: 'string',
            description: 'Primary framework to use',
            required: true,
            options: ['react', 'vue', 'angular', 'svelte']
          },
          {
            name: 'complexity',
            type: 'string',
            description: 'Project complexity level',
            required: true,
            options: ['simple', 'moderate', 'complex']
          }
        ],
        conditionals: [],
        variables: [],
        transformations: []
      },
      examples: []
    })

    this.templates.set('api_development', {
      id: 'api_development',
      name: 'API Development Project',
      description: 'REST/GraphQL API development breakdown',
      category: 'development',
      applicability: {
        languages: ['javascript', 'typescript', 'python', 'java', 'go'],
        frameworks: ['express', 'fastapi', 'spring', 'gin'],
        projectTypes: ['rest_api', 'graphql_api', 'microservices'],
        complexity: ['simple', 'moderate', 'complex'],
        team_size: ['1-3', '4-8', '9+'],
        timeline: ['weeks', 'months']
      },
      structure: {
        phases: ['planning', 'design', 'development', 'testing', 'deployment'],
        taskTypes: ['setup', 'endpoint', 'middleware', 'testing', 'documentation'],
        dependencies: ['linear', 'parallel'],
        deliverables: ['api', 'tests', 'documentation', 'deployment'],
        milestones: ['mvp', 'beta', 'release'],
        resources: ['backend_developers', 'qa', 'devops']
      },
      customization: {
        parameters: [
          {
            name: 'api_type',
            type: 'string',
            description: 'Type of API to develop',
            required: true,
            options: ['rest', 'graphql', 'grpc']
          }
        ],
        conditionals: [],
        variables: [],
        transformations: []
      },
      examples: []
    })
  }

  async generateTaskBreakdown(
    input: string,
    context: GenerationContext
  ): Promise<TaskBreakdown> {
    try {
      // Analyze input and context
      const analysis = await this.analyzeInput(input, context)
      
      // Select appropriate template
      const template = this.selectTemplate(analysis)
      
      // Generate base structure
      const baseStructure = await this.generateBaseStructure(analysis, template)
      
      // Enhance with AI predictions
      const enhancedStructure = await this.enhanceWithAI(baseStructure, context)
      
      // Add dependencies and timeline
      const withDependencies = this.addDependencies(enhancedStructure)
      const withTimeline = this.addTimeline(withDependencies)
      
      // Validate and refine
      const validated = await this.validateAndRefine(withTimeline)
      
      // Create final breakdown
      const breakdown = this.createTaskBreakdown(validated, context)
      
      // Store in history
      this.generationHistory.push(breakdown)
      
      return breakdown
    } catch (error) {
      console.error('Error generating task breakdown:', error)
      throw error
    }
  }

  private async analyzeInput(input: string, context: GenerationContext): Promise<any> {
    // Use context analyzer to understand the input
    const analysis = await this.contextAnalyzer.analyzeText(input)
    
    // Use pattern recognizer to identify common patterns
    const patterns = await this.patternRecognizer.identifyPatterns(input)
    
    // Use semantic search to find similar past tasks
    const similarTasks = await this.semanticSearch.search(input, {
      limit: 5,
      threshold: 0.7
    })
    
    return {
      intent: analysis.intent,
      entities: analysis.entities,
      complexity: analysis.complexity,
      patterns: patterns.map(p => p.name),
      similarTasks: similarTasks.map(t => t.id),
      requirements: this.extractRequirements(input),
      constraints: this.extractConstraints(input, context),
      success_criteria: this.extractSuccessCriteria(input)
    }
  }

  private selectTemplate(analysis: any): TaskTemplate {
    // Score templates based on applicability
    let bestTemplate: TaskTemplate | null = null
    let bestScore = 0
    
    for (const template of this.templates.values()) {
      const score = this.scoreTemplate(template, analysis)
      if (score > bestScore) {
        bestScore = score
        bestTemplate = template
      }
    }
    
    return bestTemplate || this.getDefaultTemplate()
  }

  private scoreTemplate(template: TaskTemplate, analysis: any): number {
    let score = 0
    
    // Check language compatibility
    if (template.applicability.languages.includes(analysis.language)) {
      score += 0.3
    }
    
    // Check framework compatibility
    if (template.applicability.frameworks.includes(analysis.framework)) {
      score += 0.3
    }
    
    // Check project type compatibility
    if (template.applicability.projectTypes.includes(analysis.projectType)) {
      score += 0.2
    }
    
    // Check complexity compatibility
    if (template.applicability.complexity.includes(analysis.complexity)) {
      score += 0.2
    }
    
    return score
  }

  private async generateBaseStructure(analysis: any, template: TaskTemplate): Promise<any> {
    const structure = {
      phases: this.generatePhases(analysis, template),
      tasks: this.generateTasks(analysis, template),
      resources: this.generateResources(analysis, template),
      milestones: this.generateMilestones(analysis, template)
    }
    
    return structure
  }

  private generatePhases(analysis: any, template: TaskTemplate): TaskPhase[] {
    const phases: TaskPhase[] = []
    
    for (const phaseName of template.structure.phases) {
      const phase: TaskPhase = {
        id: `phase_${phaseName}`,
        name: phaseName.charAt(0).toUpperCase() + phaseName.slice(1),
        description: `${phaseName} phase of the project`,
        startDate: new Date(),
        endDate: new Date(),
        tasks: [],
        deliverables: [],
        criticalPath: false
      }
      
      phases.push(phase)
    }
    
    return phases
  }

  private generateTasks(analysis: any, template: TaskTemplate): Task[] {
    const tasks: Task[] = []
    
    // Generate tasks based on requirements
    for (const requirement of analysis.requirements) {
      const task = this.createTaskFromRequirement(requirement, template)
      tasks.push(task)
    }
    
    // Add standard tasks from template
    for (const taskType of template.structure.taskTypes) {
      const task = this.createStandardTask(taskType, template, analysis)
      tasks.push(task)
    }
    
    return tasks
  }

  private generateResources(analysis: any, template: TaskTemplate): TaskResource[] {
    const resources: TaskResource[] = []
    
    for (const resourceName of template.structure.resources) {
      const resource: TaskResource = {
        type: this.getResourceType(resourceName),
        name: resourceName,
        description: `${resourceName} resource for the project`,
        availability: {
          status: 'available',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          utilization: 0,
          constraints: []
        },
        requirements: [],
        cost: {
          type: 'variable',
          amount: 0,
          currency: 'USD',
          period: 'monthly',
          breakdown: []
        },
        alternatives: []
      }
      
      resources.push(resource)
    }
    
    return resources
  }

  private generateMilestones(analysis: any, template: TaskTemplate): TaskMilestone[] {
    const milestones: TaskMilestone[] = []
    
    for (const milestoneName of template.structure.milestones) {
      const milestone: TaskMilestone = {
        id: `milestone_${milestoneName}`,
        title: milestoneName.charAt(0).toUpperCase() + milestoneName.slice(1),
        description: `${milestoneName} milestone`,
        date: new Date(),
        type: 'deliverable',
        criteria: [],
        dependencies: []
      }
      
      milestones.push(milestone)
    }
    
    return milestones
  }

  private async enhanceWithAI(baseStructure: any, context: GenerationContext): Promise<any> {
    // Use learning engine to predict optimal task structure
    const prediction = await this.learningEngine.predict('workflow_optimization', {
      structure: baseStructure,
      context: context,
      requirements: baseStructure.requirements
    })
    
    // Apply AI suggestions
    if (prediction.confidence > 0.7) {
      return this.applyAISuggestions(baseStructure, prediction.result)
    }
    
    return baseStructure
  }

  private addDependencies(structure: any): any {
    // Analyze task dependencies
    const tasksWithDependencies = structure.tasks.map((task: Task) => {
      const dependencies = this.calculateTaskDependencies(task, structure.tasks)
      return { ...task, dependencies }
    })
    
    return { ...structure, tasks: tasksWithDependencies }
  }

  private addTimeline(structure: any): any {
    // Calculate timeline based on dependencies and estimates
    const timeline = this.calculateTimeline(structure.tasks, structure.milestones)
    
    return { ...structure, timeline }
  }

  private async validateAndRefine(structure: any): Promise<any> {
    // Validate structure for completeness and consistency
    const validation = this.validateStructure(structure)
    
    if (!validation.valid) {
      // Apply fixes
      return this.refineStructure(structure, validation.issues)
    }
    
    return structure
  }

  private createTaskBreakdown(structure: any, context: GenerationContext): TaskBreakdown {
    const breakdown: TaskBreakdown = {
      id: `breakdown_${Date.now()}`,
      title: this.generateTitle(structure, context),
      description: this.generateDescription(structure, context),
      complexity: this.calculateOverallComplexity(structure),
      priority: this.calculateOverallPriority(structure),
      tasks: structure.tasks,
      dependencies: this.extractDependencies(structure),
      timeline: structure.timeline,
      resources: structure.resources,
      metadata: {
        created: new Date(),
        createdBy: 'system',
        lastModified: new Date(),
        modifiedBy: 'system',
        version: '1.0.0',
        source: 'task_generator',
        confidence: 0.8,
        template: structure.templateId || 'default',
        customFields: {}
      }
    }
    
    return breakdown
  }

  async generateMarkdown(breakdown: TaskBreakdown): Promise<string> {
    const document: MarkdownDocument = {
      title: breakdown.title,
      content: this.createMarkdownSections(breakdown),
      metadata: {
        author: 'Task Generator',
        created: breakdown.metadata.created,
        modified: breakdown.metadata.lastModified,
        version: breakdown.metadata.version,
        tags: ['task-breakdown', 'generated'],
        language: 'en',
        encoding: 'utf-8',
        lineEnding: '\n'
      },
      styles: {
        theme: 'default',
        headingStyle: 'atx',
        listStyle: 'dash',
        codeStyle: 'fenced',
        linkStyle: 'inline',
        tableStyle: 'pipe',
        emphasisStyle: 'asterisk'
      }
    }
    
    return this.markdownFormatter.formatDocument(document)
  }

  private createMarkdownSections(breakdown: TaskBreakdown): MarkdownSection[] {
    const sections: MarkdownSection[] = []
    
    // Overview section
    sections.push({
      id: 'overview',
      type: {
        category: 'paragraph',
        variant: 'overview',
        formatting: { indentation: 0, spacing: { before: 0, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
      },
      title: 'Project Overview',
      content: breakdown.description,
      level: 2,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'task_generator',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    })
    
    // Complexity and Priority section
    sections.push({
      id: 'complexity',
      type: {
        category: 'table',
        variant: 'info',
        formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
      },
      title: 'Project Metrics',
      content: JSON.stringify({
        headers: ['Metric', 'Value', 'Description'],
        rows: [
          ['Complexity', breakdown.complexity.overall, 'Overall project complexity'],
          ['Priority', breakdown.priority.level, 'Project priority level'],
          ['Estimated Duration', `${breakdown.timeline.duration} days`, 'Total estimated duration'],
          ['Number of Tasks', breakdown.tasks.length.toString(), 'Total number of tasks']
        ],
        alignment: ['left', 'center', 'left'],
        formatting: {
          headerStyle: 'bold',
          cellPadding: 1,
          borderStyle: 'minimal',
          zebra: false
        }
      }),
      level: 2,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'task_generator',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    })
    
    // Timeline section
    if (breakdown.timeline.milestones.length > 0) {
      sections.push({
        id: 'timeline',
        type: {
          category: 'list',
          variant: 'timeline',
          formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
        },
        title: 'Project Timeline',
        content: JSON.stringify({
          items: breakdown.timeline.milestones.map(milestone => ({
            content: `**${milestone.title}** - ${milestone.date.toDateString()}: ${milestone.description}`,
            level: 0,
            sublists: []
          })),
          ordered: false,
          startNumber: 1,
          style: 'dash',
          indent: 0
        }),
        level: 2,
        subsections: [],
        elements: [],
        metadata: {
          generated: true,
          source: 'task_generator',
          confidence: 1.0,
          editCount: 0,
          lastModified: new Date(),
          dependencies: []
        }
      })
    }
    
    // Tasks section
    const taskListItems = breakdown.tasks.map(task => ({
      title: task.title,
      completed: task.status.current === 'completed',
      priority: task.priority.level,
      estimate: `${task.estimatedTime}h`,
      description: task.description,
      subtasks: task.subtasks.map(subtask => ({
        title: subtask.title,
        completed: subtask.completed
      })),
      notes: task.notes.length > 0 ? task.notes.join('; ') : undefined
    }))
    
    sections.push({
      id: 'tasks',
      type: {
        category: 'task_breakdown',
        variant: 'detailed',
        formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
      },
      title: 'Task Breakdown',
      content: JSON.stringify(taskListItems),
      level: 2,
      subsections: [],
      elements: [],
      metadata: {
        generated: true,
        source: 'task_generator',
        confidence: 1.0,
        editCount: 0,
        lastModified: new Date(),
        dependencies: []
      }
    })
    
    // Resources section
    if (breakdown.resources.length > 0) {
      sections.push({
        id: 'resources',
        type: {
          category: 'table',
          variant: 'resources',
          formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
        },
        title: 'Required Resources',
        content: JSON.stringify({
          headers: ['Resource', 'Type', 'Availability', 'Requirements'],
          rows: breakdown.resources.map(resource => [
            resource.name,
            resource.type,
            resource.availability.status,
            resource.requirements.map(r => r.skill).join(', ')
          ]),
          alignment: ['left', 'left', 'center', 'left'],
          formatting: {
            headerStyle: 'bold',
            cellPadding: 1,
            borderStyle: 'minimal',
            zebra: true
          }
        }),
        level: 2,
        subsections: [],
        elements: [],
        metadata: {
          generated: true,
          source: 'task_generator',
          confidence: 1.0,
          editCount: 0,
          lastModified: new Date(),
          dependencies: []
        }
      })
    }
    
    // Dependencies section
    if (breakdown.dependencies.length > 0) {
      sections.push({
        id: 'dependencies',
        type: {
          category: 'list',
          variant: 'dependencies',
          formatting: { indentation: 0, spacing: { before: 1, after: 1, internal: 0 }, alignment: 'left', emphasis: { bold: false, italic: false, underline: false, strikethrough: false, highlight: false }, borders: { top: false, bottom: false, left: false, right: false, style: 'solid' } }
        },
        title: 'Task Dependencies',
        content: JSON.stringify({
          items: breakdown.dependencies.map(dep => ({
            content: `${dep.fromTask} → ${dep.toTask} (${dep.type})`,
            level: 0,
            sublists: []
          })),
          ordered: false,
          startNumber: 1,
          style: 'dash',
          indent: 0
        }),
        level: 2,
        subsections: [],
        elements: [],
        metadata: {
          generated: true,
          source: 'task_generator',
          confidence: 1.0,
          editCount: 0,
          lastModified: new Date(),
          dependencies: []
        }
      })
    }
    
    return sections
  }

  // Helper methods
  private extractRequirements(input: string): string[] {
    const requirements: string[] = []
    
    // Use regex patterns to extract requirements
    const patterns = [
      /(?:need|require|must|should|want)\s+(?:to\s+)?([^.!?]+)/gi,
      /(?:implement|create|build|develop|add)\s+([^.!?]+)/gi,
      /(?:feature|functionality|capability)\s*:\s*([^.!?]+)/gi
    ]
    
    for (const pattern of patterns) {
      const matches = input.match(pattern)
      if (matches) {
        requirements.push(...matches.map(match => match.trim()))
      }
    }
    
    return requirements
  }

  private extractConstraints(input: string, context: GenerationContext): string[] {
    const constraints: string[] = []
    
    // Extract explicit constraints
    const constraintPatterns = [
      /(?:within|by|before|deadline|due)\s+([^.!?]+)/gi,
      /(?:budget|cost|resource)\s*:\s*([^.!?]+)/gi,
      /(?:limitation|constraint|restriction)\s*:\s*([^.!?]+)/gi
    ]
    
    for (const pattern of constraintPatterns) {
      const matches = input.match(pattern)
      if (matches) {
        constraints.push(...matches.map(match => match.trim()))
      }
    }
    
    // Add context constraints
    constraints.push(...context.constraints)
    
    return constraints
  }

  private extractSuccessCriteria(input: string): string[] {
    const criteria: string[] = []
    
    const criteriaPatterns = [
      /(?:success|successful|complete|done)\s+(?:when|if)\s+([^.!?]+)/gi,
      /(?:criteria|requirement|condition)\s*:\s*([^.!?]+)/gi,
      /(?:achieve|accomplish|deliver)\s+([^.!?]+)/gi
    ]
    
    for (const pattern of criteriaPatterns) {
      const matches = input.match(pattern)
      if (matches) {
        criteria.push(...matches.map(match => match.trim()))
      }
    }
    
    return criteria
  }

  private getDefaultTemplate(): TaskTemplate {
    return {
      id: 'default',
      name: 'Default Template',
      description: 'Generic task breakdown template',
      category: 'general',
      applicability: {
        languages: [],
        frameworks: [],
        projectTypes: [],
        complexity: ['simple', 'moderate', 'complex'],
        team_size: ['1-3', '4-8', '9+'],
        timeline: ['days', 'weeks', 'months']
      },
      structure: {
        phases: ['planning', 'implementation', 'testing', 'deployment'],
        taskTypes: ['setup', 'development', 'testing', 'documentation'],
        dependencies: ['linear'],
        deliverables: ['code', 'documentation'],
        milestones: ['alpha', 'beta', 'release'],
        resources: ['developer', 'tester']
      },
      customization: {
        parameters: [],
        conditionals: [],
        variables: [],
        transformations: []
      },
      examples: []
    }
  }

  private createTaskFromRequirement(requirement: string, template: TaskTemplate): Task {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    return {
      id: taskId,
      title: requirement,
      description: `Task to address: ${requirement}`,
      type: {
        category: 'development',
        subcategory: 'feature',
        methodology: 'agile',
        tools: [],
        skillLevel: 'intermediate'
      },
      status: {
        current: 'not_started',
        progress: 0,
        lastUpdated: new Date(),
        blockers: [],
        milestones: []
      },
      priority: {
        level: 'medium',
        urgency: 'medium',
        importance: 'medium',
        businessValue: 0.5,
        technicalRisk: 0.5,
        dependencies: 0
      },
      complexity: {
        overall: 'moderate',
        technical: 0.5,
        business: 0.5,
        integration: 0.5,
        testing: 0.5,
        documentation: 0.5,
        factors: []
      },
      estimatedTime: 8,
      dependencies: [],
      subtasks: [],
      checklist: [],
      notes: [],
      tags: [],
      context: {
        project: '',
        module: '',
        feature: '',
        codebase: {
          repository: '',
          branch: '',
          languages: [],
          frameworks: [],
          architecture: '',
          patterns: [],
          conventions: [],
          coverage: 0
        },
        environment: {
          development: { name: 'dev', status: 'active', configuration: {}, dependencies: [] },
          testing: { name: 'test', status: 'active', configuration: {}, dependencies: [] },
          staging: { name: 'staging', status: 'active', configuration: {}, dependencies: [] },
          production: { name: 'prod', status: 'active', configuration: {}, dependencies: [] }
        },
        stakeholders: [],
        constraints: [],
        assumptions: []
      },
      validation: {
        criteria: [],
        methods: [],
        acceptance: [],
        testCases: [],
        reviewers: []
      },
      deliverables: []
    }
  }

  private createStandardTask(taskType: string, template: TaskTemplate, analysis: any): Task {
    const taskId = `task_${taskType}_${Date.now()}`
    
    return {
      id: taskId,
      title: `${taskType.charAt(0).toUpperCase() + taskType.slice(1)} Task`,
      description: `Standard ${taskType} task for the project`,
      type: {
        category: 'development',
        subcategory: taskType,
        methodology: 'agile',
        tools: [],
        skillLevel: 'intermediate'
      },
      status: {
        current: 'not_started',
        progress: 0,
        lastUpdated: new Date(),
        blockers: [],
        milestones: []
      },
      priority: {
        level: 'medium',
        urgency: 'medium',
        importance: 'medium',
        businessValue: 0.5,
        technicalRisk: 0.5,
        dependencies: 0
      },
      complexity: {
        overall: 'moderate',
        technical: 0.5,
        business: 0.5,
        integration: 0.5,
        testing: 0.5,
        documentation: 0.5,
        factors: []
      },
      estimatedTime: 8,
      dependencies: [],
      subtasks: [],
      checklist: [],
      notes: [],
      tags: [taskType],
      context: {
        project: '',
        module: '',
        feature: '',
        codebase: {
          repository: '',
          branch: '',
          languages: [],
          frameworks: [],
          architecture: '',
          patterns: [],
          conventions: [],
          coverage: 0
        },
        environment: {
          development: { name: 'dev', status: 'active', configuration: {}, dependencies: [] },
          testing: { name: 'test', status: 'active', configuration: {}, dependencies: [] },
          staging: { name: 'staging', status: 'active', configuration: {}, dependencies: [] },
          production: { name: 'prod', status: 'active', configuration: {}, dependencies: [] }
        },
        stakeholders: [],
        constraints: [],
        assumptions: []
      },
      validation: {
        criteria: [],
        methods: [],
        acceptance: [],
        testCases: [],
        reviewers: []
      },
      deliverables: []
    }
  }

  private getResourceType(resourceName: string): TaskResource['type'] {
    if (resourceName.includes('developer') || resourceName.includes('engineer')) {
      return 'human'
    } else if (resourceName.includes('tool') || resourceName.includes('software')) {
      return 'tool'
    } else if (resourceName.includes('service') || resourceName.includes('api')) {
      return 'service'
    } else if (resourceName.includes('server') || resourceName.includes('infrastructure')) {
      return 'infrastructure'
    } else {
      return 'knowledge'
    }
  }

  private applyAISuggestions(structure: any, suggestions: any): any {
    // Apply AI-generated suggestions to the structure
    return { ...structure, ...suggestions }
  }

  private calculateTaskDependencies(task: Task, allTasks: Task[]): string[] {
    const dependencies: string[] = []
    
    // Simple dependency detection based on task types and content
    for (const otherTask of allTasks) {
      if (otherTask.id !== task.id) {
        if (this.hasImplicitDependency(task, otherTask)) {
          dependencies.push(otherTask.id)
        }
      }
    }
    
    return dependencies
  }

  private hasImplicitDependency(task: Task, otherTask: Task): boolean {
    // Check if task depends on otherTask
    const setupTypes = ['setup', 'infrastructure', 'configuration']
    const developmentTypes = ['development', 'feature', 'implementation']
    const testingTypes = ['testing', 'qa', 'validation']
    
    if (setupTypes.includes(otherTask.type.subcategory) && 
        developmentTypes.includes(task.type.subcategory)) {
      return true
    }
    
    if (developmentTypes.includes(otherTask.type.subcategory) && 
        testingTypes.includes(task.type.subcategory)) {
      return true
    }
    
    return false
  }

  private calculateTimeline(tasks: Task[], milestones: TaskMilestone[]): TaskTimeline {
    const totalDuration = tasks.reduce((sum, task) => sum + task.estimatedTime, 0) / 8 // Convert hours to days
    const startDate = new Date()
    const endDate = new Date(startDate.getTime() + totalDuration * 24 * 60 * 60 * 1000)
    
    return {
      startDate,
      endDate,
      duration: totalDuration,
      criticalPath: this.calculateCriticalPath(tasks),
      milestones,
      phases: [],
      buffer: Math.ceil(totalDuration * 0.2) // 20% buffer
    }
  }

  private calculateCriticalPath(tasks: Task[]): string[] {
    // Simple critical path calculation
    const sortedTasks = tasks.sort((a, b) => b.estimatedTime - a.estimatedTime)
    return sortedTasks.slice(0, Math.ceil(tasks.length * 0.3)).map(task => task.id)
  }

  private validateStructure(structure: any): { valid: boolean; issues: string[] } {
    const issues: string[] = []
    
    if (!structure.tasks || structure.tasks.length === 0) {
      issues.push('No tasks defined')
    }
    
    if (!structure.timeline) {
      issues.push('No timeline defined')
    }
    
    if (!structure.resources || structure.resources.length === 0) {
      issues.push('No resources defined')
    }
    
    return {
      valid: issues.length === 0,
      issues
    }
  }

  private refineStructure(structure: any, issues: string[]): any {
    // Apply fixes for common issues
    let refined = { ...structure }
    
    for (const issue of issues) {
      if (issue === 'No tasks defined') {
        refined.tasks = [this.createStandardTask('setup', this.getDefaultTemplate(), {})]
      } else if (issue === 'No timeline defined') {
        refined.timeline = this.calculateTimeline(refined.tasks, [])
      } else if (issue === 'No resources defined') {
        refined.resources = [
          {
            type: 'human',
            name: 'Developer',
            description: 'Primary developer resource',
            availability: { status: 'available', startDate: new Date(), endDate: new Date(), utilization: 0, constraints: [] },
            requirements: [],
            cost: { type: 'variable', amount: 0, currency: 'USD', period: 'monthly', breakdown: [] },
            alternatives: []
          }
        ]
      }
    }
    
    return refined
  }

  private generateTitle(structure: any, context: GenerationContext): string {
    if (context.projectType) {
      return `${context.projectType.charAt(0).toUpperCase() + context.projectType.slice(1)} Project Task Breakdown`
    }
    
    return 'Project Task Breakdown'
  }

  private generateDescription(structure: any, context: GenerationContext): string {
    const parts = []
    
    if (context.input) {
      parts.push(`This project breakdown is based on the following requirements: ${context.input}`)
    }
    
    if (structure.tasks && structure.tasks.length > 0) {
      parts.push(`The project consists of ${structure.tasks.length} main tasks.`)
    }
    
    if (structure.timeline) {
      parts.push(`The estimated duration is ${structure.timeline.duration} days.`)
    }
    
    return parts.join(' ')
  }

  private calculateOverallComplexity(structure: any): TaskComplexity {
    const taskComplexities = structure.tasks.map((task: Task) => task.complexity.technical)
    const avgComplexity = taskComplexities.reduce((sum: number, c: number) => sum + c, 0) / taskComplexities.length
    
    let overall: TaskComplexity['overall']
    if (avgComplexity < 0.3) overall = 'simple'
    else if (avgComplexity < 0.6) overall = 'moderate'
    else if (avgComplexity < 0.8) overall = 'complex'
    else overall = 'very_complex'
    
    return {
      overall,
      technical: avgComplexity,
      business: avgComplexity,
      integration: avgComplexity,
      testing: avgComplexity,
      documentation: avgComplexity,
      factors: []
    }
  }

  private calculateOverallPriority(structure: any): TaskPriority {
    const taskPriorities = structure.tasks.map((task: Task) => task.priority.businessValue)
    const avgPriority = taskPriorities.reduce((sum: number, p: number) => sum + p, 0) / taskPriorities.length
    
    let level: TaskPriority['level']
    if (avgPriority < 0.3) level = 'low'
    else if (avgPriority < 0.6) level = 'medium'
    else if (avgPriority < 0.8) level = 'high'
    else level = 'critical'
    
    return {
      level,
      urgency: level,
      importance: level,
      businessValue: avgPriority,
      technicalRisk: avgPriority,
      dependencies: structure.dependencies.length
    }
  }

  private extractDependencies(structure: any): TaskDependency[] {
    const dependencies: TaskDependency[] = []
    
    for (const task of structure.tasks) {
      for (const depId of task.dependencies) {
        dependencies.push({
          fromTask: depId,
          toTask: task.id,
          type: 'finish_to_start',
          relationship: 'required',
          lag: 0,
          description: `${depId} must be completed before ${task.id}`
        })
      }
    }
    
    return dependencies
  }

  // Public API methods
  getTemplates(): TaskTemplate[] {
    return Array.from(this.templates.values())
  }

  getGenerationHistory(): TaskBreakdown[] {
    return [...this.generationHistory]
  }

  async exportToFile(breakdown: TaskBreakdown, filePath: string): Promise<void> {
    const markdown = await this.generateMarkdown(breakdown)
    await vscode.workspace.fs.writeFile(vscode.Uri.file(filePath), Buffer.from(markdown, 'utf8'))
  }

  async exportToClipboard(breakdown: TaskBreakdown): Promise<void> {
    const markdown = await this.generateMarkdown(breakdown)
    await vscode.env.clipboard.writeText(markdown)
  }

  addTemplate(template: TaskTemplate): void {
    this.templates.set(template.id, template)
  }

  removeTemplate(templateId: string): void {
    this.templates.delete(templateId)
  }

  clearHistory(): void {
    this.generationHistory = []
  }
}